#!/usr/bin/env python3
"""
OPRO系统运行脚本 - 简化版本 (OPRO System Runner - Simplified Version)

使用重构后的服务架构运行多智能体交易系统，支持两种运行模式：

主要特点:
- 使用ServiceFactory和RefactoredContributionAssessor
- 采用高内聚、低耦合的服务架构
- 支持配置驱动的服务创建和管理
- 简化为两种核心模式

支持的运行模式:
1. evaluation: 普通交易模式，不进行OPRO优化，持续进行交易评估
2. weekly: 周期性优化模式，使用新的"周 > 阶段"架构自动优化

使用示例:
    # 默认运行（周期性优化模式 + 完整跟踪）
    python run_opro_system_new.py --provider zhipuai

    # 普通交易模式（不优化，只交易，但保持跟踪）
    python run_opro_system_new.py --provider zhipuai --mode evaluation --disable-opro

    # 快速测试模式（使用虚拟LLM，无需API密钥）
    python run_opro_system_new.py --provider mock --mode evaluation --quick-test

    # 自定义持久化路径
    python run_opro_system_new.py --provider zhipuai --tracker-persistence-path data/my_portfolio.json

    # 手动设置继承状态
    python run_opro_system_new.py --provider zhipuai --inherited-state-path data/previous_week.json

    # 禁用投资组合跟踪器
    python run_opro_system_new.py --provider zhipuai --disable-portfolio-tracking

    # 禁用状态继承
    python run_opro_system_new.py --provider zhipuai --disable-state-inheritance

    # 完全禁用跟踪功能（回到旧行为）
    python run_opro_system_new.py --provider zhipuai --disable-portfolio-tracking --disable-state-inheritance
    
"""

import argparse
import json
import logging
import os
import sys
import time
from datetime import datetime
from typing import Dict, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

# 导入核心模块 - 使用重构后的服务架构
from contribution_assessment.service_factory import ServiceFactory
from contribution_assessment.refactored_assessor import RefactoredContributionAssessor
from contribution_assessment.llm_interface import LLMInterface
from utils.logging_config import setup_clean_logging

def setup_logging(verbose: bool = False, log_file: Optional[str] = None) -> logging.Logger:
    """设置日志记录（使用统一的日志配置工具）"""
    return setup_clean_logging(verbose=verbose, log_file=log_file, logger_name=__name__)

def load_config(config_path: str = "config/opro_config.json") -> Dict[str, Any]:
    """加载配置文件"""
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"配置文件不存在: {config_path}")
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
            
        # 清理A/B测试相关配置
        if "ab_testing" in config_data:
            print("警告: 检测到A/B测试配置，该功能已移除，配置将被忽略", file=sys.stderr)
            del config_data["ab_testing"]
            
        return config_data
        
    except Exception as e:
        raise RuntimeError(f"加载配置失败: {e}")

def create_system_config(args) -> Dict[str, Any]:
    """创建系统配置"""
    config = {
        "start_date": args.start_date,
        "end_date": args.end_date,
        "stocks": [args.symbol.upper()],  # 使用命令行参数指定的股票代码
        "symbol": args.symbol.upper(),  # 添加单独的symbol字段
        "starting_cash": 1000000,
        "simulation_days": args.simulation_days,
        "verbose": args.verbose,
        "enable_concurrent_execution": not args.disable_concurrent,
        "max_concurrent_api_calls": 60,  # 设置最大并发数为60
        "max_concurrent_simulations": 60,  # 设置最大并发模拟数为60
        "fail_on_large_gaps": False,
        "fill_date_gaps": True,
        "trading_days_per_week": 5  # 统一设置：每5个交易日为一个交易周
    }
    
    # 投资组合跟踪器和状态继承配置（默认启用）
    config["enable_portfolio_tracking"] = getattr(args, 'enable_portfolio_tracking', True)
    config["enable_state_inheritance"] = getattr(args, 'enable_state_inheritance', True)
    
    # 如果都启用了，设置相关配置
    if config["enable_portfolio_tracking"] or config["enable_state_inheritance"]:
        
        # 设置持久化路径
        if hasattr(args, 'tracker_persistence_path') and args.tracker_persistence_path:
            config["tracker_persistence_path"] = args.tracker_persistence_path
        else:
            # 默认持久化路径
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            config["tracker_persistence_path"] = f"data/portfolio_tracker_{timestamp}.json"
        
        # 确保data目录存在
        data_dir = os.path.dirname(config["tracker_persistence_path"])
        if data_dir and not os.path.exists(data_dir):
            os.makedirs(data_dir, exist_ok=True)
        
        # 如果启用了跟踪功能，创建portfolio_tracker实例
        if config["enable_portfolio_tracking"]:
            from portfolio_state_tracker import PortfolioStateTracker
            config["portfolio_tracker"] = PortfolioStateTracker(
                persistence_path=config["tracker_persistence_path"]
            )
        
        # 如果启用了状态继承，设置inherited_portfolio_state
        # 这通常在跨周运行时由系统自动设置，这里提供配置接口
        if config["enable_state_inheritance"] and hasattr(args, 'inherited_state_path') and args.inherited_state_path:
            try:
                import json
                with open(args.inherited_state_path, 'r') as f:
                    config["inherited_portfolio_state"] = json.load(f)
            except Exception as e:
                print(f"警告: 无法加载继承状态文件 {args.inherited_state_path}: {e}", file=sys.stderr)
    
    # 保持向后兼容性的日常跟踪配置
    config["enable_daily_tracking"] = config.get("enable_portfolio_tracking", True)

    # 添加日内并行执行
    config["enable_parallel_execution"] = True
    config["max_concurrent_agents"] = 3
    config["parallel_timeout"] = 180.0  # 3分钟超时    

    return config

def create_refactored_assessor(system_config: Dict[str, Any], 
                             opro_config: Dict[str, Any],
                             args: argparse.Namespace,
                             logger: logging.Logger) -> RefactoredContributionAssessor:
    """创建重构版本的评估器"""
    try:
        # 方式1: 使用ServiceFactory.create_assessor_with_config（推荐）
        logger.info("🔧 使用ServiceFactory创建重构版本的评估器...")
        
        # 合并配置
        merged_config = system_config.copy()
        merged_config.update(opro_config)
        
        # 创建评估器
        assessor = ServiceFactory.create_assessor_with_config(
            config=merged_config,
            agents=None,  # 将由assessor内部创建
            logger=logger,
            llm_provider=args.provider,
            enable_opro=args.enable_opro
        )
        
        logger.info("✅ 重构版本的评估器创建成功")
        return assessor
        
    except Exception as e:
        logger.error(f"❌ 重构版本评估器创建失败: {e}")
        raise Exception(f"评估器创建失败: {e}")

def run_evaluation_mode(assessor: RefactoredContributionAssessor, args: argparse.Namespace, logger: logging.Logger) -> Dict[str, Any]:
    """运行评估模式 - 普通交易模式，不进行OPRO优化"""
    logger.info("🚀 运行模式: 普通交易（无优化）")
    
    # 如果OPRO被启用但用户选择了evaluation模式，给出提示
    if assessor.enable_opro:
        logger.warning("⚠️  注意: OPRO功能已启用，但evaluation模式不会进行优化")
        logger.info("💡 如需优化功能，请使用 --mode weekly")
    
    # 设置默认的目标智能体列表（包含所有7个智能体）
    default_agents = ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"]
    target_agents = args.agents.split(',') if args.agents else default_agents
    logger.info(f"目标智能体: {target_agents}")
    
    if args.quick_test:
        logger.info("运行快速测试...")
        # 检查是否有quick_test方法
        if hasattr(assessor, 'run_quick_test'):
            result = assessor.run_quick_test()  # type: ignore
        else:
            logger.warning("重构版本暂不支持quick_test，使用标准run方法")
            result = assessor.run(
                target_agents=target_agents,
                max_coalitions=10  # 快速测试用较小的联盟数量
            )
    else:
        logger.info("运行完整评估...")
        result = assessor.run(
            target_agents=target_agents,
            max_coalitions=args.max_coalitions
        )
    
    if not isinstance(result, dict) or not result.get("success", False):
        error_msg = result.get("error", "评估模式执行失败") if isinstance(result, dict) else "评估模式返回结果格式错误"
        raise RuntimeError(f"评估模式失败: {error_msg}")
    
    return {
        "mode": "evaluation", 
        "result": result,
        "success": True,
        "architecture": "refactored"
    }

def run_weekly_optimization_mode(assessor: RefactoredContributionAssessor, args: argparse.Namespace, logger: logging.Logger) -> Dict[str, Any]:
    """运行周期性优化模式 - 使用新架构的WeeklyCycleManager"""
    logger.info("🔄 运行模式: 周期性在线优化（新架构WeeklyCycleManager）")
    logger.info("📋 架构特点: 周 > 阶段 的层级结构")
    
    if not assessor.enable_opro:
        raise RuntimeError("OPRO功能未启用，无法运行周期性优化模式")
    
    # 创建周期性优化配置
    weekly_config = {
        "optimization_frequency": args.optimization_frequency,
        "min_days_for_optimization": args.min_days_for_optimization,
        "max_agents_per_cycle": args.max_agents_per_cycle,
        "verbose_logging": args.verbose,
        "continue_on_failure": True  # 增加容错性
    }
    
    logger.info(f"📋 周期性优化配置:")
    logger.info(f"   优化频率: 每 {weekly_config['optimization_frequency']} 交易日")
    logger.info(f"   最少运行天数: {weekly_config['min_days_for_optimization']} 天")
    logger.info(f"   每周期最多优化: {weekly_config['max_agents_per_cycle']} 个智能体")
    logger.info(f"   容错模式: {'启用' if weekly_config['continue_on_failure'] else '禁用'}")
    
    # 严格使用WeeklyCycleManager架构，不允许回退
    if not hasattr(assessor, 'run_with_weekly_cycle_manager'):
        raise RuntimeError("assessor不支持run_with_weekly_cycle_manager方法，无法执行周期性优化")
    
    # 设置默认的目标智能体列表（包含所有7个智能体）
    default_agents = ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"]
    target_agents = args.agents.split(',') if args.agents else default_agents
    logger.info(f"目标智能体: {target_agents}")
    
    logger.info("🚀 使用WeeklyCycleManager架构（周 > 阶段）")
    result = assessor.run_with_weekly_cycle_manager(  # type: ignore
        weekly_config=weekly_config,
        target_agents=target_agents,
        max_coalitions=args.max_coalitions
    )
    
    # 检查架构是否正确执行
    if result.get("architecture") != "weekly_cycle_manager":
        raise RuntimeError(f"WeeklyCycleManager架构执行失败，返回架构: {result.get('architecture')}")
    
    # 检查执行是否成功
    if not result.get("success", False):
        error_msg = result.get("error", "周期性优化模式执行失败")
        raise RuntimeError(f"周期性优化失败: {error_msg}")
    
    logger.info("✅ WeeklyCycleManager架构执行成功")
    return {
        "mode": "weekly_optimization",
        "result": result,
        "success": True,
        "weekly_config": weekly_config,
        "architecture": "weekly_cycle_manager"
    }

def _extract_shapley_values_from_result(assessment_result) -> Dict[str, float]:
    """从评估结果中提取Shapley值"""
    if hasattr(assessment_result, 'shapley_calculation_results'):
        shapley_data = assessment_result.shapley_calculation_results
        if isinstance(shapley_data, dict) and 'shapley_values' in shapley_data:
            return shapley_data['shapley_values']
        elif hasattr(shapley_data, 'shapley_values'):
            return getattr(shapley_data, 'shapley_values')
    
    if hasattr(assessment_result, 'phase_results'):
        phase_results = assessment_result.phase_results
        if isinstance(phase_results, dict):
            shapley_phase = phase_results.get('shapley_calculation', {})
            if isinstance(shapley_phase, dict) and 'shapley_values' in shapley_phase:
                return shapley_phase['shapley_values']
    
    raise ValueError("无法从评估结果中提取Shapley值：未找到shapley_values数据")

def _extract_opro_result_from_result(assessment_result) -> Dict[str, Any]:
    """从评估结果中提取OPRO结果"""
    if hasattr(assessment_result, 'opro_optimization_results'):
        opro_results = assessment_result.opro_optimization_results
        if opro_results:
            return opro_results
    
    if hasattr(assessment_result, 'phase_results'):
        phase_results = assessment_result.phase_results
        if isinstance(phase_results, dict):
            opro_results = phase_results.get('opro_optimization', {})
            if opro_results:
                return opro_results
    
    raise ValueError("无法从评估结果中提取OPRO结果：未找到opro_optimization数据")

def export_results(result: Dict[str, Any], output_path: str, logger: logging.Logger):
    """导出结果到文件"""
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    
    logger.info(f"结果已导出至: {output_path}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="OPRO系统运行脚本（重构版本）")
    
    # 基础参数
    parser.add_argument("--provider", type=str, default="zhipuai",
                       choices=["zhipuai", "openai", "mock", "lmstudio"], help="LLM提供商 (mock用于快速测试)")
    parser.add_argument("--mode", type=str, default="weekly",
                       choices=["evaluation", "weekly"],
                       help="运行模式: evaluation(普通交易,不优化) 或 weekly(周期性优化)")
    parser.add_argument("--enable-opro", action="store_true", default=True, help="启用OPRO功能")
    parser.add_argument("--disable-opro", action="store_true",default=False, help="禁用OPRO功能")
    parser.add_argument("--verbose", action="store_true", help="详细日志")
    
    # 系统配置
    parser.add_argument("--symbol", type=str, default="AAPL", help="股票代码")
    parser.add_argument("--start-date", type=str, default="2025-01-01", help="开始日期")
    parser.add_argument("--end-date", type=str, default="2025-04-30", help="结束日期")
    parser.add_argument("--simulation-days", type=int, help="模拟天数")
    parser.add_argument("--agents", type=str, help="目标智能体列表（逗号分隔）")
    parser.add_argument("--max-coalitions", type=int, help="最大联盟数量")
    parser.add_argument("--disable-concurrent", action="store_true", help="禁用并发执行")
    
    # 测试选项
    parser.add_argument("--quick-test", action="store_true", help="运行快速测试")
    
    # 周期性优化选项
    parser.add_argument("--weekly-optimization", action="store_true", help="启用周期性优化（每周优化）")
    parser.add_argument("--optimization-frequency", type=int, default=7, help="优化频率（天）")
    parser.add_argument("--min-days-for-optimization", type=int, default=5, help="最少运行天数才开始优化")
    parser.add_argument("--max-agents-per-cycle", type=int, default=1, help="每个周期最多优化的智能体数量")
    
    # 输出选项
    parser.add_argument("--output", type=str, help="结果输出文件")
    parser.add_argument("--log-file", type=str, help="日志文件")
    
    # 配置文件
    parser.add_argument("--config", type=str, default="config/opro_config.json", 
                       help="配置文件路径")
    
    # 投资组合跟踪选项（默认启用）
    parser.add_argument("--disable-portfolio-tracking", action="store_true", 
                       help="禁用投资组合跟踪器（默认启用）")
    parser.add_argument("--disable-state-inheritance", action="store_true", 
                       help="禁用跨周状态继承（默认启用）")
    parser.add_argument("--tracker-persistence-path", type=str, 
                       help="投资组合跟踪数据持久化路径（默认自动生成）")
    parser.add_argument("--inherited-state-path", type=str, 
                       help="继承状态文件路径（用于手动设置继承状态）")
    
    # 保持向后兼容性
    parser.add_argument("--disable-daily-tracking", action="store_true", 
                       help="禁用日常投资组合跟踪（已废弃，请使用--disable-portfolio-tracking）")
    
    args = parser.parse_args()
    
    # 处理OPRO开关逻辑
    if args.disable_opro:
        args.enable_opro = False
    
    # 处理投资组合跟踪参数逻辑
    if args.disable_portfolio_tracking:
        args.enable_portfolio_tracking = False
    else:
        args.enable_portfolio_tracking = True
    
    if args.disable_state_inheritance:
        args.enable_state_inheritance = False
    else:
        args.enable_state_inheritance = True
    
    # 向后兼容性：如果使用了旧的--disable-daily-tracking参数
    if hasattr(args, 'disable_daily_tracking') and args.disable_daily_tracking:
        args.enable_portfolio_tracking = False
        print("警告: --disable-daily-tracking已废弃，已自动映射到--disable-portfolio-tracking", file=sys.stderr)
    
    # 处理已废弃的参数和模式
    if hasattr(args, 'max_iterations') and args.max_iterations:
        print("警告: --max-iterations参数已废弃", file=sys.stderr)
    
    # 检查废弃的模式
    deprecated_modes = ["optimization", "integrated", "dashboard", "ab-testing"]
    if args.mode in deprecated_modes:
        print(f"错误: {args.mode}模式已移除，请使用支持的运行模式：", file=sys.stderr)
        print("  evaluation   - 普通交易模式（不优化）", file=sys.stderr)
        print("  weekly       - 周期性优化模式", file=sys.stderr)
        sys.exit(1)
    
    # 设置日志
    log_file = args.log_file or f"opro_system_new_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    logger = setup_logging(args.verbose, log_file)
    
    logger.info("=" * 100)
    logger.info("🚀 OPRO系统启动（重构版本）")
    logger.info("=" * 100)
    logger.info(f"运行模式: {args.mode}")
    logger.info(f"LLM提供商: {args.provider}")
    logger.info(f"系统架构: 重构版本（Service-Based Architecture）")
    if args.provider == "mock":
        logger.info("🎭 使用虚拟LLM提供商 - 适用于快速测试，无需API密钥")
    logger.info(f"OPRO启用: {args.enable_opro}")
    
    # 显示投资组合跟踪状态
    portfolio_tracking_enabled = args.enable_portfolio_tracking
    state_inheritance_enabled = args.enable_state_inheritance
    
    if portfolio_tracking_enabled:
        logger.info("📊 投资组合跟踪器: 启用（记录每日投资组合状态）")
        if hasattr(args, 'tracker_persistence_path') and args.tracker_persistence_path:
            logger.info(f"💾 持久化路径: {args.tracker_persistence_path}")
        else:
            logger.info("💾 持久化路径: 自动生成")
    else:
        logger.info("📊 投资组合跟踪器: 禁用")
    
    if state_inheritance_enabled:
        logger.info("🔄 跨周状态继承: 启用（支持周间状态传递）")
        if hasattr(args, 'inherited_state_path') and args.inherited_state_path:
            logger.info(f"📂 继承状态文件: {args.inherited_state_path}")
    else:
        logger.info("🔄 跨周状态继承: 禁用")
    
    # 综合状态提示
    if portfolio_tracking_enabled and state_inheritance_enabled:
        logger.info("✅ 完整模式: 跟踪器=True, 继承状态=True")
    elif portfolio_tracking_enabled and not state_inheritance_enabled:
        logger.info("📊 跟踪模式: 跟踪器=True, 继承状态=False")
    elif not portfolio_tracking_enabled and state_inheritance_enabled:
        logger.info("🔄 继承模式: 跟踪器=False, 继承状态=True")
    else:
        logger.info("🆕 全新模式: 跟踪器=False, 继承状态=False")
    
    if args.weekly_optimization or args.mode == "weekly":
        logger.info(f"周期性优化启用: 每 {args.optimization_frequency} 天")
    
    try:
        # 加载配置
        config_data = load_config(args.config)
        
        # 创建系统配置
        system_config = create_system_config(args)
        
        # 初始化系统
        logger.info("初始化重构版本系统...")
        
        # 创建LLM接口（用于验证提供商）
        LLMInterface(provider=args.provider, logger=logger)
        
        # 创建OPRO配置
        opro_config = {}
        if config_data:
            # 合并所有相关的配置部分
            opro_config.update(config_data.get("optimization", {}))
            opro_config.update(config_data.get("evaluation", {}))
            opro_config.update(config_data.get("storage", {}))
        
        # 创建重构版本的评估器
        assessor = create_refactored_assessor(
            system_config=system_config,
            opro_config=opro_config,
            args=args,
            logger=logger
        )
        
        logger.info("✅ 重构版本系统初始化完成")
        
        # 检查是否启用周期性优化（只在非optimization模式下自动切换）
        if args.weekly_optimization and args.mode != "optimization":
            logger.info("🔄 检测到周期性优化模式，自动启用每周优化")
            args.mode = "weekly"
        elif args.mode == "weekly":
            logger.info("🔄 使用周期性优化模式")

        # 根据模式运行
        if args.mode == "evaluation":
            result = run_evaluation_mode(assessor, args, logger)
        elif args.mode == "weekly":
            result = run_weekly_optimization_mode(assessor, args, logger)
        else:
            raise ValueError(f"未知运行模式: {args.mode}，支持的模式: evaluation, weekly")
        
        # 添加执行信息
        result.update({
            "execution_info": {
                "timestamp": datetime.now().isoformat(),
                "mode": args.mode,
                "opro_enabled": args.enable_opro,
                "llm_provider": args.provider,
                "config_file": args.config,
                "architecture": "refactored",
                "version": "重构版本 v1.0.0"
            }
        })
        
        # 输出结果
        success = result.get("success", False)
        if success:
            logger.info("🎉 执行成功!（重构版本）")
        else:
            logger.error("❌ 执行失败!（重构版本）")
            if "error" in result:
                logger.error(f"错误: {result['error']}")
        
        # 导出结果
        if args.output:
            export_results(result, args.output, logger)
        
        # 返回适当的退出代码
        sys.exit(0 if success else 1)
        
    except Exception as e:
        logger.error(f"系统执行异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()