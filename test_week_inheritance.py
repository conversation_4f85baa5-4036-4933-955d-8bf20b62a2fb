#!/usr/bin/env python3
"""
测试跨周状态继承的完整流程
验证第1周完整联盟记录能正确传递给第2周
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from portfolio_state_tracker import PortfolioStateTracker


def test_cross_week_inheritance_with_coalition_filtering():
    """测试带联盟过滤的跨周继承完整流程"""
    print("=== 测试跨周继承完整流程 ===")
    
    # 第1步：创建tracker并模拟第1周
    tracker = PortfolioStateTracker()
    tracker.start_new_week(1)
    print(f"第1周开始...")
    
    # 第2步：模拟完整联盟运行（这些记录应该被保存）
    complete_coalition_info = {
        "coalition_id": "frozenset({'TRA', 'NOA', 'NAA', 'BeOA', 'FAA', 'BOA', 'TAA'})",
        "coalition_size": 7
    }
    
    print(f"第1周完整联盟模拟...")
    tracker.add_daily_record("2025-01-01", 10100, 0.01, 0.01, 0.01, 0.0, complete_coalition_info)
    tracker.add_daily_record("2025-01-02", 10300, 0.02, 0.03, 0.03, 0.0, complete_coalition_info)
    tracker.add_daily_record("2025-01-03", 10400, 0.01, 0.04, 0.04, 0.0, complete_coalition_info)
    tracker.add_daily_record("2025-01-04", 10500, 0.01, 0.05, 0.05, 0.0, complete_coalition_info)
    tracker.add_daily_record("2025-01-05", 10610, 0.01, 0.061, 0.061, 0.0, complete_coalition_info)  # 第1周结束: 周收益率6.1%
    
    # 第3步：模拟子集联盟运行（这些记录应该被跳过）
    subset_coalition_info = {
        "coalition_id": "frozenset({'TRA', 'NAA', 'BeOA', 'BOA', 'TAA'})",
        "coalition_size": 5
    }
    
    print(f"第1周子集联盟模拟（应被跳过）...")
    tracker.add_daily_record("2025-01-01", 9900, -0.01, -0.01, -0.01, 0.0, subset_coalition_info)
    tracker.add_daily_record("2025-01-02", 9800, -0.01, -0.02, -0.02, 0.0, subset_coalition_info)
    tracker.add_daily_record("2025-01-03", 9750, -0.005, -0.025, -0.025, 0.0, subset_coalition_info)
    tracker.add_daily_record("2025-01-04", 9700, -0.005, -0.030, -0.030, 0.0, subset_coalition_info)
    tracker.add_daily_record("2025-01-05", 9650, -0.005, -0.035, -0.035, 0.0, subset_coalition_info)  # 子集: 周收益率-3.5%
    
    # 第4步：检查第1周结束后的状态
    print(f"\n第1周结束后状态检查:")
    tracker.debug_tracker_state()
    
    week1_latest = tracker.get_latest_state()
    print(f"第1周最新状态: 累计收益率={week1_latest['cumulative_return']:.4f}, 周收益率={week1_latest['weekly_return']:.4f}")
    
    # 第5步：模拟第2周开始，调用get_clean_state_for_new_phase
    print(f"\n第2周开始，获取干净状态...")
    tracker.start_new_week(2)
    clean_state_week2 = tracker.get_clean_state_for_new_phase(2)
    
    if clean_state_week2:
        print(f"第2周干净状态:")
        print(f"  累计收益率: {clean_state_week2['cumulative_return']:.4f} (应为0.061)")
        print(f"  当前周收益率: {clean_state_week2['weekly_return']:.4f} (应重置为0.0)")
        print(f"  上周收益率: {clean_state_week2['last_week_return']:.4f} (应为0.061)")
        
        # 验证结果
        expected_cumulative = 0.061
        expected_weekly = 0.0
        expected_last_week = 0.061
        
        cumulative_ok = abs(clean_state_week2['cumulative_return'] - expected_cumulative) < 0.0001
        weekly_ok = clean_state_week2['weekly_return'] == expected_weekly
        last_week_ok = abs(clean_state_week2['last_week_return'] - expected_last_week) < 0.0001
        
        if cumulative_ok and weekly_ok and last_week_ok:
            print("✅ 跨周继承测试通过! 完整联盟过滤和状态继承都正常工作。")
            return True
        else:
            print(f"❌ 跨周继承测试失败:")
            if not cumulative_ok:
                print(f"   累计收益率错误: 期望{expected_cumulative:.4f}, 实际{clean_state_week2['cumulative_return']:.4f}")
            if not weekly_ok:
                print(f"   当前周收益率错误: 期望{expected_weekly:.4f}, 实际{clean_state_week2['weekly_return']:.4f}")
            if not last_week_ok:
                print(f"   上周收益率错误: 期望{expected_last_week:.4f}, 实际{clean_state_week2['last_week_return']:.4f}")
            return False
    else:
        print("❌ 无法获取第2周干净状态")
        return False


def main():
    """运行测试"""
    print("开始测试跨周状态继承完整流程...\n")
    
    success = test_cross_week_inheritance_with_coalition_filtering()
    
    if success:
        print("\n🎉 跨周继承完整测试通过!")
        print("✅ 完整联盟过滤正常工作")
        print("✅ 第1周完整联盟记录正确保存")
        print("✅ 第2周正确继承第1周完整联盟的状态")
    else:
        print("\n⚠️ 跨周继承测试失败，需要检查实现")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)