# A/B测试功能依赖关系分析报告

## 分析时间
2025-07-15

## 核心文件依赖

### 1. 主要A/B测试文件
- `contribution_assessment/ab_testing_manager.py` - 核心A/B测试管理器（需完全删除）

### 2. 主运行脚本依赖
**文件：** `run_opro_system.py`
- `run_ab_testing_mode()` 函数 (第363行)
- `--mode` 参数中的 "ab-testing" 选项 (第418行)
- `--max-iterations` 参数定义 (第449行)
- 主函数中的ab-testing模式分支 (第529行)
- 文档字符串中的A/B测试使用示例 (第30-31行)

### 3. 评估器集成依赖
**文件：** `contribution_assessment/assessor.py`
- ABTestingManager导入 (第31行，重复导入)
- ab_testing_manager实例变量 (第224行，重复初始化)
- `run_continuous_ab_optimization()` 方法 (第3757行)
- `_get_original_agents_for_ab_test()` 方法 (第4204行)
- `_should_continue_optimization()` 方法 (第3895行)
- `_print_continuous_optimization_summary()` 方法 (第3926行)
- A/B测试统计信息获取 (第2055行)
- 周期性优化中的A/B测试调用 (第3718行)

### 4. 配置文件依赖
**文件：** `config/opro_config.json`
- `ab_testing` 配置段 (第32-38行)
- `enable_ab_testing` 选项在optimization段 (第167行)

## 耦合关系分析

### 高耦合关系
1. **ABTestingManager与ContributionAssessor**
   - 直接实例化和方法调用
   - 统计信息集成
   - 周期性优化流程集成

2. **run_opro_system.py与A/B测试模式**
   - 命令行参数解析
   - 模式分发逻辑
   - 错误处理

### 中等耦合关系
1. **配置系统**
   - JSON配置文件中的A/B测试段
   - 运行时配置传递

2. **文档和示例**
   - 使用示例中的A/B测试命令
   - 帮助文档

### 低耦合关系
1. **日志和监控**
   - A/B测试相关的日志输出
   - 统计信息收集

## 安全移除边界

### 可以安全删除的组件
- `ab_testing_manager.py` 整个文件
- `run_ab_testing_mode()` 函数
- A/B测试相关的配置项
- A/B测试相关的命令行参数

### 需要谨慎处理的组件
- `assessor.py` 中的集成代码（需要保留其他功能）
- 周期性优化流程（需要移除A/B测试部分但保留核心功能）
- 配置文件（需要保留其他配置项）

### 不能删除的组件
- 核心的OPRO优化功能
- 其他运行模式（evaluation, optimization, weekly, integrated, dashboard）
- 基础的智能体管理和Shapley值计算

## 风险评估

### 低风险操作
- 删除独立的ab_testing_manager.py文件
- 移除命令行参数中的ab-testing选项
- 清理配置文件中的A/B测试段

### 中等风险操作
- 修改assessor.py中的集成代码
- 处理周期性优化中的A/B测试调用

### 高风险操作
- 无（所有A/B测试功能都是独立的，不影响核心功能）

## 回滚计划
如果需要回滚，需要恢复以下文件：
1. `contribution_assessment/ab_testing_manager.py`
2. `run_opro_system.py` 的A/B测试相关部分
3. `contribution_assessment/assessor.py` 的A/B测试集成部分
4. `config/opro_config.json` 的A/B测试配置段