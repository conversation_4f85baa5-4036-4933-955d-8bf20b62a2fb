# 设计文档

## 概述

本设计文档描述了一个基于5个交易日为周期的四阶段优化系统的架构和实现方案。该系统将自动化执行多智能体交易系统的持续优化流程，通过周期性的四阶段循环来不断改进系统性能。

系统采用模块化设计，充分复用现有的contribution_assessment模块，并引入新的周期管理和调度组件来实现自动化的周期性执行。

## 架构

### 系统架构图

```mermaid
graph TB
    subgraph "五天周期优化系统"
        CM[周期管理器<br/>CycleManager]
        PS[阶段调度器<br/>PhaseScheduler]
        SM[状态管理器<br/>StateManager]
        
        subgraph "四个执行阶段"
            P1[阶段1: 完整集合交易<br/>FullSetTrading]
            P2[阶段2: 子集计算与Shapley<br/>SubsetCalculation]
            P3[阶段3: 识别最差Agent<br/>WorstAgentIdentification]
            P4[阶段4: OPRO优化<br/>PromptOptimization]
        end
        
        subgraph "现有组件集成"
            CA[ContributionAssessor]
            CS[CoalitionService]
            SS[ShapleyService]
            OS[OPROService]
            TS[TradingSimulator]
        end
        
        subgraph "数据存储"
            CD[周期数据<br/>CycleData]
            PR[阶段结果<br/>PhaseResults]
            PM[性能指标<br/>PerformanceMetrics]
        end
    end
    
    CM --> PS
    PS --> P1
    P1 --> P2
    P2 --> P3
    P3 --> P4
    P4 --> PS
    
    PS --> SM
    SM --> CD
    
    P1 --> CA
    P2 --> CS
    P2 --> SS
    P3 --> SS
    P4 --> OS
    
    CA --> TS
    CS --> PR
    SS --> PR
    OS --> PR
    
    PR --> PM
```

### 核心组件设计

#### 1. 周期管理器 (CycleManager)

负责管理5天交易日周期的识别、启动和结束。

**主要职责：**
- 交易日历管理和周期计算
- 周期状态跟踪和持久化
- 周期间数据传递和清理
- 异常恢复和状态同步

**关键方法：**
- `start_new_cycle()`: 启动新的5天周期
- `is_cycle_complete()`: 检查当前周期是否完成
- `get_current_cycle_info()`: 获取当前周期信息
- `handle_cycle_transition()`: 处理周期转换

#### 2. 阶段调度器 (PhaseScheduler)

负责四个阶段的顺序执行和状态管理。

**主要职责：**
- 阶段执行顺序控制
- 阶段间数据传递
- 错误处理和重试机制
- 执行进度监控

**关键方法：**
- `execute_phase()`: 执行指定阶段
- `transition_to_next_phase()`: 转换到下一阶段
- `handle_phase_failure()`: 处理阶段执行失败
- `get_phase_status()`: 获取阶段执行状态

#### 3. 状态管理器 (StateManager)

负责系统状态的持久化和恢复。

**主要职责：**
- 系统状态持久化
- 异常恢复支持
- 状态查询和监控
- 历史数据管理

**关键方法：**
- `save_state()`: 保存当前状态
- `load_state()`: 加载历史状态
- `get_system_status()`: 获取系统状态
- `cleanup_old_data()`: 清理历史数据

## 组件和接口

### 主要组件

#### FiveDayCycleOptimizer

系统的主入口点，协调所有组件的工作。

```python
class FiveDayCycleOptimizer:
    def __init__(self, config: Dict[str, Any], logger: logging.Logger)
    def start_optimization_loop(self) -> None
    def stop_optimization_loop(self) -> None
    def get_system_status(self) -> Dict[str, Any]
    def export_cycle_data(self, output_path: str) -> bool
```

#### CycleManager

管理5天交易日周期。

```python
class CycleManager:
    def __init__(self, trading_calendar: ITradingCalendar, config: Dict[str, Any])
    def start_new_cycle(self) -> CycleInfo
    def is_cycle_complete(self) -> bool
    def get_current_cycle_info(self) -> CycleInfo
    def get_cycle_trading_days(self, cycle_number: int) -> List[str]
```

#### PhaseScheduler

调度四个执行阶段。

```python
class PhaseScheduler:
    def __init__(self, assessor: ContributionAssessor, config: Dict[str, Any])
    def execute_phase_one(self, cycle_info: CycleInfo) -> PhaseResult
    def execute_phase_two(self, cycle_info: CycleInfo) -> PhaseResult
    def execute_phase_three(self, cycle_info: CycleInfo) -> PhaseResult
    def execute_phase_four(self, cycle_info: CycleInfo) -> PhaseResult
```

### 数据模型

#### CycleInfo

```python
@dataclass
class CycleInfo:
    cycle_number: int
    start_date: str
    end_date: str
    trading_days: List[str]
    status: CycleStatus
    created_at: datetime
    updated_at: datetime
```

#### PhaseResult

```python
@dataclass
class PhaseResult:
    phase_number: int
    phase_name: str
    cycle_number: int
    success: bool
    start_time: datetime
    end_time: datetime
    data: Dict[str, Any]
    error_message: Optional[str] = None
```

#### SystemState

```python
@dataclass
class SystemState:
    current_cycle: Optional[CycleInfo]
    current_phase: int
    is_running: bool
    last_update: datetime
    total_cycles_completed: int
    performance_metrics: Dict[str, Any]
```

## 数据模型

### 周期数据结构

系统使用分层的数据结构来组织周期性数据：

1. **周期级别数据** - 每个5天周期的整体信息
2. **阶段级别数据** - 每个阶段的执行结果
3. **智能体级别数据** - 每个智能体的性能指标
4. **历史趋势数据** - 跨周期的性能趋势

### 数据持久化策略

- **实时状态** - 存储在内存中，定期持久化到磁盘
- **阶段结果** - 每个阶段完成后立即持久化
- **周期汇总** - 每个周期结束后生成汇总报告
- **历史数据** - 定期归档，支持数据导出和分析

## 错误处理

### 错误分类

1. **配置错误** - 配置文件缺失或参数无效
2. **网络错误** - LLM API调用失败或网络中断
3. **数据错误** - 交易数据缺失或格式错误
4. **计算错误** - Shapley值计算失败或数值异常
5. **系统错误** - 内存不足或磁盘空间不足

### 错误处理策略

- **重试机制** - 对临时性错误进行指数退避重试
- **降级服务** - 在资源不足时提供基础功能
- **状态恢复** - 从持久化状态恢复系统运行
- **告警通知** - 对严重错误进行日志记录和通知
- **优雅停机** - 在无法恢复的错误时安全停止系统

### 容错设计

- **阶段隔离** - 单个阶段失败不影响其他阶段
- **数据备份** - 关键数据多重备份
- **状态检查点** - 定期保存系统状态
- **自动恢复** - 系统重启后自动恢复到上次状态

## 测试策略

### 单元测试

- **组件测试** - 每个核心组件的独立测试
- **接口测试** - 组件间接口的契约测试
- **数据模型测试** - 数据结构的序列化和验证测试
- **错误处理测试** - 异常情况的处理逻辑测试

### 集成测试

- **阶段集成测试** - 四个阶段的端到端测试
- **周期集成测试** - 完整5天周期的集成测试
- **外部依赖测试** - 与LLM API和数据源的集成测试
- **性能测试** - 系统负载和性能基准测试

### 系统测试

- **长期运行测试** - 多周期连续运行测试
- **故障恢复测试** - 各种故障场景的恢复测试
- **配置测试** - 不同配置组合的兼容性测试
- **用户验收测试** - 端用户场景的验收测试

### 测试数据管理

- **模拟数据** - 用于单元测试的模拟交易数据
- **历史数据** - 用于集成测试的真实历史数据
- **测试环境** - 独立的测试环境和数据库
- **数据清理** - 测试后的数据清理和重置机制