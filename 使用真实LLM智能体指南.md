# 使用真实LLM智能体运行贡献度评估系统指南

## 问题背景

在之前的系统运行中，您可能遇到了系统使用模拟智能体而非真实LLM智能体的问题。这个问题的根本原因是：

1. **LLM接口未正确初始化**：系统需要明确的LLM提供商参数才能初始化LLM接口
2. **智能体实例缺失**：系统需要实际的智能体实例，否则会回退到模拟数据
3. **环境配置不完整**：缺少必要的API密钥或SDK安装

## 解决方案概述

我们提供了三个新工具来解决这个问题：

1. **`check_llm_setup.py`** - 环境配置检查工具
2. **`run_with_real_agents.py`** - 使用真实智能体的运行脚本
3. **修改后的 `run_contribution_assessment.py`** - 增强的主运行脚本

## 步骤1：环境配置

### 1.1 安装必要的SDK

根据您选择的LLM提供商安装相应的SDK：

```bash
# 如果使用智谱AI
pip install zhipuai

# 如果使用OpenAI
pip install openai
```

### 1.2 设置环境变量

创建 `.env` 文件或设置环境变量：

```bash
# 智谱AI
export ZHIPUAI_API_KEY=your_zhipuai_api_key

# 或者 OpenAI
export OPENAI_API_KEY=your_openai_api_key
```

### 1.3 验证环境配置

运行环境检查工具：

```bash
# 检查智谱AI配置
python check_llm_setup.py --provider zhipuai

# 检查OpenAI配置
python check_llm_setup.py --provider openai
```

这个工具会检查：
- ✅ 环境变量是否设置
- ✅ SDK是否安装
- ✅ LLM接口是否可用
- ✅ 智能体是否能正确创建
- ✅ 评估器是否配置正确

## 步骤2：运行真实智能体评估

### 2.1 使用专用脚本（推荐）

```bash
# 使用智谱AI运行
python run_with_real_agents.py --llm-provider zhipuai

# 使用OpenAI运行
python run_with_real_agents.py --llm-provider openai

# 显示详细日志
python run_with_real_agents.py --llm-provider zhipuai --verbose
```

### 2.2 使用增强的主脚本

```bash
# 使用智谱AI运行完整评估
python run_contribution_assessment.py --llm-provider zhipuai

# 使用智谱AI运行快速测试
python run_contribution_assessment.py --llm-provider zhipuai --quick-test

# 指定特定智能体
python run_contribution_assessment.py --llm-provider zhipuai --agents NAA TAA FAA
```

## 步骤3：验证结果

### 3.1 检查日志输出

正确运行时，您应该看到：

```
🤖 正在创建LLM智能体实例 (提供商: zhipuai)...
✅ LLM接口初始化成功
✅ 成功创建智能体: NAA
✅ 成功创建智能体: TAA
✅ 成功创建智能体: FAA
...
✅ 成功创建 7 个智能体实例
```

而不是：
```
⚠️  未提供分析智能体实例，将使用模拟数据
```

### 3.2 检查评估结果

在评估结果中，您应该看到：
```
✅ 成功使用真实LLM智能体!
```

而不是：
```
⚠️  警告: 系统仍在使用模拟数据!
```

## 关键技术要点

### 为什么之前使用模拟智能体？

1. **ContributionAssessor初始化**：
   ```python
   # 错误的方式 - 没有传入智能体实例
   assessor = ContributionAssessor(config=config, llm_provider="zhipuai")
   
   # 正确的方式 - 传入智能体实例
   agent_instances = create_agent_instances("zhipuai")
   assessor = ContributionAssessor(
       config=config, 
       agents=agent_instances,  # 关键！
       llm_provider="zhipuai"
   )
   ```

2. **运行评估时**：
   ```python
   # 错误的方式 - 没有传入智能体实例
   result = assessor.run()
   
   # 正确的方式 - 传入智能体实例
   result = assessor.run(agents=agent_instances)  # 关键！
   ```

### 系统的回退逻辑

系统设计了智能的回退机制：

1. 如果 `llm_interface` 不存在或 `client` 不可用 → 使用模拟数据
2. 如果 `agents` 参数为 `None` 或为空 → 使用模拟数据
3. 如果智能体实例不包含所需的分析智能体 → 使用模拟数据

这确保了系统在任何情况下都能运行，但只有正确配置时才会使用真实LLM。

## 故障排除

### 问题1：仍然显示使用模拟数据

**可能原因**：
- LLM接口初始化失败
- 智能体实例未正确传入
- API密钥无效或网络问题

**解决方法**：
1. 运行 `python check_llm_setup.py --provider your_provider`
2. 检查API密钥是否正确
3. 确认网络连接正常

### 问题2：智能体创建失败

**可能原因**：
- SDK未安装或版本不兼容
- 导入路径错误

**解决方法**：
1. 重新安装SDK：`pip install --upgrade zhipuai`
2. 检查项目结构和导入路径

### 问题3：API调用失败

**可能原因**：
- API配额不足
- 模型名称错误
- 网络连接问题

**解决方法**：
1. 检查API配额和余额
2. 确认模型名称正确（如 "glm-4-flash"）
3. 检查网络连接和防火墙设置

## 最佳实践

1. **始终先运行环境检查**：在正式运行前使用 `check_llm_setup.py`
2. **使用专用脚本进行测试**：`run_with_real_agents.py` 专门为真实智能体设计
3. **启用详细日志**：使用 `--verbose` 参数查看详细执行过程
4. **从小规模开始**：先测试少量智能体和短期数据
5. **监控API使用**：注意API调用次数和成本

## 总结

通过以上步骤，您现在可以：

1. ✅ 正确配置LLM环境
2. ✅ 创建真实的智能体实例
3. ✅ 运行使用真实LLM的贡献度评估
4. ✅ 验证系统确实在使用真实智能体而非模拟数据

这确保了您的Shapley值计算基于真实的LLM智能体分析，而不是预设的模拟数据，从而获得更准确和有意义的贡献度评估结果。
