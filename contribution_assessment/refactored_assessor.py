"""
重构版本的ContributionAssessor

这是ContributionAssessor的重构版本，采用模块化架构和依赖注入模式。
主要改进：
- 使用PhaseCoordinator协调执行流程
- 通过服务层分离关注点
- 支持配置驱动的行为
- 更好的错误处理和日志记录
- 完全模块化，无回退逻辑
- 低耦合高聚合
"""

import time
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
import pandas as pd

# 导入服务层组件
from .services.phase_coordinator import PhaseCoordinator
from .infrastructure.configuration_manager import ConfigurationManager
from .infrastructure.error_handler import ErrorHandler
from .dto.assessment_dto import AssessmentRequest, AssessmentResult
# 导入顶层ServiceFactory
from .service_factory import ServiceFactory as TopLevelServiceFactory


class RefactoredContributionAssessor:
    """
    重构版本的贡献度评估器
    
    采用模块化架构，通过PhaseCoordinator协调各个服务的执行。
    完全使用新架构组件，无任何回退逻辑。
    """
    
    def __init__(self, 
                 config: Dict[str, Any],
                 agents: Optional[Dict[str, Any]] = None,
                 logger: Optional[logging.Logger] = None,
                 llm_provider: Optional[Any] = None,
                 enable_opro: bool = False,
                 opro_config: Optional[Dict[str, Any]] = None):
        """
        初始化重构版本的评估器
        
        Args:
            config: 配置字典
            agents: 智能体配置
            logger: 日志记录器
            llm_provider: LLM提供者
            enable_opro: 是否启用OPRO优化
            opro_config: OPRO配置
        """
        self.logger = logger or logging.getLogger(__name__)
        self.config = config
        self.agents = agents or {}
        self.llm_provider = llm_provider
        self.enable_opro = enable_opro
        self.opro_config = opro_config or {}
        
        # 统计信息
        self.stats = {
            "total_runs": 0,
            "successful_runs": 0,
            "failed_runs": 0,
            "total_execution_time": 0.0,
            "last_run_time": None
        }
        
        # 初始化组件
        self._initialize_components()

    def _initialize_components(self):
        """初始化核心组件"""
        try:
            self.logger.info("初始化RefactoredContributionAssessor组件...")
            
            # 创建配置好的ServiceFactory实例
            factory = TopLevelServiceFactory()
            factory.configure_services(self.config)
            
            # 创建PhaseCoordinator
            self.phase_coordinator = factory.create_phase_coordinator(
                logger=self.logger,
                enable_opro=self.enable_opro
            )
            
            # 初始化配置管理器和错误处理器
            self.config_manager = ConfigurationManager(config_dict=self.config)
            self.error_handler = ErrorHandler(logger=self.logger)
            
            self.logger.info("✅ RefactoredContributionAssessor初始化完成")
            
        except Exception as e:
            self.logger.error(f"RefactoredContributionAssessor初始化失败: {e}")
            raise RuntimeError(f"无法初始化RefactoredContributionAssessor: {e}")

    def run(self, 
            agents: Optional[Dict[str, Any]] = None,
            target_agents: Optional[List[str]] = None,
            max_coalitions: Optional[int] = None) -> Dict[str, Any]:
        """
        运行贡献度评估
        
        Args:
            agents: 智能体字典
            target_agents: 目标智能体列表
            max_coalitions: 最大联盟数
            
        Returns:
            评估结果字典
        """
        start_time = time.time()
        self.stats["total_runs"] += 1
        
        try:
            self.logger.info("开始运行评估流程（新架构）")
            
            # 准备智能体
            agents_to_use = agents or self.agents or self._create_agents()
            if not agents_to_use:
                raise ValueError("未能创建或获取智能体")
            
            # 构建评估请求
            request = AssessmentRequest(
                agents=agents_to_use,
                target_agents=target_agents,
                max_coalitions=max_coalitions,
                config=self.config,
                enable_opro=self.enable_opro,
                opro_config=self.opro_config
            )
            
            # 通过PhaseCoordinator执行评估工作流
            result = self.phase_coordinator.execute_assessment_workflow(request)
            
            # 更新统计
            execution_time = time.time() - start_time
            self.stats["successful_runs"] += 1
            self.stats["total_execution_time"] += execution_time
            self.stats["last_run_time"] = datetime.now().isoformat()
            
            # 包装结果
            return self._wrap_result(result, execution_time)
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.stats["failed_runs"] += 1
            self.stats["total_execution_time"] += execution_time
            
            self.logger.error(f"评估流程执行失败: {e}")
            return self._create_error_result(str(e), execution_time)

    def run_quick_test(self,
                      agents: Optional[Dict[str, Any]] = None,
                      target_agents: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        运行快速测试
        
        Args:
            agents: 智能体字典
            target_agents: 目标智能体列表
            
        Returns:
            测试结果字典
        """
        self.logger.info("开始快速测试模式")
        
        # 使用较小的联盟数进行快速测试
        return self.run(
            agents=agents,
            target_agents=target_agents,
            max_coalitions=10
        )

    def _create_agents(self) -> Dict[str, Any]:
        """创建智能体"""
        try:
            self.logger.info("使用新架构创建智能体")
            
            # 创建配置好的ServiceFactory实例
            factory = TopLevelServiceFactory()
            factory.configure_services(self.config)
            
            # 通过ServiceFactory创建智能体
            agent_service = factory.create_agent_creation_service(
                logger=self.logger,
                llm_provider=self.llm_provider,
                enable_opro=self.enable_opro
            )
            
            agents = agent_service.create_default_agents()
            self.logger.info(f"✅ 新架构成功创建智能体: {list(agents.keys())}")
            return agents
            
        except Exception as e:
            self.logger.error(f"创建智能体失败: {e}")
            raise RuntimeError(f"无法创建智能体: {e}")

    def run_with_weekly_optimization(self,
                                   target_agents: Optional[List[str]] = None,
                                   max_coalitions: Optional[int] = None,
                                   weekly_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        运行周期性优化
        
        Args:
            target_agents: 目标智能体列表
            max_coalitions: 最大联盟数
            weekly_config: 周期配置
            
        Returns:
            优化结果字典
        """
        start_time = time.time()
        self.stats["total_runs"] += 1
        
        try:
            self.logger.info("开始周期性优化流程")
            
            agents = self.agents or self._create_agents()
            
            # 构建评估请求
            request = AssessmentRequest(
                agents=agents,
                target_agents=target_agents,
                max_coalitions=max_coalitions,
                config={**self.config, **(weekly_config or {})},
                enable_opro=self.enable_opro,
                opro_config=self.opro_config
            )
            
            # 执行周期性优化工作流
            result = self.phase_coordinator.execute_weekly_optimization_workflow(request)
            
            # 更新统计
            execution_time = time.time() - start_time
            self.stats["successful_runs"] += 1
            self.stats["total_execution_time"] += execution_time
            self.stats["last_run_time"] = datetime.now().isoformat()
            
            return self._wrap_result(result, execution_time)
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.stats["failed_runs"] += 1
            self.stats["total_execution_time"] += execution_time
            
            self.logger.error(f"周期性优化失败: {e}")
            return self._create_error_result(str(e), execution_time)

    def run_with_weekly_cycle_manager(self,
                                    weekly_config: Optional[Dict[str, Any]] = None,
                                    target_agents: Optional[List[str]] = None,
                                    max_coalitions: Optional[int] = None) -> Dict[str, Any]:
        """
        使用新的WeeklyCycleManager运行周期性优化
        
        这是新架构的入口点，实现"周 > 阶段"的层级结构
        
        Args:
            weekly_config: 周期配置
            target_agents: 目标智能体列表
            max_coalitions: 最大联盟数
            
        Returns:
            优化结果字典
        """
        start_time = time.time()
        self.stats["total_runs"] += 1
        
        # 根据日期范围自动计算周数
        try:
            start_date = self.config.get("start_date", "2025-01-01")
            end_date = self.config.get("end_date", "2025-02-15")
            trading_days_per_week = self.config.get("trading_days_per_week", 5)
            
            # 转换为pandas日期
            start_pd = pd.to_datetime(start_date)
            end_pd = pd.to_datetime(end_date)
            
            # 计算工作日数量（排除周末）
            business_days = len(pd.bdate_range(start_pd, end_pd))
            
            # 计算可以完整运行的周数
            auto_weeks = max(1, business_days // trading_days_per_week)
            
            self.logger.info(f"📅 日期范围: {start_date} 到 {end_date}")
            self.logger.info(f"📊 交易日总数: {business_days} 天")
            self.logger.info(f"🔄 自动计算周数: {auto_weeks} 周 (每周{trading_days_per_week}个交易日)")
            
        except Exception as e:
            self.logger.warning(f"自动计算周数失败，使用默认值1: {e}")
            auto_weeks = 1
        
        # 设置默认配置
        default_config = {
            "num_weeks": auto_weeks,  # 根据日期范围自动计算
            "agents_per_week": 2,
            "enable_phase_breakdown": True,
            "save_intermediate_results": True,
            "results_dir": "results/weekly_cycles"
        }
        
        # 合并配置
        final_config = {**default_config, **(weekly_config or {})}
        
        try:
            # 导入并创建WeeklyCycleManager
            from .weekly_cycle_manager import WeeklyCycleManager
            
            cycle_manager = WeeklyCycleManager(
                assessor=self,
                config=self.config,
                logger=self.logger
            )
            
            self.logger.info("✅ WeeklyCycleManager创建成功，开始执行周期性优化")
            
            # 执行周期性优化
            result = cycle_manager.run_weekly_cycles(
                num_weeks=final_config["num_weeks"],
                agents_per_week=final_config["agents_per_week"],
                target_agents=target_agents,
                max_coalitions=max_coalitions,
                weekly_config=final_config
            )
            
            # 更新统计
            execution_time = time.time() - start_time
            if result.get("success", False):
                self.stats["successful_runs"] += 1
            else:
                self.stats["failed_runs"] += 1
            
            self.stats["total_execution_time"] += execution_time
            self.stats["last_run_time"] = datetime.now().isoformat()
            
            # 添加架构标识
            result["architecture"] = "weekly_cycle_manager"
            result["refactored_assessor_stats"] = self.get_stats()
            
            return result
            
        except ImportError as e:
            self.logger.error(f"WeeklyCycleManager导入失败: {e}")
            raise ImportError(f"无法导入WeeklyCycleManager: {e}")
            
        except Exception as e:
            self.stats["failed_runs"] += 1
            execution_time = time.time() - start_time
            self.stats["total_execution_time"] += execution_time
            
            self.logger.error(f"WeeklyCycleManager执行失败: {e}")
            raise RuntimeError(f"WeeklyCycleManager执行失败: {e}")

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "total_runs": self.stats["total_runs"],
            "successful_runs": self.stats["successful_runs"],
            "failed_runs": self.stats["failed_runs"],
            "success_rate": (self.stats["successful_runs"] / max(1, self.stats["total_runs"])) * 100,
            "total_execution_time": self.stats["total_execution_time"],
            "average_execution_time": self.stats["total_execution_time"] / max(1, self.stats["total_runs"]),
            "last_run_time": self.stats["last_run_time"]
        }

    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 检查核心组件
            components_status = {
                "phase_coordinator": hasattr(self, 'phase_coordinator') and self.phase_coordinator is not None,
                "config_manager": hasattr(self, 'config_manager') and self.config_manager is not None,
                "error_handler": hasattr(self, 'error_handler') and self.error_handler is not None
            }
            
            all_healthy = all(components_status.values())
            
            return {
                "status": "healthy" if all_healthy else "unhealthy",
                "components": components_status,
                "stats": self.get_stats(),
                "architecture": "new_refactored"
            }
        except Exception as e:
            return {"status": "unhealthy", "error": str(e)}

    def _wrap_result(self, result: AssessmentResult, execution_time: float) -> Dict[str, Any]:
        """包装评估结果"""
        try:
            return {
                "success": True,
                "execution_time": execution_time,
                "timestamp": datetime.now().isoformat(),
                "architecture": "refactored",
                "coalition_results": getattr(result, 'coalition_result', None),
                "simulation_results": getattr(result, 'simulation_result', None),
                "shapley_values": getattr(result, 'shapley_result', None),
                "opro_results": getattr(result, 'opro_result', None),
                "summary": {
                    "target_agents": getattr(result, 'target_agents', []),
                    "total_coalitions": getattr(result, 'total_coalitions', 0),
                    "enable_opro": self.enable_opro
                },
                "stats": self.get_stats()
            }
        except Exception as e:
            self.logger.warning(f"结果包装失败，使用简化格式: {e}")
            return {
                "success": True,
                "execution_time": execution_time,
                "timestamp": datetime.now().isoformat(),
                "architecture": "refactored",
                "result": result,
                "stats": self.get_stats()
            }


    def _create_error_result(self, error_message: str, execution_time: float) -> Dict[str, Any]:
        """创建错误结果"""
        return {
            "success": False,
            "error": error_message,
            "execution_time": execution_time,
            "timestamp": datetime.now().isoformat(),
            "architecture": "refactored",
            "stats": self.get_stats()
        }