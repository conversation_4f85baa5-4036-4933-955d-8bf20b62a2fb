#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的TRA输入行提取脚本
"""

import sys
import re


def extract_and_display_tra_inputs(file_path: str):
    """
    提取并显示包含"🤖 TRA 输入"的行
    
    Args:
        file_path: 日志文件路径
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        tra_lines = []
        for line_num, line in enumerate(lines, 1):
            if '🤖 TRA 输入' in line:
                tra_lines.append((line_num, line.strip()))
        
        if not tra_lines:
            print("未找到包含'🤖 TRA 输入'的行")
            return
        
        print(f"找到 {len(tra_lines)} 条TRA输入记录:\n")
        print("行号\t时间戳\t\t\t日期\t\t累计收益\t周收益")
        print("-" * 80)
        
        for line_num, line in tra_lines:
            # 解析行内容
            match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}).*?日期=([^,]+), 累计收益=([^,]+), 周收益=(.+)', line)
            if match:
                timestamp = match.group(1)
                date = match.group(2)
                cum_return = match.group(3)
                week_return = match.group(4)
                print(f"{line_num}\t{timestamp}\t{date}\t{cum_return}\t\t{week_return}")
            else:
                print(f"{line_num}\t{line}")
        
        # 统计信息
        print(f"\n统计信息:")
        print(f"总记录数: {len(tra_lines)}")
        
        # 解析收益数据进行统计
        returns = []
        for _, line in tra_lines:
            match = re.search(r'累计收益=([^,]+)', line)
            if match:
                try:
                    returns.append(float(match.group(1)))
                except ValueError:
                    pass
        
        if returns:
            positive = sum(1 for r in returns if r > 0)
            negative = sum(1 for r in returns if r < 0)
            zero = sum(1 for r in returns if r == 0)
            
            print(f"正收益记录: {positive}")
            print(f"负收益记录: {negative}")
            print(f"零收益记录: {zero}")
            print(f"最大累计收益: {max(returns):.4f}")
            print(f"最小累计收益: {min(returns):.4f}")
    
    except FileNotFoundError:
        print(f"错误: 文件 '{file_path}' 不存在")
    except Exception as e:
        print(f"处理文件时出错: {e}")


def main():
    """主函数"""
    # 默认文件路径
    default_file = "test_results/NVDA_phase1.md"
    
    # 获取输入文件路径
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    else:
        input_file = default_file
    
    print(f"正在提取文件中的TRA输入行: {input_file}\n")
    extract_and_display_tra_inputs(input_file)


if __name__ == "__main__":
    main()
