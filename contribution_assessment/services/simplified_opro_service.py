"""
简化的OPRO优化服务

专注于基于本周输入输出数据的反思总结，生成增量提示词改进
增强版：集成历史数据分析功能
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

from ..interfaces.opro_service import IOPROService
from ..dto.phase_results_dto import OPROResult

# 导入每日状态管理器用于历史数据访问
try:
    from state_management.daily_state_manager import DailyStateManager
except ImportError:
    DailyStateManager = None


class SimplifiedOPROService(IOPROService):
    """
    简化的OPRO优化服务
    
    核心理念：
    1. 只使用本周的输入输出数据
    2. 通过反思总结生成经验教训
    3. 将经验教训作为增量提示词加入原提示词
    """
    
    def __init__(self, 
                 llm_interface=None,
                 logger: Optional[logging.Logger] = None,
                 enabled: bool = True,
                 daily_state_manager: Optional[DailyStateManager] = None):
        """
        初始化简化OPRO服务
        
        Args:
            llm_interface: LLM接口
            logger: 日志记录器
            enabled: 是否启用服务
            daily_state_manager: 每日状态管理器，用于历史数据访问
        """
        self.llm_interface = llm_interface
        self.logger = logger or self._create_default_logger()
        self.enabled = enabled
        
        # 初始化每日状态管理器
        self.daily_state_manager = daily_state_manager
        if self.daily_state_manager is None and DailyStateManager is not None:
            try:
                self.daily_state_manager = DailyStateManager()
                self.logger.debug("DailyStateManager在OPRO服务中初始化成功")
            except Exception as e:
                self.logger.warning(f"DailyStateManager初始化失败，将跳过历史数据分析: {e}")
        
        self.logger.info("简化OPRO服务初始化完成 (with historical data support)")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger("SimplifiedOPROService")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def optimize_agents(self,
                       shapley_values: Dict[str, float],
                       agents: Dict[str, Any],
                       config: Optional[Dict[str, Any]] = None) -> OPROResult:
        """
        基于Shapley值优化智能体
        
        Args:
            shapley_values: 智能体Shapley值
            agents: 智能体实例字典
            config: 优化配置参数
            
        Returns:
            OPROResult: 优化结果
        """
        if not self.enabled:
            return OPROResult(success=False, enabled=False, execution_time=0.0)
        
        start_time = datetime.now()
        
        try:
            # 识别表现最差的智能体 - 确保我们比较的是数值得分
            # shapley_values应该是 {agent_id: score} 的字典
            if not shapley_values:
                self.logger.warning("没有可用的Shapley值进行优化")
                return OPROResult(success=False, enabled=True, execution_time=0.0)
            
            # 确保我们处理的是agent_id到score的映射
            score_items = []
            for key, value in shapley_values.items():
                if isinstance(value, (int, float)):
                    score_items.append((key, value))
                else:
                    self.logger.warning(f"跳过非数值得分: {key} -> {type(value)}")
            
            if not score_items:
                self.logger.warning("没有有效的数值得分可供优化")
                return OPROResult(success=False, enabled=True, execution_time=0.0)
            
            worst_agent_id, worst_score = min(score_items, key=lambda x: x[1])
            
            self.logger.info(f"识别最差智能体: {worst_agent_id} (得分: {worst_score:.6f})")
            
            # 获取智能体实例
            agent = agents.get(worst_agent_id)
            if not agent:
                self.logger.warning(f"智能体 {worst_agent_id} 实例未找到")
                return OPROResult(success=False, enabled=True, execution_time=0.0)
            
            # 获取本周输入输出数据（支持多种方法名）
            weekly_io_data = None
            self.logger.info(f"🔍 检查智能体 {worst_agent_id} 的IO数据获取方法...")
            
            if hasattr(agent, 'get_weekly_io_data'):
                self.logger.info(f"  ✅ 智能体 {worst_agent_id} 支持 get_weekly_io_data 方法")
                weekly_io_data = agent.get_weekly_io_data()
                self.logger.info(f"  📊 获取到 {len(weekly_io_data) if weekly_io_data else 0} 条IO数据记录")
                # 添加详细的IO数据日志
                if weekly_io_data:
                    self._log_io_data_details(worst_agent_id, weekly_io_data)
            elif hasattr(agent, 'get_weekly_io_summary'):
                self.logger.info(f"  ✅ 智能体 {worst_agent_id} 支持 get_weekly_io_summary 方法")
                weekly_io_data = agent.get_weekly_io_summary()
                self.logger.info(f"  📊 获取到 {len(weekly_io_data) if weekly_io_data else 0} 条IO摘要数据")
                # 添加详细的IO数据日志
                if weekly_io_data:
                    self._log_io_data_details(worst_agent_id, weekly_io_data)
            else:
                self.logger.warning(f"  ❌ 智能体 {worst_agent_id} 不支持周IO数据获取方法")
                return OPROResult(success=False, enabled=True, execution_time=0.0)
            
            # 获取原始提示词
            original_prompt = agent.get_prompt_template()
            
            # 执行优化
            self.logger.info(f"🚀 开始优化智能体 {worst_agent_id}...")
            optimization_result = self.optimize_single_agent(
                agent_name=worst_agent_id,
                current_prompt=original_prompt,
                performance_score=worst_score,
                weekly_io_data=weekly_io_data
            )
            self.logger.info(f"🏁 智能体 {worst_agent_id} 优化完成，结果: {optimization_result.get('success', False)}")
            
            execution_time = (datetime.now() - start_time).total_seconds()
            updated_agents = []
            
            # 如果优化成功，应用新提示词到智能体
            if optimization_result.get("success", False):
                optimized_prompt = optimization_result.get("optimized_prompt", "")
                
                if optimized_prompt and optimized_prompt != original_prompt:
                    # 应用优化后的提示词到智能体实例
                    if hasattr(agent, 'update_prompt'):
                        success = agent.update_prompt(
                            new_prompt=optimized_prompt,
                            source="opro_optimization",
                            metadata={
                                "optimization_timestamp": datetime.now().isoformat(),
                                "original_score": worst_score,
                                "optimization_method": "reflection_based"
                            }
                        )
                        if success:
                            updated_agents.append(worst_agent_id)
                            self.logger.info(f"✅ 已应用优化提示词到智能体 {worst_agent_id}")
                            
                            # 记录提示词变化详情
                            self.logger.info(f"📝 智能体 {worst_agent_id} 提示词更新:")
                            self.logger.info(f"   原始长度: {len(original_prompt)} 字符")
                            self.logger.info(f"   优化后长度: {len(optimized_prompt)} 字符")
                            self.logger.info(f"   变化: +{len(optimized_prompt) - len(original_prompt)} 字符")
                            
                            # 显示提示词差异的前后对比（截取关键部分）
                            if len(optimized_prompt) > len(original_prompt):
                                added_content = optimized_prompt[len(original_prompt):].strip()
                                if added_content:
                                    self.logger.info(f"📄 新增内容预览: {added_content[:200]}{'...' if len(added_content) > 200 else ''}")
                        else:
                            self.logger.warning(f"智能体 {worst_agent_id} 提示词更新失败")
                    else:
                        self.logger.warning(f"智能体 {worst_agent_id} 不支持提示词更新，跳过应用")
                else:
                    self.logger.info(f"智能体 {worst_agent_id} 提示词无需更新")
            
            return OPROResult(
                success=optimization_result.get("success", False),
                enabled=True,
                execution_time=execution_time,
                updated_agents=updated_agents,
                optimization_result=optimization_result,
                optimization_stats={
                    "optimized_agent": worst_agent_id,
                    "original_score": worst_score,
                    "prompt_applied": len(updated_agents) > 0,
                    "optimization_method": optimization_result.get("optimization_method", "reflection_based")
                }
            )
            
        except Exception as e:
            self.logger.error(f"智能体优化失败: {e}")
            import traceback
            self.logger.error(f"优化异常详情: {traceback.format_exc()}")
            execution_time = (datetime.now() - start_time).total_seconds()
            return OPROResult(success=False, enabled=True, execution_time=execution_time)
    
    def optimize_single_agent(self,
                             agent_name: str,
                             current_prompt: str,
                             performance_score: float,
                             weekly_io_data: Optional[List[Dict[str, Any]]] = None,
                             failure_cases: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        优化单个智能体的提示词
        
        Args:
            agent_name: 智能体名称
            current_prompt: 当前提示词
            performance_score: 性能评分
            weekly_io_data: 本周输入输出数据
            failure_cases: 失败案例（兼容性参数，未使用）
            
        Returns:
            Dict[str, Any]: 优化结果
        """
        try:
            if not self.enabled:
                return {"success": False, "error": "简化OPRO服务未启用"}
            
            if not self.llm_interface:
                return {"success": False, "error": "LLM接口未配置"}
            
            # 生成反思总结
            reflection_summary = self._generate_reflection_summary(
                agent_name=agent_name,
                weekly_io_data=weekly_io_data or [],
                performance_score=performance_score
            )
            
            if not reflection_summary:
                return {"success": False, "error": "无法生成反思总结"}
            
            # 构建增量提示词
            enhanced_prompt = self._build_enhanced_prompt(current_prompt, reflection_summary)
            
            return {
                "success": True,
                "optimized_prompt": enhanced_prompt,
                "reflection_summary": reflection_summary,
                "estimated_improvement": 0.1,  # 简化的预期改进
                "optimization_method": "reflection_based",
                "agent_id": agent_name,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"单个智能体优化失败 ({agent_name}): {e}")
            return {"success": False, "error": str(e)}
    
    def _generate_reflection_summary(self,
                                   agent_name: str,
                                   weekly_io_data: List[Dict[str, Any]],
                                   performance_score: float) -> Optional[str]:
        """
        基于本周输入输出数据生成反思总结
        
        Args:
            agent_name: 智能体名称
            weekly_io_data: 本周输入输出数据
            performance_score: 性能评分
            
        Returns:
            Optional[str]: 反思总结文本
        """
        try:
            if not weekly_io_data:
                return "本周暂无足够的输入输出数据进行反思分析。"
            
            # 构建增强的反思提示词，深度关注亏损导向分析
            reflection_prompt = f"""
你是一个专业的AI系统分析师和交易风险管理专家，请基于以下{agent_name}智能体本周的运行数据进行深度反思总结。

## 智能体信息
- 智能体: {agent_name}
- 本周性能评分: {performance_score:.6f} (负值表示表现不佳)
- 数据记录数: {len(weekly_io_data)}

## 本周运行数据摘要
{self._format_weekly_data_summary(weekly_io_data)}

## 深度反思要求
作为交易风险管理专家，请重点从以下维度进行分析：

### 1. **亏损日期深度剖析** (最重要)
- 分析前一日亏损（previous_day_return < 0）时智能体的决策模式
- 识别亏损后是否有恐慌性决策或过度保守倾向
- 评估亏损日期的风险控制措施是否得当

### 2. **情绪化决策识别**
- 亏损后是否出现情绪化的买卖决策
- 是否存在"追涨杀跌"的不理性行为模式
- 在连续亏损情况下是否保持了客观分析

### 3. **风险管理能力评估**
- 在市场不利时是否采取了适当的防御性策略
- 持仓集中度和现金管理是否合理
- 止损和风险控制机制是否有效执行

### 4. **学习适应能力**
- 智能体是否能从前一日的亏损中吸取教训
- 决策逻辑是否随着市场变化而适应调整
- 是否具备从失败中快速恢复的能力

### 5. **盈亏对比模式分析**
- 对比盈利日与亏损日的决策差异
- 识别成功决策的共同特征
- 找出导致亏损的关键决策缺陷

基于以上深度分析，请提供5-7条具体、可操作的经验教训，重点关注如何在亏损和市场不利情况下做出更优的决策。

输出格式：
经验教训：
1. [风险控制方面的具体改进建议]
2. [亏损后恢复方面的改进建议] 
3. [情绪管理方面的改进建议]
4. [市场逆境应对的改进建议]
5. [决策质量提升的改进建议]

请确保每条建议都具体、可执行，并直接针对智能体在交易决策中的实际问题。
"""
            
            # 调用LLM生成反思
            response = self.llm_interface.analyze(
                prompt=reflection_prompt,
                model="glm-4-flash",
                temperature=0.3,
                top_p=0.7
            )
            
            if isinstance(response, dict) and "content" in response:
                return response["content"].strip()
            elif isinstance(response, str):
                return response.strip()
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"生成反思总结失败: {e}")
            return None
    
    def _format_weekly_data_summary(self, weekly_io_data: List[Dict[str, Any]]) -> str:
        """
        格式化本周数据摘要，深度分析亏损日期的详细信息
        增强版：集成历史数据分析
        """
        if not weekly_io_data:
            return "无数据记录"
        
        summary_lines = []
        loss_records = []
        profit_records = []
        neutral_records = []
        consecutive_losses = 0
        max_consecutive_losses = 0
        current_loss_streak = 0
        
        # 分析并分类记录，计算连续亏损
        for record in weekly_io_data[-10:]:  # 分析最近10条记录
            input_state = record.get("input_state", {})
            llm_response = record.get("llm_raw_response", {})
            timestamp = record.get("timestamp", "未知时间")
            
            previous_day_return = input_state.get("previous_day_return", 0.0)
            
            record_summary = {
                "timestamp": timestamp,
                "previous_day_return": previous_day_return,
                "input_state": input_state,
                "llm_response": llm_response,
                "date": input_state.get("current_date", "未知日期"),
                "cash": input_state.get("cash", 0),
                "positions": input_state.get("positions", {}),
                "position_values": input_state.get("position_values", {})
            }
            
            # 连续亏损分析
            if previous_day_return < 0:
                loss_records.append(record_summary)
                current_loss_streak += 1
                max_consecutive_losses = max(max_consecutive_losses, current_loss_streak)
            elif previous_day_return == 0.0:
                neutral_records.append(record_summary)
                current_loss_streak = 0
            else:
                profit_records.append(record_summary)
                current_loss_streak = 0
        
        # 添加统计摘要
        total_records = len(loss_records) + len(profit_records) + len(neutral_records)
        if total_records > 0:
            summary_lines.append("📊 **交易表现统计**:")
            summary_lines.append(f"总记录数: {total_records}")
            summary_lines.append(f"亏损日数: {len(loss_records)} ({len(loss_records)/total_records*100:.1f}%)")
            summary_lines.append(f"盈利日数: {len(profit_records)} ({len(profit_records)/total_records*100:.1f}%)")
            summary_lines.append(f"平衡日数: {len(neutral_records)} ({len(neutral_records)/total_records*100:.1f}%)")
            summary_lines.append(f"最长连续亏损: {max_consecutive_losses}天")
            summary_lines.append("")
        
        # 优先显示亏损日期的详细分析
        if loss_records:
            summary_lines.append("🔴 **亏损日期深度分析** (previous_day_return < 0):")
            
            for i, record in enumerate(loss_records, 1):
                summary_lines.append(f"【亏损日{i}】{record['date']} (前日亏损: {record['previous_day_return']:.4f})")
                
                # 财务状况分析
                input_state = record['input_state']
                cash = record['cash']
                positions = record['positions']
                position_values = record.get('position_values', {})
                
                total_position_value = sum(position_values.values()) if position_values else 0
                total_net_worth = cash + total_position_value
                cash_ratio = (cash / total_net_worth * 100) if total_net_worth > 0 else 0
                
                summary_lines.append(f"  💰 财务状况: 现金 ${cash:,.2f} ({cash_ratio:.1f}%), 总净值 ${total_net_worth:,.2f}")
                summary_lines.append(f"  📊 持仓分布: {positions}")
                
                # 风险暴露分析
                if positions:
                    position_count = sum(1 for v in positions.values() if v > 0)
                    summary_lines.append(f"  ⚠️  风险暴露: {position_count}只股票持仓")
                
                # LLM决策分析
                llm_resp = record['llm_response']
                processed_result = llm_resp.get('processed_result', {})
                analysis = processed_result.get('analysis', '无分析结果')
                
                # 提取决策关键词以判断决策倾向
                decision_keywords = self._extract_decision_keywords(analysis)
                summary_lines.append(f"  🧠 智能体决策: {analysis[:120]}...")
                summary_lines.append(f"  📈 决策倾向: {decision_keywords}")
                
                # 添加情绪化决策判断
                emotional_indicators = self._detect_emotional_decision(analysis, record['previous_day_return'])
                if emotional_indicators:
                    summary_lines.append(f"  ⚡ 情绪化倾向: {', '.join(emotional_indicators)}")
                
                summary_lines.append("")
        else:
            summary_lines.append("🟢 **本期无亏损记录，表现稳健**")
            summary_lines.append("")
        
        # 显示盈利日期对比分析
        if profit_records:
            summary_lines.append("🟢 **盈利日期对比分析** (previous_day_return > 0):")
            
            # 计算平均盈利表现
            avg_profit = sum(r['previous_day_return'] for r in profit_records) / len(profit_records)
            summary_lines.append(f"盈利日平均收益率: {avg_profit:.4f}")
            
            for i, record in enumerate(profit_records[-2:], 1):  # 显示最近2个盈利日
                summary_lines.append(f"【盈利日{i}】{record['date']} (前日盈利: {record['previous_day_return']:.4f})")
                
                llm_resp = record['llm_response']
                processed_result = llm_resp.get('processed_result', {})
                analysis = processed_result.get('analysis', '无分析结果')
                decision_keywords = self._extract_decision_keywords(analysis)
                
                summary_lines.append(f"  成功决策: {analysis[:100]}...")
                summary_lines.append(f"  决策特征: {decision_keywords}")
                summary_lines.append("")
        
        # 如果没有任何有效记录
        if not loss_records and not profit_records and not neutral_records:
            summary_lines.append("⚠️ 无有效的输入状态记录可供分析")
        
        # 添加历史数据增强分析（如果可用）
        historical_analysis = self._add_historical_context_analysis(weekly_io_data)
        if historical_analysis:
            summary_lines.append("\n" + "="*60)
            summary_lines.append("📚 **历史数据增强分析**")
            summary_lines.append("="*60)
            summary_lines.append(historical_analysis)
        
        return "\n".join(summary_lines)
    
    def _add_historical_context_analysis(self, weekly_io_data: List[Dict[str, Any]]) -> str:
        """
        添加历史数据上下文分析
        
        Args:
            weekly_io_data: 当前周的输入输出数据
            
        Returns:
            str: 历史上下文分析文本
        """
        if self.daily_state_manager is None:
            return ""
        
        try:
            # 从当前周数据中提取智能体信息
            agents_in_data = set()
            for record in weekly_io_data:
                # 尝试从记录中识别智能体类型
                input_state = record.get("input_state", {})
                if "analyst_outputs" in input_state:
                    agents_in_data.update(input_state["analyst_outputs"].keys())
                if "outlook_outputs" in input_state:
                    agents_in_data.update(input_state["outlook_outputs"].keys())
                if "trading_outputs" in input_state:
                    agents_in_data.update(input_state["trading_outputs"].keys())
            
            # 如果无法从数据中识别智能体，使用默认列表
            if not agents_in_data:
                agents_in_data = {"NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"}
            
            analysis_lines = []
            
            # 为每个智能体添加历史分析
            for agent_id in sorted(agents_in_data):
                if agent_id in ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"]:
                    # 获取历史表现趋势
                    performance_trend = self.daily_state_manager.get_performance_trend(agent_id, days_back=30)
                    
                    if performance_trend and 'error' not in performance_trend:
                        analysis_lines.append(f"\n🤖 **{agent_id}智能体历史表现**:")
                        
                        # 基础统计
                        days_analyzed = performance_trend.get('days_analyzed', 0)
                        avg_return = performance_trend.get('average_return', 0)
                        win_rate = performance_trend.get('win_rate', 0)
                        
                        analysis_lines.append(f"  📊 分析天数: {days_analyzed}天")
                        analysis_lines.append(f"  📈 平均收益率: {avg_return:.4f}")
                        analysis_lines.append(f"  🎯 胜率: {win_rate:.1%}")
                        
                        # 趋势分析
                        confidence_trend = performance_trend.get('confidence_trend')
                        if confidence_trend is not None:
                            trend_desc = "上升" if confidence_trend > 0.05 else "下降" if confidence_trend < -0.05 else "稳定"
                            analysis_lines.append(f"  🔄 信心度趋势: {trend_desc} ({confidence_trend:+.3f})")
                        
                        # 建议
                        recommendation = performance_trend.get('recommendation', '')
                        if recommendation:
                            analysis_lines.append(f"  💡 建议: {recommendation}")
                    
                    # 获取亏损模式分析
                    loss_analysis = self._analyze_loss_patterns(agent_id)
                    if loss_analysis and 'error' not in loss_analysis and loss_analysis.get('total_loss_days', 0) > 0:
                        analysis_lines.append(f"  🔴 亏损分析: {loss_analysis.get('pattern_analysis', '')}")
                        
                        recommendations = loss_analysis.get('recommendations', [])
                        if recommendations:
                            analysis_lines.append("  📋 改进建议:")
                            for rec in recommendations[:2]:  # 最多显示2个建议
                                analysis_lines.append(f"    - {rec}")
            
            # 添加整体历史对比
            if analysis_lines:
                analysis_lines.append(f"\n🔍 **历史数据洞察**:")
                analysis_lines.append("基于过去30天的交易数据，以上分析可帮助识别:")
                analysis_lines.append("• 智能体决策模式的稳定性和一致性")
                analysis_lines.append("• 亏损后的恢复能力和适应性调整") 
                analysis_lines.append("• 长期表现趋势与当前周表现的对比")
                analysis_lines.append("• 基于历史模式的风险预警和改进方向")
            
            return "\n".join(analysis_lines) if analysis_lines else ""
            
        except Exception as e:
            self.logger.error(f"历史上下文分析失败: {str(e)}")
            return f"历史数据分析异常: {str(e)}"
    
    def _extract_decision_keywords(self, analysis_text: str) -> str:
        """提取决策关键词以判断智能体的决策倾向"""
        if not analysis_text:
            return "无法判断"
        
        analysis_lower = analysis_text.lower()
        keywords = []
        
        # 买入倾向
        if any(word in analysis_lower for word in ['买入', '购买', '增持', '建仓', '加仓']):
            keywords.append('看涨')
        
        # 卖出倾向  
        if any(word in analysis_lower for word in ['卖出', '减持', '清仓', '止损']):
            keywords.append('看跌')
        
        # 观望倾向
        if any(word in analysis_lower for word in ['观望', '持有', '等待', '谨慎']):
            keywords.append('观望')
            
        # 风险意识
        if any(word in analysis_lower for word in ['风险', '谨慎', '保守', '稳健']):
            keywords.append('风险意识')
            
        # 激进倾向
        if any(word in analysis_lower for word in ['激进', '大幅', '全仓', '满仓']):
            keywords.append('激进')
        
        return ', '.join(keywords) if keywords else '中性'
    
    def _detect_emotional_decision(self, analysis_text: str, previous_return: float) -> List[str]:
        """检测情绪化决策迹象"""
        if not analysis_text:
            return []
        
        emotional_indicators = []
        analysis_lower = analysis_text.lower()
        
        # 在亏损后出现的情绪化反应
        if previous_return < -0.02:  # 前日亏损超过2%
            if any(word in analysis_lower for word in ['恐慌', '担心', '害怕', '避险']):
                emotional_indicators.append('恐慌性反应')
                
            if any(word in analysis_lower for word in ['报复性', '追回', '弥补']):
                emotional_indicators.append('报复性交易')
        
        # 过度自信或贪婪
        if any(word in analysis_lower for word in ['必然', '肯定', '绝对', '确信']):
            emotional_indicators.append('过度自信')
            
        # 从众心理
        if any(word in analysis_lower for word in ['大家都', '市场认为', '普遍看法']):
            emotional_indicators.append('从众心理')
        
        return emotional_indicators
    
    def _build_enhanced_prompt(self, original_prompt: str, reflection_summary: str) -> str:
        """
        构建增强的提示词（原提示词 + 经验教训）
        
        Args:
            original_prompt: 原始提示词
            reflection_summary: 反思总结
            
        Returns:
            str: 增强后的提示词
        """
        enhanced_prompt = f"""{original_prompt}

## 基于近期经验的改进指导
{reflection_summary}

请在分析时参考以上经验教训，持续改进分析质量。"""
        
        return enhanced_prompt
    

    def get_optimization_history(self,
                               agent_name: Optional[str] = None,
                               limit: int = 100) -> List[Dict[str, Any]]:
        """获取优化历史记录（简化实现）"""
        return []  # 简化实现，不保存历史记录


    def validate_prompt(self,
                       prompt: str,
                       agent_type: str) -> Dict[str, Any]:
        """验证提示词的有效性（简化实现）"""
        is_valid = len(prompt) > 10 and "智能体" in prompt
        return {
            "valid": is_valid,
            "method": "simplified",
            "checks": ["length", "keyword"],
            "timestamp": datetime.now().isoformat()
        }
    
    def _get_agent_historical_performance(self, agent_id: str, days_back: int = 30) -> List[Dict[str, Any]]:
        """
        从历史state中获取智能体表现数据
        
        Args:
            agent_id: 智能体ID (如 "NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA")
            days_back: 向前查询的天数
            
        Returns:
            List[Dict[str, Any]]: 历史表现数据列表
        """
        if self.daily_state_manager is None:
            self.logger.warning("DailyStateManager不可用，无法获取历史数据")
            return []
        
        try:
            # 计算查询日期范围
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')
            
            # 查询历史数据
            history = self.daily_state_manager.query_agent_history(agent_id, start_date, end_date)
            
            self.logger.debug(f"获取到{agent_id}的历史数据: {len(history)}条记录")
            return history
            
        except Exception as e:
            self.logger.error(f"获取{agent_id}历史表现数据失败: {str(e)}")
            return []
    
    def _analyze_loss_patterns(self, agent_id: str) -> Dict[str, Any]:
        """
        分析智能体在亏损日期的行为模式（基于previous_day_return < 0）
        
        Args:
            agent_id: 智能体ID
            
        Returns:
            Dict[str, Any]: 亏损模式分析结果
        """
        if self.daily_state_manager is None:
            return {"error": "DailyStateManager不可用"}
        
        try:
            # 获取亏损日期数据
            loss_days = self.daily_state_manager.get_loss_days_for_agent(agent_id, days_back=30)
            
            if not loss_days:
                return {
                    "agent_id": agent_id,
                    "total_loss_days": 0,
                    "pattern_analysis": "无亏损数据可分析",
                    "recommendations": []
                }
            
            # 分析亏损模式
            total_loss_days = len(loss_days)
            avg_loss = sum(day['trading_return'] for day in loss_days) / total_loss_days
            
            # 分析决策模式
            decision_patterns = {}
            confidence_levels = []
            
            for day in loss_days:
                output = day.get('output', {})
                if isinstance(output, dict):
                    # 提取决策相关信息
                    if 'confidence' in output:
                        confidence_levels.append(output['confidence'])
                    
                    # 根据智能体类型分析决策模式
                    if agent_id in ["NAA", "TAA", "FAA"]:
                        sentiment = output.get('sentiment', 'unknown')
                        decision_patterns[sentiment] = decision_patterns.get(sentiment, 0) + 1
                    elif agent_id in ["BOA", "BeOA", "NOA"]:
                        outlook = output.get('market_outlook', 'unknown')
                        decision_patterns[outlook] = decision_patterns.get(outlook, 0) + 1
                    elif agent_id == "TRA":
                        action = output.get('action', 'unknown')
                        decision_patterns[action] = decision_patterns.get(action, 0) + 1
            
            # 计算平均信心度
            avg_confidence = sum(confidence_levels) / len(confidence_levels) if confidence_levels else 0.0
            
            # 生成建议
            recommendations = self._generate_loss_pattern_recommendations(
                agent_id, total_loss_days, avg_loss, avg_confidence, decision_patterns
            )
            
            return {
                "agent_id": agent_id,
                "total_loss_days": total_loss_days,
                "average_loss": avg_loss,
                "average_confidence_on_loss_days": avg_confidence,
                "decision_patterns": decision_patterns,
                "pattern_analysis": f"在{total_loss_days}个亏损日中，平均亏损{avg_loss:.4f}，平均信心度{avg_confidence:.2f}",
                "recommendations": recommendations
            }
            
        except Exception as e:
            self.logger.error(f"分析{agent_id}亏损模式失败: {str(e)}")
            return {"error": str(e)}
    
    def _enhance_reflection_with_history(self, agent_name: str, weekly_data: Dict[str, Any]) -> str:
        """
        结合历史数据增强反思分析
        
        Args:
            agent_name: 智能体名称
            weekly_data: 当前周数据
            
        Returns:
            str: 增强后的反思分析文本
        """
        if self.daily_state_manager is None:
            return "历史数据不可用，仅基于当前周数据进行分析。"
        
        try:
            # 获取历史表现数据
            historical_performance = self._get_agent_historical_performance(agent_name, days_back=30)
            loss_pattern_analysis = self._analyze_loss_patterns(agent_name)
            
            # 构建增强的反思内容
            enhanced_reflection = []
            
            # 添加历史表现概览
            if historical_performance:
                enhanced_reflection.append("## 历史表现分析")
                enhanced_reflection.append(f"📊 过去30天共有{len(historical_performance)}条交易记录")
                
                # 计算历史胜率
                positive_days = len([h for h in historical_performance if h.get('trading_return', 0) > 0])
                win_rate = positive_days / len(historical_performance) * 100 if historical_performance else 0
                enhanced_reflection.append(f"📈 历史胜率: {win_rate:.1f}% ({positive_days}/{len(historical_performance)})")
            
            # 添加亏损模式分析
            if loss_pattern_analysis and 'error' not in loss_pattern_analysis:
                enhanced_reflection.append("\n## 亏损模式深度分析")
                enhanced_reflection.append(f"🔴 {loss_pattern_analysis.get('pattern_analysis', '')}")
                
                decision_patterns = loss_pattern_analysis.get('decision_patterns', {})
                if decision_patterns:
                    enhanced_reflection.append("🎯 亏损日决策模式分布:")
                    for pattern, count in decision_patterns.items():
                        enhanced_reflection.append(f"  - {pattern}: {count}次")
                
                recommendations = loss_pattern_analysis.get('recommendations', [])
                if recommendations:
                    enhanced_reflection.append("\n💡 基于历史模式的改进建议:")
                    for rec in recommendations:
                        enhanced_reflection.append(f"  - {rec}")
            
            # 添加趋势对比
            enhanced_reflection.append("\n## 当前表现与历史对比")
            current_performance = weekly_data.get('performance_summary', '当前周表现数据')
            enhanced_reflection.append(f"本周表现: {current_performance}")
            
            if historical_performance:
                recent_trend = self._analyze_recent_trend(historical_performance[-7:])  # 最近7天
                enhanced_reflection.append(f"近期趋势: {recent_trend}")
            
            return "\n".join(enhanced_reflection)
            
        except Exception as e:
            self.logger.error(f"增强{agent_name}反思分析失败: {str(e)}")
            return f"历史数据分析出现错误: {str(e)}，仅基于当前周数据进行分析。"
    
    def _generate_loss_pattern_recommendations(self, agent_id: str, loss_days: int, avg_loss: float, 
                                             avg_confidence: float, patterns: Dict[str, int]) -> List[str]:
        """生成基于亏损模式的改进建议"""
        recommendations = []
        
        # 基于亏损频率的建议
        if loss_days > 10:
            recommendations.append(f"亏损频率较高({loss_days}天)，建议加强风险控制机制")
        elif loss_days > 5:
            recommendations.append("适度亏损，建议优化决策逻辑")
        
        # 基于平均亏损的建议
        if avg_loss < -0.05:
            recommendations.append(f"单日平均亏损较大({avg_loss:.4f})，建议降低风险暴露")
        
        # 基于信心度的建议
        if avg_confidence < 0.3:
            recommendations.append(f"亏损日信心度偏低({avg_confidence:.2f})，建议提高决策确定性")
        elif avg_confidence > 0.8:
            recommendations.append("亏损日信心度过高，可能存在过度自信，建议增加谨慎性")
        
        # 基于决策模式的建议
        if patterns:
            most_common = max(patterns, key=patterns.get)
            if patterns[most_common] / sum(patterns.values()) > 0.6:
                recommendations.append(f"亏损时决策过于集中于'{most_common}'，建议增加策略多样性")
        
        return recommendations
    
    def _analyze_recent_trend(self, recent_data: List[Dict[str, Any]]) -> str:
        """分析最近的表现趋势"""
        if not recent_data:
            return "无足够数据分析趋势"
        
        returns = [data.get('trading_return', 0) for data in recent_data]
        positive_count = sum(1 for r in returns if r > 0)
        
        if positive_count >= len(returns) * 0.7:
            return f"近期表现良好，{len(returns)}天中{positive_count}天盈利"
        elif positive_count <= len(returns) * 0.3:
            return f"近期表现不佳，{len(returns)}天中仅{positive_count}天盈利"
        else:
            return f"近期表现平稳，{len(returns)}天中{positive_count}天盈利"
    
    def _log_io_data_details(self, agent_id: str, io_data: List[Dict[str, Any]], detailed: bool = True) -> None:
        """
        记录IO数据的详细信息
        
        Args:
            agent_id: 智能体ID
            io_data: IO数据列表
            detailed: 是否显示详细信息
        """
        try:
            if not io_data:
                self.logger.info(f"  ❌ {agent_id}: 无IO数据记录")
                return
            
            self.logger.info(f"  📋 {agent_id} IO数据详细分析:")
            self.logger.info("  " + "=" * 80)
            
            # 统计分析
            total_records = len(io_data)
            profit_records = []
            loss_records = []
            neutral_records = []
            
            # 分类统计
            for i, record in enumerate(io_data):
                previous_day_return = record.get('input_state', {}).get('previous_day_return', 0.0)
                if previous_day_return > 0:
                    profit_records.append((i, record))
                elif previous_day_return < 0:
                    loss_records.append((i, record))
                else:
                    neutral_records.append((i, record))
            
            # 输出统计摘要
            self.logger.info(f"  📊 数据统计: 总记录{total_records}条, 盈利{len(profit_records)}条, 亏损{len(loss_records)}条, 平衡{len(neutral_records)}条")
            
            if detailed:
                # 详细显示亏损记录（关键分析对象）
                if loss_records:
                    self.logger.info(f"  🔴 亏损日期详细记录 ({len(loss_records)}条):")
                    for idx, (original_idx, record) in enumerate(loss_records[:20]):  # 限制显示前20条
                        self._log_single_io_record(agent_id, original_idx + 1, record, "🔴")
                        if idx >= 19 and len(loss_records) > 20:
                            self.logger.info(f"  ... (还有{len(loss_records) - 20}条亏损记录)")
                            break
                
                # 显示部分盈利记录作为对比
                if profit_records:
                    self.logger.info(f"  🟢 盈利日期示例记录 (显示前5条,共{len(profit_records)}条):")
                    for idx, (original_idx, record) in enumerate(profit_records[:5]):
                        self._log_single_io_record(agent_id, original_idx + 1, record, "🟢")
                
                # 显示部分中性记录
                if neutral_records:
                    self.logger.info(f"  🟡 平衡日期示例记录 (显示前3条,共{len(neutral_records)}条):")
                    for idx, (original_idx, record) in enumerate(neutral_records[:3]):
                        self._log_single_io_record(agent_id, original_idx + 1, record, "🟡")
            else:
                # 简要模式：只显示关键统计
                if loss_records:
                    avg_loss = sum(r[1].get('previous_day_return', 0) for r in loss_records) / len(loss_records)
                    self.logger.info(f"  🔴 亏损记录摘要: {len(loss_records)}条, 平均亏损{avg_loss:.4f}")
                
                if profit_records:
                    avg_profit = sum(r[1].get('previous_day_return', 0) for r in profit_records) / len(profit_records)
                    self.logger.info(f"  🟢 盈利记录摘要: {len(profit_records)}条, 平均盈利{avg_profit:.4f}")
            
            self.logger.info("  " + "=" * 80)
            
        except Exception as e:
            self.logger.error(f"记录{agent_id} IO数据详情失败: {e}")
    
    def _get_llm_input_summary(self, input_state: Dict[str, Any]) -> str:
        """获取LLM输入的简要总结"""
        summary_parts = []
        if 'analyst_outputs' in input_state:
            summary_parts.append(f"分析师: {len(input_state['analyst_outputs'])}条")
        if 'outlook_outputs' in input_state:
            summary_parts.append(f"展望: {len(input_state['outlook_outputs'])}条")
        if not summary_parts:
            return "无前序输入"
        return ", ".join(summary_parts)

    def _log_single_io_record(self, agent_id: str, record_num: int, record: Dict[str, Any], icon: str) -> None:
        """
        记录单条IO数据
        
        Args:
            agent_id: 智能体ID
            record_num: 记录编号
            record: 单条记录数据
            icon: 状态图标
        """
        try:
            # 从IO记录中提取关键信息
            input_state = record.get('input_state', {})
            llm_response = record.get('llm_raw_response', {})
            
            # 提取真实日期和收益率
            current_date = input_state.get('current_date', 'N/A')
            previous_return = input_state.get('previous_day_return', 0.0)
            
            # 格式化日期
            time_str = current_date.strftime('%m-%d') if isinstance(current_date, datetime) else str(current_date)

            # 提取并格式化财务状况
            cash = input_state.get('cash', 'N/A')
            positions = input_state.get('positions', {})
            cash_str = f"${cash:,.0f}" if isinstance(cash, (int, float)) else str(cash)
            pos_str = ', '.join([f"{k}:{v}" for k, v in positions.items() if v > 0]) or "无持仓"
            pos_str = pos_str[:40] + '...' if len(pos_str) > 40 else pos_str

            # 提取LLM输入和输出的摘要
            llm_input_summary = self._get_llm_input_summary(input_state)
            analysis = llm_response.get('processed_result', {}).get('analysis', '无分析结果')
            response_preview = ' '.join(analysis.split())[:80] + '...' if analysis and analysis != '无分析结果' else analysis

            # 输出结构化的日志
            self.logger.info(f"    {icon} 记录{record_num:2d}: {time_str} | 前日收益率 {previous_return:+.4f}")
            self.logger.info(f"         ├─ 状态: 现金 {cash_str} | 持仓: {pos_str}")
            self.logger.info(f"         ├─ LLM输入: {llm_input_summary}")
            self.logger.info(f"         └─ LLM输出: {response_preview}")
            
        except Exception as e:
            self.logger.error(f"记录单条IO数据失败: {e}")
            previous_return = record.get('input_state', {}).get('previous_day_return', 0.0)
            self.logger.info(f"    {icon} 记录{record_num:2d}: 前日收益{previous_return:+.4f} (详情获取失败)")
    
    def _extract_key_fields_from_io_data(self, io_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        从IO数据中提取关键统计字段
        
        Args:
            io_data: IO数据列表
            
        Returns:
            关键统计信息字典
        """
        if not io_data:
            return {"total_records": 0}
        
        total_records = len(io_data)
        profit_days = sum(1 for record in io_data if record.get('previous_day_return', 0) > 0)
        loss_days = sum(1 for record in io_data if record.get('previous_day_return', 0) < 0)
        neutral_days = total_records - profit_days - loss_days
        
        # 计算平均收益率
        returns = [record.get('previous_day_return', 0) for record in io_data]
        avg_return = sum(returns) / len(returns) if returns else 0
        
        # 计算盈利和亏损的平均值
        profit_returns = [r for r in returns if r > 0]
        loss_returns = [r for r in returns if r < 0]
        
        avg_profit = sum(profit_returns) / len(profit_returns) if profit_returns else 0
        avg_loss = sum(loss_returns) / len(loss_returns) if loss_returns else 0
        
        return {
            "total_records": total_records,
            "profit_days": profit_days,
            "loss_days": loss_days,
            "neutral_days": neutral_days,
            "avg_return": avg_return,
            "avg_profit": avg_profit,
            "avg_loss": avg_loss,
            "profit_ratio": profit_days / total_records if total_records > 0 else 0,
            "loss_ratio": loss_days / total_records if total_records > 0 else 0
        }

