"""
日常投资组合状态跟踪器 (Daily Portfolio State Tracker)

核心功能:
- 记录每日投资组合完整状态 (现金、持仓、净值、收益率)
- 支持跨周状态连续性传递
- 提供数据分析和可视化支持
- 轻量级内存存储 + 可选文件持久化

设计原则:
- 高内聚: 专注投资组合状态管理单一职责
- 低耦合: 通过配置机制与交易环境解耦
- 最小侵入: 现有代码修改最小化
"""

from typing import Dict, Any, Optional, List
from datetime import datetime, date
import json
import copy
from pathlib import Path
from state_management.logging_modes import get_current_logging_mode, LoggingMode


class PortfolioStateTracker:
    """轻量级日常投资组合状态跟踪器"""
    
    def __init__(self, persistence_path: Optional[str] = None):
        """
        初始化跟踪器
        
        Args:
            persistence_path: 可选的文件持久化路径
        """
        # 核心数据结构: {date_str: {cash, positions, position_values, net_worth, daily_return}}
        self._daily_records: Dict[str, Dict[str, Any]] = {}
        self._persistence_path = persistence_path
        self._current_week = 1
        self._simulation_start_date: Optional[str] = None
        
        # 如果指定了持久化路径，尝试加载已有数据
        if self._persistence_path:
            self._load_from_file()
    
    def add_daily_record(self, 
                        date_str: str,
                        net_worth: float,
                        daily_return: float = 0.0,
                        cumulative_return: float = 0.0,
                        weekly_return: float = 0.0,
                        last_week_return: float = 0.0,
                        coalition_info: Optional[Dict[str, Any]] = None) -> None:
        """
        添加日常投资组合记录（支持收益率跟踪）
        
        Args:
            date_str: 交易日期 (YYYY-MM-DD格式)
            net_worth: 投资组合总净值
            daily_return: 当日收益率
            cumulative_return: 累计收益率
            weekly_return: 当前周收益率
            last_week_return: 上周收益率
            coalition_info: 联盟信息，包含联盟ID和大小等
        """
        # 设置模拟开始日期
        if self._simulation_start_date is None:
            self._simulation_start_date = date_str
        
        # 检查是否为完整联盟记录 - 只保存完整联盟的数据用于跨周状态传递
        if coalition_info is not None:
            coalition_size = coalition_info.get("coalition_size", 0)
            coalition_id = coalition_info.get("coalition_id", "unknown")
            
            # 完整联盟的判断：包含所有7个智能体
            is_complete_coalition = coalition_size == 7
            
            if not is_complete_coalition:
                # 子集联盟记录不保存到状态跟踪器，避免污染跨周状态传递
                current_mode = get_current_logging_mode()
                if current_mode == LoggingMode.DETAILED:
                    print(f"📊 跳过子集联盟记录: {date_str} | 联盟大小={coalition_size} | 联盟ID={coalition_id}")
                return
            else:
                # 完整联盟记录
                current_mode = get_current_logging_mode()
                if current_mode == LoggingMode.DETAILED:
                    print(f"📊 保存完整联盟记录: {date_str} | 联盟大小={coalition_size}")
        
        # 深拷贝避免引用问题
        record = {
            "date": date_str,
            "net_worth": float(net_worth),
            "daily_return": float(daily_return),
            # 新增收益率跟踪字段
            "cumulative_return": float(cumulative_return),
            "weekly_return": float(weekly_return),
            "last_week_return": float(last_week_return),
            "week_number": self._current_week,
            "timestamp": datetime.now().isoformat()
        }
        
        self._daily_records[date_str] = record
        
        # 自动持久化
        if self._persistence_path:
            self._save_to_file()
        
        # 根据日志模式调整输出的详细程度
        current_mode = get_current_logging_mode()
        if current_mode == LoggingMode.DETAILED:
            # 详细模式：显示更多持仓信息
            print(f"📈 投资组合状态: {date_str} | 累计收益率={cumulative_return:.4f} | 周收益率={weekly_return:.4f} | 上周收益率={last_week_return:.4f} | 周数={self._current_week}")
        else:
            # 简洁模式：只显示关键收益率信息
            print(f"📈 投资组合状态: {date_str} | 累计收益率={cumulative_return:.4f} | 周收益率={weekly_return:.4f} | 上周收益率={last_week_return:.4f} | 周数={self._current_week}")
    
    def get_latest_state(self) -> Optional[Dict[str, Any]]:
        """
        获取最新的投资组合状态
        
        Returns:
            最新的投资组合状态字典，如果无记录则返回None
        """
        if not self._daily_records:
            return None
        
        # 按日期排序获取最新记录
        latest_date = max(self._daily_records.keys())
        latest_record = self._daily_records[latest_date]
        
        # 返回StockTradingEnv.reset()所需的状态格式（仅包含收益率信息）
        state = {
            "net_worth": latest_record["net_worth"],
            # 收益率状态字段
            "cumulative_return": latest_record.get("cumulative_return", 0.0),
            "weekly_return": latest_record.get("weekly_return", 0.0),
            "last_week_return": latest_record.get("last_week_return", 0.0),
            "last_date": latest_date,
            "week_number": latest_record["week_number"]
        }
        
        # 如果当前记录的周数小于跟踪器的当前周数，说明需要进行周收益率重置
        if latest_record["week_number"] < self._current_week:
            # 将上一周的周收益率作为last_week_return，重置当前周收益率
            state["last_week_return"] = state["weekly_return"]
            state["weekly_return"] = 0.0
            state["week_number"] = self._current_week
        
        return state
    
    def get_clean_state_for_new_phase(self, current_week_number: int) -> Optional[Dict[str, Any]]:
        """
        获取用于新阶段的干净状态，确保周收益率在新周或新阶段开始时重置。
        此方法解决了阶段间收益率污染问题。

        Args:
            current_week_number: 当前运行的周数

        Returns:
            干净的状态字典，适用于新阶段或新周的开始
        """
        print(f"🔍 get_clean_state_for_new_phase 被调用: current_week_number={current_week_number}")
        self.debug_tracker_state()
        
        if not self._daily_records:
            print(f"⚠️ 没有历史记录，返回None")
            return None  # 没有历史记录，从零开始

        latest_state = self.get_latest_state()
        if not latest_state:
            return None

        clean_state = latest_state.copy()
        latest_week_in_tracker = latest_state.get("week_number", 1)

        # 案例一: 开始一个新周 (例如, 跟踪器记录的最新是第1周, 当前要运行第2周)
        if latest_week_in_tracker < current_week_number:
            # 上一周的 `weekly_return` 成为新周的 `last_week_return`
            clean_state["last_week_return"] = latest_state.get("weekly_return", 0.0)
            # 新周的 `weekly_return` 必须重置为 0
            clean_state["weekly_return"] = 0.0

        # 案例二: 在同一周内开始一个新阶段 (例如, 完整联盟跑完后，子集联盟开始)
        elif latest_week_in_tracker == current_week_number:
            # 在同一周内，weekly_return不应重置，应继承当前值
            # last_week_return 也应正确设置
            if current_week_number > 1:
                previous_week_state = self.get_week_end_return_state(current_week_number - 1)
                if previous_week_state:
                    clean_state["last_week_return"] = previous_week_state.get("weekly_return", 0.0)
                else:
                    clean_state["last_week_return"] = 0.0
            else:
                clean_state["last_week_return"] = 0.0

        # 案例三: 异常情况 (例如, 跟踪器记录的周数大于当前运行的周数), 按原样返回
        else:
             self.logger.warning(f"状态跟踪器周数 ({latest_week_in_tracker}) 大于当前运行周数 ({current_week_number})")

        return clean_state

    def get_week_end_return_state(self, week_number: int) -> Optional[Dict[str, Any]]:
        """
        获取指定周末的收益率状态，用于跨周传递
        
        Args:
            week_number: 周数
            
        Returns:
            周末收益率状态字典，如果未找到则返回None
        """
        # 查找指定周的最后一个交易日记录
        week_records = [record for record in self._daily_records.values() 
                       if record["week_number"] == week_number]
        
        if not week_records:
            return None
        
        # 获取该周最后一个交易日的记录
        last_record = max(week_records, key=lambda x: x["date"])
        
        return {
            "cumulative_return": last_record.get("cumulative_return", 0.0),
            "weekly_return": last_record.get("weekly_return", 0.0),
            "last_week_return": last_record.get("weekly_return", 0.0),  # 本周的周收益率作为下周的上周收益
            "net_worth": last_record["net_worth"]
        }
    
    def start_new_week(self, week_number: int) -> None:
        """
        开始新的一周（处理周收益率重置）
        
        Args:
            week_number: 新的周数
        """
        self._current_week = week_number
        
        # 如果有上一周的数据，确保正确处理周收益率传递
        if week_number > 1:
            previous_week_state = self.get_week_end_return_state(week_number - 1)
            if previous_week_state:
                print(f"🔄 投资组合跟踪器: 开始第{week_number}周, 继承累计收益率={previous_week_state.get('cumulative_return', 0):.4f}")
            else:
                print(f"🔄 投资组合跟踪器: 开始第{week_number}周 (无前周数据)")
        else:
            print(f"🔄 投资组合跟踪器: 开始第{week_number}周 (初始周)")
    
    def get_week_summary(self, week_number: int) -> Dict[str, Any]:
        """
        获取指定周的投资组合摘要
        
        Args:
            week_number: 周数
            
        Returns:
            周摘要数据
        """
        week_records = {
            date_str: record for date_str, record in self._daily_records.items()
            if record.get("week_number") == week_number
        }
        
        if not week_records:
            return {"week_number": week_number, "records_count": 0}
        
        dates = sorted(week_records.keys())
        start_record = week_records[dates[0]]
        end_record = week_records[dates[-1]]
        
        week_return = (end_record["net_worth"] - start_record["net_worth"]) / start_record["net_worth"]
        
        return {
            "week_number": week_number,
            "start_date": dates[0],
            "end_date": dates[-1],
            "records_count": len(week_records),
            "start_net_worth": start_record["net_worth"],
            "end_net_worth": end_record["net_worth"],
            "week_return": week_return
        }
    
    def get_all_records(self) -> List[Dict[str, Any]]:
        """
        获取所有日常记录，按日期排序
        
        Returns:
            按日期排序的所有记录列表
        """
        return [self._daily_records[date_str] for date_str in sorted(self._daily_records.keys())]
    
    def get_plotting_data(self) -> Dict[str, List]:
        """
        获取用于绘图的数据格式
        
        Returns:
            包含dates, net_worth, daily_returns等列表的字典
        """
        if not self._daily_records:
            return {"dates": [], "net_worth": [], "daily_returns": []}
        
        sorted_records = self.get_all_records()
        
        return {
            "dates": [record["date"] for record in sorted_records],
            "net_worth": [record["net_worth"] for record in sorted_records],
            "daily_returns": [record["daily_return"] for record in sorted_records],
            "week_numbers": [record.get("week_number", 1) for record in sorted_records]
        }
    
    def get_total_return(self) -> float:
        """
        计算总收益率
        
        Returns:
            从开始到最新的总收益率
        """
        if len(self._daily_records) < 2:
            return 0.0
        
        records = self.get_all_records()
        start_net_worth = records[0]["net_worth"]
        end_net_worth = records[-1]["net_worth"]
        
        return (end_net_worth - start_net_worth) / start_net_worth
    
    def _save_to_file(self) -> None:
        """保存数据到文件"""
        try:
            if self._persistence_path:
                # 确保目录存在
                Path(self._persistence_path).parent.mkdir(parents=True, exist_ok=True)
                
                # 保存数据
                save_data = {
                    "daily_records": self._daily_records,
                    "current_week": self._current_week,
                    "simulation_start_date": self._simulation_start_date,
                    "last_updated": datetime.now().isoformat()
                }
                
                with open(self._persistence_path, 'w', encoding='utf-8') as f:
                    json.dump(save_data, f, indent=2, ensure_ascii=False)
                    
        except Exception as e:
            print(f"⚠️ 投资组合数据保存失败: {e}")
    
    def _load_from_file(self) -> None:
        """从文件加载数据"""
        try:
            if self._persistence_path and Path(self._persistence_path).exists():
                with open(self._persistence_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                self._daily_records = data.get("daily_records", {})
                self._current_week = data.get("current_week", 1)
                self._simulation_start_date = data.get("simulation_start_date")
                
                print(f"📂 加载投资组合历史数据: {len(self._daily_records)}条记录")
                
        except Exception as e:
            print(f"⚠️ 投资组合数据加载失败: {e}")
            # 加载失败时使用空数据
            self._daily_records = {}
            self._current_week = 1
            self._simulation_start_date = None
    
    def clear_data(self) -> None:
        """清除所有数据（主要用于测试）"""
        self._daily_records.clear()
        self._current_week = 1
        self._simulation_start_date = None
        
        if self._persistence_path and Path(self._persistence_path).exists():
            Path(self._persistence_path).unlink()
        
        print("🧹 投资组合跟踪数据已清除")
    
    def __len__(self) -> int:
        """返回记录数量"""
        return len(self._daily_records)
    
    def debug_tracker_state(self) -> None:
        """调试方法：显示tracker当前状态"""
        print(f"\n🔍 PortfolioStateTracker 调试信息:")
        print(f"   - 总记录数: {len(self._daily_records)}")
        print(f"   - 当前周数: {self._current_week}")
        print(f"   - 模拟开始日期: {self._simulation_start_date}")
        
        if self._daily_records:
            print(f"   - 日期范围: {min(self._daily_records.keys())} 到 {max(self._daily_records.keys())}")
            latest_state = self.get_latest_state()
            if latest_state:
                print(f"   - 最新状态: 累计收益率={latest_state['cumulative_return']:.4f}, 周收益率={latest_state['weekly_return']:.4f}")
            
            # 显示所有记录的简要信息
            print(f"   - 所有记录:")
            for date_str in sorted(self._daily_records.keys()):
                record = self._daily_records[date_str]
                print(f"     {date_str}: 累计={record.get('cumulative_return', 0):.4f}, 周={record.get('weekly_return', 0):.4f}, 周数={record.get('week_number', 1)}")
        else:
            print(f"   - 无记录数据")
        print()

    def __str__(self) -> str:
        """字符串表示"""
        total_return = self.get_total_return()
        return (f"PortfolioStateTracker("
                f"records={len(self._daily_records)}, "
                f"weeks={self._current_week}, "
                f"total_return={total_return:.4f})")