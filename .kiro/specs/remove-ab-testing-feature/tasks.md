# A/B测试功能移除实施计划

## 实施任务列表

- [x] 1. 准备和分析阶段
  - [x] 1.1 分析A/B测试功能的完整依赖关系
    - 识别所有引用A/B测试功能的文件和代码段
    - 分析与其他模块的耦合关系
    - 确认安全移除的边界
    - _需求: 2.2, 2.3_

  - [x] 1.2 创建功能移除前的备份
    - 备份当前完整的代码库状态
    - 记录当前A/B测试功能的完整实现
    - 创建回滚计划
    - _需求: 3.1, 3.2, 3.3, 3.4, 3.5_

- [-] 2. 核心文件移除阶段
  - [ ] 2.1 移除ABTestingManager模块
    - 删除 `contribution_assessment/ab_testing_manager.py` 文件
    - 验证没有其他文件直接导入该模块
    - 清理相关的__init__.py导入声明
    - _需求: 2.2_

  - [ ] 2.2 清理assessor.py中的A/B测试集成
    - 移除ABTestingManager的导入语句
    - 删除ab_testing_manager实例变量
    - 移除run_continuous_ab_optimization方法
    - 移除_get_original_agents_for_ab_test方法
    - 移除_should_continue_optimization方法
    - 移除_print_continuous_optimization_summary方法
    - 清理周期性优化中的A/B测试调用
    - _需求: 2.3_

- [ ] 3. 主运行脚本修改阶段
  - [ ] 3.1 修改run_opro_system.py的命令行参数
    - 从--mode选择中移除"ab-testing"选项
    - 移除--max-iterations参数定义
    - 更新帮助文档字符串
    - _需求: 1.1, 1.2_

  - [ ] 3.2 移除A/B测试运行模式函数
    - 删除run_ab_testing_mode函数
    - 移除主函数中的ab-testing模式分支
    - 清理相关的错误处理代码
    - _需求: 1.1_

  - [ ] 3.3 更新文档字符串和注释
    - 移除文档开头的A/B测试使用示例
    - 清理代码中的A/B测试相关注释
    - 更新模块级别的功能描述
    - _需求: 4.1_

- [ ] 4. 配置和文档清理阶段
  - [ ] 4.1 清理配置文件
    - 从config/opro_config.json移除ab_testing配置段
    - 检查其他配置文件中的A/B测试引用
    - 更新配置文件的示例和文档
    - _需求: 4.2_

  - [ ] 4.2 更新项目文档
    - 更新README.md中的功能列表
    - 修改使用指南，移除A/B测试相关内容
    - 更新API文档
    - _需求: 4.3_

- [ ] 5. 向后兼容性处理阶段
  - [ ] 5.1 实现废弃参数的友好错误处理
    - 添加对--max-iterations参数的废弃警告
    - 实现对ab-testing模式的清晰错误提示
    - 添加配置文件兼容性处理
    - _需求: 6.1, 6.2, 6.3_

  - [ ] 5.2 创建迁移指南
    - 编写用户迁移文档
    - 提供替代方案的使用示例
    - 创建常见问题解答
    - _需求: 4.1_

- [x] 6. 测试和验证阶段
  - [x] 6.1 创建回归测试用例
    - 编写测试验证所有保留功能正常工作
    - 创建错误处理的单元测试
    - 实现集成测试覆盖主要使用场景
    - _需求: 3.1, 3.2, 3.3, 3.4, 3.5_

  - [x] 6.2 执行全面的功能测试
    - 测试evaluation模式的完整功能
    - 验证optimization模式的OPRO优化
    - 确认weekly模式的周期性优化
    - 测试integrated模式的集成功能
    - 验证dashboard模式的数据显示
    - _需求: 3.1, 3.2, 3.3, 3.4, 3.5_

  - [x] 6.3 验证错误处理和兼容性
    - 测试使用废弃参数时的错误提示
    - 验证不支持模式的错误处理
    - 确认配置文件兼容性处理
    - _需求: 6.1, 6.2, 6.3_

- [ ] 7. 代码质量和清理阶段
  - [ ] 7.1 执行静态代码分析
    - 运行linting工具检查未使用的导入
    - 验证没有死代码残留
    - 确认代码风格一致性
    - _需求: 2.4_

  - [ ] 7.2 清理测试文件和日志配置
    - 移除A/B测试相关的测试文件
    - 清理日志配置中的A/B测试记录器
    - 验证系统不产生A/B测试相关日志
    - _需求: 5.1, 5.2, 5.3_

- [ ] 8. 最终验证和部署阶段
  - [ ] 8.1 执行端到端测试
    - 运行完整的系统测试套件
    - 验证所有命令行选项的正确性
    - 确认系统启动和关闭流程
    - _需求: 3.1, 3.2, 3.3, 3.4, 3.5_

  - [ ] 8.2 性能和稳定性验证
    - 确认移除A/B测试后系统性能无退化
    - 验证内存使用和资源消耗
    - 测试长时间运行的稳定性
    - _需求: 3.1, 3.2, 3.3, 3.4, 3.5_

  - [ ] 8.3 创建变更日志和发布说明
    - 记录所有变更的详细信息
    - 创建用户可读的发布说明
    - 更新版本号和标签
    - _需求: 4.1, 4.3_