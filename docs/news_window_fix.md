# 新闻窗口修复文档

## 问题描述

在 `stock_trading_env.py` 中发现了新闻窗口的逻辑问题：

```python
# 原始有问题的代码
for i in range(self.news_window):
    target_day_index = self.current_day_index - i
    
    if target_day_index >= 0 and target_day_index < len(self.trading_days):
        # 只能获取交易日范围内的新闻
```

**问题分析:**
- 在第一个交易日（`current_day_index = 0`）时，只能获取到当天的新闻
- 无法获取到交易日期开始前的新闻数据，即使这些新闻实际存在
- 导致第一天的新闻窗口不完整

## 根本原因

1. **数据加载范围限制**: `self.news_data` 只初始化了 `self.trading_days` 范围内的日期
2. **索引范围限制**: 新闻收集逻辑被限制在 `trading_days` 的索引范围内
3. **日期范围不匹配**: 新闻数据可能存在于交易日期开始前，但没有被加载

## 修复方案

### 1. 扩展新闻数据加载范围

**修复前:**
```python
# 只初始化交易日范围内的日期
for date in self.trading_days:
    date_str = date.strftime('%Y-%m-%d')
    news_data[date_str] = {stock: [] for stock in stocks}
```

**修复后:**
```python
# 扩展日期范围：包含交易日期开始前的 news_window 天
import pandas as pd
first_trading_day = self.trading_days[0]
extended_start_date = first_trading_day - pd.Timedelta(days=self.news_window)

# 生成扩展的日期范围（包含交易日前的日期）
extended_date_range = pd.date_range(
    start=extended_start_date, 
    end=self.trading_days[-1], 
    freq='D'  # 每日频率，包含所有日期（不仅仅是交易日）
)

# 初始化扩展日期范围内每个日期的新闻数据字典
for date in extended_date_range:
    date_str = date.strftime('%Y-%m-%d')
    news_data[date_str] = {stock: [] for stock in stocks}
```

### 2. 修复新闻收集逻辑

**修复前:**
```python
# 基于交易日索引的限制性逻辑
for i in range(self.news_window):
    target_day_index = self.current_day_index - i
    
    if target_day_index >= 0 and target_day_index < len(self.trading_days):
        current_news_date_obj = self.trading_days[target_day_index]
        current_news_date_str = current_news_date_obj.strftime('%Y-%m-%d') 
        if current_news_date_str in self.news_data:
            news_history[current_news_date_str] = self.news_data[current_news_date_str]
```

**修复后:**
```python
# 基于实际日期的灵活逻辑
current_trading_date = self.trading_days[self.current_day_index]

for i in range(self.news_window):
    # 计算目标日期（向前推i天）
    import pandas as pd
    target_date = current_trading_date - pd.Timedelta(days=i)
    target_date_str = target_date.strftime('%Y-%m-%d')
    
    # 检查该日期是否有新闻数据
    if target_date_str in self.news_data:
        news_history[target_date_str] = self.news_data[target_date_str]
        news_days_collected += 1
```

### 3. 更新期望值计算

**修复前:**
```python
# 错误的期望值计算
expected_news_days = min(self.news_window, self.current_day_index + 1)
```

**修复后:**
```python
# 正确的期望值计算
expected_news_days = self.news_window
```

## 数据库查询范围修复

发现了第二个问题：虽然扩展了 `news_data` 字典的日期范围，但数据库查询仍然被限制在交易日范围内。

### 4. 扩展数据库查询范围

**修复前:**
```python
# 只查询交易日范围内的新闻
start_date_str = self.trading_days[0].strftime('%Y-%m-%d')
end_date_str = self.trading_days[-1].strftime('%Y-%m-%d')
```

**修复后:**
```python
# 查询扩展日期范围内的新闻（包含交易日前的news_window天）
import pandas as pd
first_trading_day = self.trading_days[0]
extended_start_date = first_trading_day - pd.Timedelta(days=self.news_window)

start_date_str = extended_start_date.strftime('%Y-%m-%d')
end_date_str = self.trading_days[-1].strftime('%Y-%m-%d')
```

## 验证结果

通过测试验证了修复的正确性：

### 测试场景
- **交易日期范围**: 2024-12-30 到 2025-01-03
- **新闻窗口**: 5天
- **第一个交易日**: 2024-12-30

### 修复前的问题
- 第一天只能获取到1天新闻（当天）
- 数据库查询范围限制在交易日内
- 新闻历史日期: `['2024-12-30']`

### 修复后的结果
- ✅ **数据库加载**: 成功从数据库加载了2116条新闻数据
- ✅ **新闻窗口大小**: 期望5天，实际5天
- ✅ **日期范围**: 新闻历史日期 `['2024-12-26', '2024-12-27', '2024-12-28', '2024-12-29', '2024-12-30']`
- ✅ **包含交易日前的新闻**: 成功获取到交易日前4天的新闻
- ✅ **数据范围扩展**: 新闻数据日期范围从2024-12-25到2025-01-03
- ✅ **实际新闻内容**: 总共51条新闻，包含真实的新闻标题和内容

### 新闻内容验证
- 2024-12-26: 11条新闻
- 2024-12-27: 19条新闻
- 2024-12-28: 6条新闻
- 2024-12-29: 1条新闻
- 2024-12-30: 14条新闻

## 影响范围

此修复影响：
1. **新闻数据加载**: 扩展了加载的日期范围
2. **新闻窗口逻辑**: 改进了新闻收集的算法
3. **第一天体验**: 第一个交易日现在能获取到完整的新闻历史

## 性能影响

- **内存使用**: 略微增加（额外加载 `news_window` 天的新闻数据结构）
- **加载时间**: 基本无影响（主要是初始化空的数据结构）
- **运行时性能**: 无影响

## 向后兼容性

此修复完全向后兼容，不会破坏任何现有功能，只是改善了新闻窗口的行为。
