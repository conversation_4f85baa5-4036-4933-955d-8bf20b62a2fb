2025-07-28 21:17:06,823 - __main__ - INFO - ======================================================================
2025-07-28 21:17:06,823 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-28 21:17:06,823 - __main__ - INFO - ======================================================================
2025-07-28 21:17:06,823 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-28 21:17:06,823 - __main__ - INFO - 开始模拟联盟: {'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'}
2025-07-28 21:17:06,823 - __main__ - INFO - 📅 当前周数: 1
2025-07-28 21:17:06,823 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-28 21:17:06,823 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'})
2025-07-28 21:17:06,824 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-28 21:17:06,967 - __main__ - INFO - 📅 设置当前模拟周数: 1 (来自参数)
2025-07-28 21:17:06,968 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 21:17:26,589 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 21:17:26,589 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 21:18:05,499 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 21:18:05,500 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 21:18:05,500 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000
2025-07-28 21:18:20,414 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 21:18:20,415 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 21:18:20,415 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 21:18:20,415 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 21:18:20,415 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 21:18:20,415 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在2025-01-02的分析中，我们首先对基础面进行了评估。FAA基本面分析显示公司财务健康度良好，估值合理，且在竞争地位上处于领先。然而，由于NAA新闻分析缺乏相关新闻数据，我们无法评估市场情绪和新闻对目标股票的影响，因此分析信心度较低。在技术面分析中，TAA技术分析显示股票价格呈现上升趋势，支撑位和阻力位明确，技术评分较高，分析信心度也较高。展望层智能体中，BOA看涨信号强度较高，而BeOA看跌信号和NOA中性观点均因缺乏新闻数据而分析信心度较低。综合各层级信息，尽管基础面和技术面均显示积极信号，但由于新闻分析的信心度较低，我们无法完全确认市场情绪。因此，在风险控制方面，我们考虑了信号的一致性和潜在风险，最终决策为保持观望。**最终决策：保持不变**', 'action': 'hold', 'trading_actions': {'__HOLD__': 0.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-02'}
2025-07-28 21:18:20,415 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 0.0}
2025-07-28 21:18:20,415 - __main__ - INFO - 📈 第1天交易动作: {'__HOLD__': 0.0}
2025-07-28 21:18:20,418 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 21:19:44,432 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 21:19:44,434 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 21:20:10,443 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 21:20:10,444 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 21:20:10,444 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-03, 累计收益=0.0000, 周收益=0.0000
2025-07-28 21:20:24,733 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 21:20:24,734 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 21:20:24,735 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 21:20:24,735 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 21:20:24,735 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 21:20:24,735 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 21:20:24,736 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 21:20:24,736 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对META进行了全面的多维度分析。首先，从基础面来看，FAA显示META的财务健康度良好，估值合理，显示出公司的稳健性。NAA的分析表明，尽管META更换了全球事务主管，这一人事变动可能对公司的政治立场和未来政策产生影响，但整体上对META股票的影响为中性。技术面分析显示，TAA指出META股票价格呈现上升趋势，支撑位和阻力位的设定也支持这一趋势。展望层分析中，BOA显示上涨概率较高，而BeOA和NOA则提供了中性或看跌的观点。在风险评估方面，各层级信号基本一致，分歧度较低，市场环境不确定性较高。综合以上分析，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-03'}
2025-07-28 21:20:24,737 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-28 21:20:24,737 - __main__ - INFO - 📈 第2天交易动作: {'META': 1.0}
2025-07-28 21:20:24,740 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 21:20:45,370 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 21:20:45,371 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 21:21:07,853 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 21:21:07,854 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 21:21:07,854 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-06, 累计收益=-0.0007, 周收益=-0.0007
2025-07-28 21:21:17,006 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 21:21:17,007 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 21:21:17,007 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 21:21:17,007 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 21:21:17,007 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 21:21:17,008 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 21:21:17,008 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 21:21:17,008 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在2025-01-06的分析中，我们对META股票进行了全面的多维度分析。首先，从基础面来看，FAA基本面分析显示META公司财务健康，估值合理，且在竞争地位上处于领先。尽管NAA新闻分析由于缺乏具体新闻而无法提供情绪评分和影响评估，但整体上没有重大负面消息。技术面分析由TAA提供，显示META股票处于上升趋势，支撑位和阻力位分析支持这一趋势。RSI和MACD指标均显示出短期内股票趋势看涨。展望层分析中，BOA看涨信号强于BeOA看跌信号，NOA认为市场展望中性。综合分析，各层级信号基本一致，风险控制方面，信号分歧度较低。因此，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-06'}
2025-07-28 21:21:17,008 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-28 21:21:17,008 - __main__ - INFO - 📈 第3天交易动作: {'META': 1.0}
2025-07-28 21:21:17,013 - __main__ - INFO - 💰 第3天净值变化: 收益率=0.0344, 净值=$1033663.50
2025-07-28 21:21:17,016 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 21:21:32,577 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 21:21:32,578 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 21:22:08,407 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 21:22:08,409 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 21:22:08,409 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-07, 累计收益=0.0337, 周收益=0.0337
2025-07-28 21:22:13,247 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 21:22:13,247 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 21:22:13,247 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 21:22:13,248 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 21:22:13,248 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 21:22:13,248 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 21:22:13,248 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 21:22:13,248 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对META股票进行了全面的多维度分析。首先，从基础面来看，FAA基本面分析显示META公司财务健康度良好，估值合理，且在竞争地位上处于领先。尽管NAA新闻分析由于缺乏具体新闻而无法提供明确的市场情绪变化，但整体上没有出现重大负面消息。技术面分析显示，TAA技术分析指出META股票处于上升趋势，支撑位和阻力位的设定也支持这一观点。展望层分析中，BOA看涨信号强度较高，而BeOA看跌信号较弱，NOA则持中性观点。在风险评估方面，各层级信号的一致性较高，风险控制指标也显示出较低的风险。综合以上分析，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-07'}
2025-07-28 21:22:13,248 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-28 21:22:13,248 - __main__ - INFO - 📈 第4天交易动作: {'META': 1.0}
2025-07-28 21:22:13,251 - __main__ - INFO - 💰 第4天净值变化: 收益率=-0.0040, 净值=$1029540.73
2025-07-28 21:22:13,252 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 21:22:31,921 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 21:22:31,923 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 21:22:54,041 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 21:22:54,042 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 21:22:54,043 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-08, 累计收益=0.0295, 周收益=0.0295
2025-07-28 21:23:04,398 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 21:23:04,399 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 21:23:04,399 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 21:23:04,399 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 21:23:04,399 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 21:23:04,399 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 21:23:04,399 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 21:23:04,399 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在2025-01-08的分析中，我们对META股票进行了全面的多维度分析。首先，从基础面来看，FAA基本面分析显示META的财务健康度良好，估值合理，且在竞争中处于领先地位。尽管NAA新闻分析由于缺乏具体新闻数据而无法提供明确的市场情绪变化，但整体上没有发现重大负面消息。技术面分析由TAA提供，显示META股票处于上升趋势，支撑位在$602.79附近，阻力位在$616.44附近。技术评分中等偏上，分析信心度较高。展望层分析中，BOA看涨信号强度较高，而BeOA看跌信号较弱，NOA持中性观点。综合分析，各层级信号基本一致，风险控制方面，信号一致性较高，市场环境不确定性较低。因此，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-08'}
2025-07-28 21:23:04,399 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-28 21:23:04,399 - __main__ - INFO - 📈 第5天交易动作: {'META': 1.0}
2025-07-28 21:23:04,401 - __main__ - INFO - 💰 第5天净值变化: 收益率=-0.0105, 净值=$1018762.12
2025-07-28 21:23:04,401 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-28 21:23:04,402 - __main__ - INFO - ============================================================
2025-07-28 21:23:04,402 - __main__ - INFO - 第 1 周性能分析 - 联盟: frozenset({'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'})
2025-07-28 21:23:04,402 - __main__ - INFO - 周总收益率: 0.0192
2025-07-28 21:23:04,402 - __main__ - INFO - 周夏普比率: 3.8090
2025-07-28 21:23:04,402 - __main__ - INFO - 交易天数: 5
2025-07-28 21:23:04,402 - __main__ - INFO - ============================================================
2025-07-28 21:55:20,135 - __main__ - INFO - ======================================================================
2025-07-28 21:55:20,135 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-28 21:55:20,135 - __main__ - INFO - ======================================================================
2025-07-28 21:55:20,136 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-28 21:55:20,136 - __main__ - INFO - 开始模拟联盟: {'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'}
2025-07-28 21:55:20,136 - __main__ - INFO - 📅 当前周数: 2
2025-07-28 21:55:20,136 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-28 21:55:20,136 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'})
2025-07-28 21:55:20,137 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-01-08 (第2周)
2025-07-28 21:55:20,137 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-01-08, end_date=2025-01-15
2025-07-28 21:55:20,138 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-28 21:55:20,232 - __main__ - INFO - 📅 设置当前模拟周数: 2 (来自参数)
2025-07-28 21:55:20,234 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 21:55:41,682 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 21:55:41,683 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 21:56:16,729 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 21:56:16,731 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 21:56:16,731 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-08, 累计收益=0.0000, 周收益=0.0000
2025-07-28 21:56:24,744 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 21:56:24,744 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 21:56:24,744 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 21:56:24,744 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 21:56:24,744 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 21:56:24,745 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 21:56:24,745 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 21:56:24,745 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在2025-01-08的分析中，我们对META进行了全面的多维度分析。首先，从基础面来看，FAA给出了财务健康度评分8.5，表明公司财务状况良好，估值评估为合理，竞争优势领先。尽管NAA由于缺乏新闻数据无法提供具体分析，但根据逻辑推断，市场情绪可能较为稳定。技术面分析显示，TAA认为META显示出了看涨趋势，支撑位在$602.79附近，阻力位在$616.44附近，技术评分中等偏上，分析信心度较高。展望层分析中，BOA给出了看涨展望，认为META在当前市场趋势和财务健康评分下显示出看涨的市场展望。NOA则认为市场展望为中性，但看涨因素略占优势。综合以上分析，各层级信号基本一致，风险控制方面，信号分歧度较低，市场环境不确定性不高。因此，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-08'}
2025-07-28 21:56:24,745 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-28 21:56:24,745 - __main__ - INFO - 📈 第1天交易动作: {'META': 1.0}
2025-07-28 21:56:24,748 - __main__ - INFO - 💰 第1天净值变化: 收益率=0.0028, 净值=$1002829.36
2025-07-28 21:56:24,750 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 21:56:24,751 - __main__ - INFO - TAA检测到无价格数据，建议跳过当前日期: 2025-01-09
2025-07-28 21:56:42,621 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 21:56:42,623 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 21:57:04,814 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 21:57:04,816 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 21:57:04,816 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-09, 累计收益=0.0028, 周收益=0.0028
2025-07-28 21:57:26,615 - __main__ - WARNING - 未找到明确的最终决策声明，保持action=hold
2025-07-28 21:57:26,615 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 21:57:26,615 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 21:57:26,615 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 21:57:26,615 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 21:57:26,615 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': "分析日期: 2025-01-09\n\n持仓: {'META': 1697}\n\n🤖 分析层智能体输出:\n\nNAA: 由于今天没有相关新闻，因此无法进行市场新闻和舆论情绪的分析。情绪评分、新闻摘要、关键事件列表和影响评估均无法提供。分析信心度设置为1，表示在没有数据的情况下，分析结果基于逻辑推断，但缺乏实际数据支持。\n\nTAA: 无法进行技术分析，建议跳过 (信心度: 1.0)\n\nFAA: 基本面分析显示公司财务健康度良好，估值合理，竞争地位领先，长期趋势看好。分析信心度未知。\n\n🔮 展望层智能体输出:\n\nBOA: 基于分析层智能体的输出，以下是我的看涨展望结果：\n\n### 市场展望\n- **看涨**：考虑到当前市场环境以及分析层智能体的输出，预计市场将呈现看涨趋势。\n\n## 分析细节\n- 基于FAA的财务健康和竞争地位，以及TAA的技术分析（虽然无法进行），预计公司股价有望上涨。\n\nBeOA: 由于缺乏新闻和关键事件，无法进行具体的市场分析。分析信心度未知。\n\nNOA: 市场展望保持中性。由于缺乏具体的市场新闻和情绪数据，市场可能处于观望状态。分析信心度未知。\n\n### 风险评估\n- 基于当前信息，风险相对较低，但存在市场不确定性。\n\n### 最终决策声明：买入\n\n综合以上分析，尽管存在市场不确定性，但基于FAA的稳健基本面和BOA的看涨展望，建议进行买入操作。", 'action': 'hold', 'trading_actions': {'__HOLD__': 0.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-09'}
2025-07-28 21:57:26,615 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 0.0}
2025-07-28 21:57:26,615 - __main__ - INFO - 📈 第2天交易动作: {'__HOLD__': 0.0}
2025-07-28 21:57:26,620 - __main__ - INFO - 💰 第2天净值变化: 收益率=-0.0082, 净值=$994564.19
2025-07-28 21:57:26,623 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 21:57:57,052 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 21:57:57,054 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 21:58:35,909 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 21:58:35,910 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 21:58:35,910 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-10, 累计收益=-0.0054, 周收益=-0.0054
2025-07-28 21:58:49,827 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 21:58:49,828 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 21:58:49,828 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 21:58:49,828 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 21:58:49,828 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 21:58:49,828 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 21:58:49,828 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 21:58:49,828 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '今日市场新闻情绪中性，对目标股票的影响程度有限。Meta Platforms (META) 相关新闻可能对该公司股价产生一定影响。从基本面分析来看，FAA显示META财务健康度良好，估值合理，竞争地位领先。技术面分析显示，TAA显示META股价呈现上升趋势，存在一个潜在的支撑位$597.34和一个阻力位$629.91。展望层分析中，BOA显示市场整体情绪中性，但META和Dyne Therapeutics (DYN) 等公司具有上涨潜力。BeOA则认为META存在下跌风险，但NOA认为市场不确定性和观望理由较为充分。综合分析，各层级信号基本一致，分歧度较低，因此认为买入META是一个合理的选择。**最终决策：买入**', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-10'}
2025-07-28 21:58:49,828 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-28 21:58:49,828 - __main__ - INFO - 📈 第3天交易动作: {'META': 1.0}
2025-07-28 21:58:49,831 - __main__ - INFO - 💰 第3天净值变化: 收益率=-0.0049, 净值=$989694.26
2025-07-28 21:58:49,835 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 21:59:16,773 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 21:59:16,775 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 21:59:52,944 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 21:59:52,946 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 21:59:52,946 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-13, 累计收益=-0.0103, 周收益=-0.0103
2025-07-28 22:00:06,058 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 22:00:06,060 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 22:00:06,060 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 22:00:06,060 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 22:00:06,060 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 22:00:06,061 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 22:00:06,061 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 22:00:06,061 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在2025-01-13的分析中，我们对META股票进行了全面的多维度分析。首先，从基础面来看，FAA基本面分析显示META公司财务健康，估值合理，且在NAA新闻分析中，由于缺乏相关新闻，我们无法评估市场情绪变化，但根据逻辑推断，没有重大负面消息。技术面分析中，TAA显示META股票处于上升趋势，存在明确的支撑位和阻力位，技术评分较高，分析信心度也较高。展望层分析中，BOA看涨信号强于BeOA看跌信号，NOA认为市场展望中性。综合考虑各层级信号，我们发现基础面和技术面均支持看涨，展望层也倾向于看涨，尽管存在中性观点。风险评估显示信号一致性较高，潜在风险可控。因此，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-13'}
2025-07-28 22:00:06,061 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-28 22:00:06,061 - __main__ - INFO - 📈 第4天交易动作: {'META': 1.0}
2025-07-28 22:00:06,065 - __main__ - INFO - 💰 第4天净值变化: 收益率=-0.0400, 净值=$950117.46
2025-07-28 22:00:06,068 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 22:00:22,138 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 22:00:22,138 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 22:00:34,267 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 22:00:34,268 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 22:00:34,268 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-14, 累计收益=-0.0499, 周收益=-0.0499
2025-07-28 22:00:53,097 - __main__ - INFO - 🎯 找到最终决策声明: sell
2025-07-28 22:00:53,098 - __main__ - WARNING - 修正决策不一致: 声明sell vs 字段hold
2025-07-28 22:00:53,098 - __main__ - INFO - 一致性修正：hold -> sell
2025-07-28 22:00:53,098 - __main__ - INFO - 🤖 TRA 输出: action=sell, position_size=1.0
2025-07-28 22:00:53,098 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: sell (信心度: 0.00)
2025-07-28 22:00:53,098 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 22:00:53,098 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 22:00:53,098 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对基础面进行了评估。FAA基本面分析显示META公司的财务健康度良好，估值合理，且在竞争中处于领先地位。然而，由于NAA新闻分析没有发现重大负面消息，我们无法对市场情绪进行深入分析。接下来，我们进行了技术面分析，TAA技术分析显示META股票价格在2025-01-14呈现看跌趋势，支撑位可能在$590.00左右，阻力位可能在$600.00左右。尽管RSI值低于50，表明市场可能处于超卖状态，但MACD线和移动平均线的趋势表明可能存在看跌趋势。展望层智能体中，BOA看涨信号强度较弱，而BeOA看跌信号强度较强，NOA则认为市场展望中性。在风险评估方面，我们注意到各层级信号存在一定分歧，特别是基础面和技术面方向相反。因此，综合考虑，我们得出**最终决策：卖出**。', 'action': 'sell', 'trading_actions': {'META': -1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-14'}
2025-07-28 22:00:53,098 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': -1.0}
2025-07-28 22:00:53,099 - __main__ - INFO - 📈 第5天交易动作: {'META': -1.0}
2025-07-28 22:00:53,101 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-28 22:00:53,102 - __main__ - INFO - ============================================================
2025-07-28 22:00:53,102 - __main__ - INFO - 第 2 周性能分析 - 联盟: frozenset({'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'})
2025-07-28 22:00:53,102 - __main__ - INFO - 周总收益率: -0.0503
2025-07-28 22:00:53,102 - __main__ - INFO - 周夏普比率: -10.4196
2025-07-28 22:00:53,102 - __main__ - INFO - 交易天数: 5
2025-07-28 22:00:53,102 - __main__ - INFO - ============================================================
2025-07-28 22:32:45,265 - __main__ - INFO - ======================================================================
2025-07-28 22:32:45,265 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-28 22:32:45,265 - __main__ - INFO - ======================================================================
2025-07-28 22:32:45,265 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-28 22:32:45,266 - __main__ - INFO - 开始模拟联盟: {'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'}
2025-07-28 22:32:45,266 - __main__ - INFO - 📅 当前周数: 3
2025-07-28 22:32:45,266 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-28 22:32:45,266 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'})
2025-07-28 22:32:45,267 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-01-15 (第3周)
2025-07-28 22:32:45,267 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-01-15, end_date=2025-01-22
2025-07-28 22:32:45,267 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-28 22:32:45,353 - __main__ - INFO - 📅 设置当前模拟周数: 3 (来自参数)
2025-07-28 22:32:45,355 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 22:33:01,975 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 22:33:01,976 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 22:33:24,725 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 22:33:24,726 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 22:33:24,726 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-15, 累计收益=0.0000, 周收益=0.0000
2025-07-28 22:33:29,541 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 22:33:29,542 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 22:33:29,542 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 22:33:29,542 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 22:33:29,542 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 22:33:29,542 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 22:33:29,542 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 22:33:29,542 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对META股票进行了全面的多维度分析。基础面分析显示，FAA给出了财务健康度评分8.5，表明公司财务状况良好，估值评估为合理，竞争地位领先。NAA由于缺乏相关新闻，无法进行市场新闻和舆论情绪的分析，但根据TAA的技术分析，META股票显示出看涨的趋势，支撑位在$606.5附近，阻力位在$622.46附近。技术指标如RSI、MACD和移动平均线均支持这一趋势。展望层分析中，BOA给出了看涨的市场展望，而BeOA则从风险和潜在下行风险的角度出发，提出了看跌的观点。NOA则保持中性，认为市场情绪和趋势分析较为谨慎。在风险评估方面，各层级信号基本一致，分歧度较低，市场环境不确定性较高。综合以上分析，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-15'}
2025-07-28 22:33:29,542 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-28 22:33:29,542 - __main__ - INFO - 📈 第1天交易动作: {'META': 1.0}
2025-07-28 22:33:29,544 - __main__ - INFO - 💰 第1天净值变化: 收益率=0.0121, 净值=$1012067.10
2025-07-28 22:33:29,545 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 22:33:52,822 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 22:33:52,823 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 22:34:20,112 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 22:34:20,112 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 22:34:20,112 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-16, 累计收益=0.0121, 周收益=0.0121
2025-07-28 22:34:33,659 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 22:34:33,659 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 22:34:33,659 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 22:34:33,659 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 22:34:33,659 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 22:34:33,659 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 22:34:33,659 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 22:34:33,659 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对基础面进行了评估。根据FAA的基本面分析，公司财务健康度良好，估值合理，显示出一定的投资价值。然而，NAA的新闻分析显示，目前没有重大负面消息，但也没有明显的正面新闻，市场情绪较为稳定。在技术面分析中，TAA显示股票价格呈现一定的下跌趋势，动量指标显示中性或略微看跌，支撑位和阻力位分析表明短期内可能存在一定的支撑。展望层智能体中，BOA看涨信号强度较弱，BeOA看跌信号强度也较弱，NOA则认为市场表现出一定的中性倾向。在风险评估方面，各层级信号存在一定分歧，但整体风险可控。综合以上分析，**最终决策：买入**。我们建议在当前市场环境下，可以适当买入，但需注意风险控制。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-16'}
2025-07-28 22:34:33,660 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-28 22:34:33,660 - __main__ - INFO - 📈 第2天交易动作: {'META': 1.0}
2025-07-28 22:34:33,664 - __main__ - INFO - 💰 第2天净值变化: 收益率=0.0079, 净值=$1020057.72
2025-07-28 22:34:33,667 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 22:35:30,361 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 22:35:30,363 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 22:35:53,177 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 22:35:53,179 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 22:35:53,179 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-17, 累计收益=0.0201, 周收益=0.0201
2025-07-28 22:36:09,852 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 22:36:09,852 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 22:36:09,852 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 22:36:09,852 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 22:36:09,852 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 22:36:09,852 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 22:36:09,852 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 22:36:09,852 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对基础面进行了评估。根据FAA基本面分析，META公司的财务健康度良好，估值合理，且在竞争地位上处于领先。NAA新闻分析显示，虽然存在一些法律诉讼相关的负面消息，但整体市场情绪较为悲观，这可能对META公司产生一定影响。在技术面分析中，TAA显示META公司股票在1月17日显示出一定的下跌趋势，但整体趋势仍呈上升趋势。展望层智能体中，BOA看涨信号强度较高，预计市场将实现显著增长，而BeOA看跌信号强度较低，NOA则认为市场存在不确定性。综合考虑各层级信息，我们得出以下结论：基础面和技术面显示META公司具有良好的投资价值，展望层智能体信号也支持这一观点。因此，我们建议买入META公司股票。**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-17'}
2025-07-28 22:36:09,852 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-28 22:36:09,852 - __main__ - INFO - 📈 第3天交易动作: {'META': 1.0}
2025-07-28 22:36:09,854 - __main__ - INFO - 💰 第3天净值变化: 收益率=-0.0043, 净值=$1015674.60
2025-07-28 22:36:09,855 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 22:36:09,855 - __main__ - INFO - TAA检测到无价格数据，建议跳过当前日期: 2025-01-20
2025-07-28 22:36:14,452 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 22:36:14,454 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 22:36:35,813 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 22:36:35,815 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 22:36:35,815 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-20, 累计收益=0.0157, 周收益=0.0157
2025-07-28 22:36:52,298 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 22:36:52,301 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 22:36:52,301 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 22:36:52,301 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 22:36:52,301 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 22:36:52,301 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 22:36:52,301 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 22:36:52,301 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析日期: 2025-01-20\n\n基础面评估：根据FAA的基本面分析，公司财务健康度良好，得分为8.5，估值合理，竞争地位领先。NAA由于缺乏具体新闻和事件，无法进行市场新闻和舆论情绪的分析，因此情绪评分、新闻摘要、关键事件列表和影响评估均为无。综合来看，基础面表现稳健。\n\n技术面分析：TAA由于无法进行技术分析，建议跳过。因此，技术面分析无法提供具体信号。\n\n展望层综合：BOA基于分析层智能体的输出，给出了看涨展望，认为市场环境缺乏具体新闻和事件，但基于财务健康和竞争地位等内部因素，看涨概率较高。BeOA由于缺乏具体新闻和事件，无法进行具体分析。NOA认为市场当前处于中性状态，市场展望保持中性。综合来看，展望层智能体信号偏向中性偏乐观。\n\n风险评估：由于缺乏具体的技术面分析，无法评估技术风险。基础面和展望层智能体信号基本一致，但风险控制方面需要考虑市场环境和流动性风险。\n\n最终决策：买入\n\n理由：基础面稳健，展望层智能体信号偏向中性偏乐观，尽管技术面分析无法提供具体信号，但整体分析结果支持买入决策。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-20'}
2025-07-28 22:36:52,301 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-28 22:36:52,301 - __main__ - INFO - 📈 第4天交易动作: {'META': 1.0}
2025-07-28 22:36:52,307 - __main__ - INFO - 💰 第4天净值变化: 收益率=-0.0070, 净值=$1008549.78
2025-07-28 22:36:52,310 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 22:37:06,255 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 22:37:06,255 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 22:37:34,671 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 22:37:34,672 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 22:37:34,672 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-21, 累计收益=0.0085, 周收益=0.0085
2025-07-28 22:37:46,516 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 22:37:46,517 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 22:37:46,517 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 22:37:46,517 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 22:37:46,517 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 22:37:46,517 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '```json\n{\n  "analysis": "在本次分析中，我们首先对基础面进行了评估。根据FAA的基本面分析，META公司的财务健康度得分较高，显示出良好的财务状况。同时，NAA的新闻分析显示，目前没有重大负面消息影响META公司。这表明基础面较为稳健。\n\n接下来，我们进行了技术面分析。TAA的技术分析结果显示，META股票在2025-01-21呈现上涨趋势，RSI和MACD指标均显示出多头信号，移动平均线也显示出短期趋势强于长期趋势。这些指标共同支持了技术面看涨的结论。\n\n在展望层综合分析中，BOA的看涨信号强度较高，预计META股票在未来一段时间内将呈现上涨趋势。尽管BeOA提出了一些看跌因素，但整体来看，BOA的信号强于BeOA。NOA的中性观点也支持了上涨概率较高的结论。\n\n在风险评估方面，各层级信号基本一致，分歧度较低。市场环境和流动性风险也在可控范围内。\n\n综上所述，基于基础面、技术面、展望层综合分析和风险评估，我们得出**最终决策：买入**META股票的结论。",\n  "action": "buy"\n}\n```', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-21'}
2025-07-28 22:37:46,517 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-28 22:37:46,517 - __main__ - INFO - 📈 第5天交易动作: {'META': 1.0}
2025-07-28 22:37:46,519 - __main__ - INFO - 💰 第5天净值变化: 收益率=0.0107, 净值=$1019318.18
2025-07-28 22:37:46,519 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-28 22:37:46,520 - __main__ - INFO - ============================================================
2025-07-28 22:37:46,520 - __main__ - INFO - 第 3 周性能分析 - 联盟: frozenset({'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'})
2025-07-28 22:37:46,520 - __main__ - INFO - 周总收益率: 0.0193
2025-07-28 22:37:46,520 - __main__ - INFO - 周夏普比率: 7.5734
2025-07-28 22:37:46,520 - __main__ - INFO - 交易天数: 5
2025-07-28 22:37:46,520 - __main__ - INFO - ============================================================
2025-07-28 23:10:18,153 - __main__ - INFO - ======================================================================
2025-07-28 23:10:18,153 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-28 23:10:18,153 - __main__ - INFO - ======================================================================
2025-07-28 23:10:18,153 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-28 23:10:18,153 - __main__ - INFO - 开始模拟联盟: {'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'}
2025-07-28 23:10:18,153 - __main__ - INFO - 📅 当前周数: 4
2025-07-28 23:10:18,153 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-28 23:10:18,153 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'})
2025-07-28 23:10:18,154 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-01-22 (第4周)
2025-07-28 23:10:18,155 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-01-22, end_date=2025-01-29
2025-07-28 23:10:18,155 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-28 23:10:18,244 - __main__ - INFO - 📅 设置当前模拟周数: 4 (来自参数)
2025-07-28 23:10:18,245 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 23:10:58,208 - __main__ - ERROR - LLM分析请求失败: Error code: 400, with error text {"contentFilter":[{"level":1,"role":"assistant"}],"error":{"code":"1301","message":"系统检测到输入或生成内容可能包含不安全或敏感内容，请您避免输入易产生敏感内容的提示语，感谢您的配合。"}}
2025-07-28 23:10:58,210 - __main__ - WARNING - 🤖 NAA: 数据验证失败，使用默认响应
2025-07-28 23:10:58,210 - __main__ - ERROR - 智能体 NAA LLM调用失败: the JSON object must be str, bytes or bytearray, not NoneType
2025-07-28 23:10:58,211 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 23:10:58,211 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 23:11:24,489 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 23:11:24,490 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 23:11:24,490 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-22, 累计收益=0.0000, 周收益=0.0000
2025-07-28 23:11:43,213 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 23:11:43,214 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 23:11:43,214 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 23:11:43,214 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 23:11:43,214 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 23:11:43,214 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 23:11:43,214 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 23:11:43,214 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对META股票进行了全面的基础面、技术面和展望层分析。\n\n**基础面评估**：根据FAA的基本面分析，META的财务健康度良好，估值合理，且在竞争中处于领先地位。NAA的新闻分析显示，目前没有重大负面消息影响META。因此，基础面评估结果为正面。\n\n**技术面分析**：TAA的技术分析结果显示，META股票表现出明显的上升趋势，支撑位可能在$619.435附近，阻力位可能在$633.7附近。RSI值在60以上，表明市场接近超买但未达到极端水平，MACD和移动平均线均支持看涨趋势。技术评分和信心度较高，为0.8。\n\n**展望层综合**：BOA的看涨展望分析认为，市场对META股票的看涨因素包括技术分析指标的支持和接近超买但未达到极端的市场状态。BeOA的看跌展望分析由于LLM不可用，我们使用了默认分析，但根据TAA的结果，看跌信号并不强烈。NOA的中性展望分析指出，尽管技术分析显示看涨趋势，但市场情绪可能受到其他因素的影响。因此，展望层综合结果为看涨。\n\n**风险评估**：各层级信号基本一致，分歧度较低。市场环境和流动性风险目前未对META股票产生显著影响。\n\n**最终决策**：基于上述分析，我们得出结论，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-22'}
2025-07-28 23:11:43,215 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-28 23:11:43,215 - __main__ - INFO - 📈 第1天交易动作: {'META': 1.0}
2025-07-28 23:11:43,217 - __main__ - INFO - 💰 第1天净值变化: 收益率=0.0149, 净值=$1014917.45
2025-07-28 23:11:43,218 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 23:12:06,278 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 23:12:06,278 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 23:12:28,922 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 23:12:28,924 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 23:12:28,924 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-23, 累计收益=0.0149, 周收益=0.0149
2025-07-28 23:12:45,495 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 23:12:45,496 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 23:12:45,496 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 23:12:45,496 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 23:12:45,496 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 23:12:45,496 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 23:12:45,496 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 23:12:45,496 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对基础面进行了评估。根据FAA和NAA的分析结果，我们发现META公司的财务健康度良好，估值合理，且近期无重大负面消息。这表明基础面支持买入决策。接着，我们进行了技术面分析，TAA显示META股票处于上升趋势，且技术指标如RSI、MACD和移动平均线均支持这一趋势。展望层智能体中，BOA显示看涨信号，而BeOA和NOA则提供了中性展望。这表明市场整体情绪偏向乐观，但存在一定的不确定性。在风险评估方面，我们发现各层级信号基本一致，分歧度较低，且市场环境相对稳定。因此，基于以上分析，我们得出**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-23'}
2025-07-28 23:12:45,496 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-28 23:12:45,496 - __main__ - INFO - 📈 第2天交易动作: {'META': 1.0}
2025-07-28 23:12:45,498 - __main__ - INFO - 💰 第2天净值变化: 收益率=-0.0076, 净值=$1007237.53
2025-07-28 23:12:45,501 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 23:13:06,996 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 23:13:06,997 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 23:13:33,115 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 23:13:33,115 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 23:13:33,115 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-24, 累计收益=0.0072, 周收益=0.0072
2025-07-28 23:13:46,377 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 23:13:46,379 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 23:13:46,379 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 23:13:46,379 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 23:13:46,379 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 23:13:46,379 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 23:13:46,379 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 23:13:46,379 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对基础面进行了评估。FAA基本面分析显示META公司财务健康度良好，估值合理，且在竞争中处于领先地位。NAA新闻分析表明，市场新闻情绪以中性为主，但积极情绪来源于公司业绩、新合同签订和技术进步等方面。这些积极因素对META公司有利。接着，我们进行了技术面分析。TAA技术分析显示META股票价格呈现上升趋势，且技术指标如RSI和MACD均支持看涨趋势。展望层智能体中，BOA看涨信号强度较高，而BeOA看跌信号较弱，NOA认为市场上涨概率较高。在风险评估方面，各层级信号基本一致，分歧度较低。因此，综合考虑各层级信息，我们得出**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-24'}
2025-07-28 23:13:46,379 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-28 23:13:46,379 - __main__ - INFO - 📈 第3天交易动作: {'META': 1.0}
2025-07-28 23:13:46,383 - __main__ - INFO - 💰 第3天净值变化: 收益率=0.0090, 净值=$1016297.71
2025-07-28 23:13:46,385 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 23:14:16,142 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 23:14:16,142 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 23:14:47,498 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 23:14:47,499 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 23:14:47,499 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-27, 累计收益=0.0163, 周收益=0.0163
2025-07-28 23:14:51,248 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 23:14:51,248 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 23:14:51,248 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 23:14:51,248 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 23:14:51,248 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 23:14:51,248 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 23:14:51,248 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 23:14:51,248 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对基础面进行了评估。根据FAA基本面分析，公司财务健康度良好，估值处于合理水平。NAA新闻分析显示，尽管DeepSeek AI模型的出现引发了一些市场担忧，但Meta Platforms和Nvidia的股价上涨以及医疗和科技市场的增长预测为市场带来了一些积极因素。在技术面分析中，TAA技术分析显示股票价格呈现上升趋势，动量强劲，且存在明显的支撑位和阻力位。展望层智能体中，BOA看涨信号强度较高，BeOA看跌信号较弱，NOA认为市场上涨概率较高。在风险评估方面，各层级信号基本一致，分歧度较低。因此，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-27'}
2025-07-28 23:14:51,248 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-28 23:14:51,248 - __main__ - INFO - 📈 第4天交易动作: {'META': 1.0}
2025-07-28 23:14:51,251 - __main__ - INFO - 💰 第4天净值变化: 收益率=0.0322, 净值=$1049052.40
2025-07-28 23:14:51,252 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 23:15:25,639 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 23:15:25,639 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 23:16:00,599 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 23:16:00,601 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 23:16:00,601 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-28, 累计收益=0.0491, 周收益=0.0491
2025-07-28 23:16:13,669 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 23:16:13,670 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 23:16:13,670 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 23:16:13,670 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 23:16:13,670 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 23:16:13,670 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 23:16:13,670 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 23:16:13,670 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在基础面评估中，FAA显示Meta Platforms的财务健康度良好，且估值合理。NAA分析显示，尽管市场对DeepSeek的技术突破反应过度，但Meta Platforms的AI投资计划和Microsoft与TikTok的收购谈判等积极信号对市场情绪有正面影响。在技术面分析中，TAA显示Meta Platforms股票价格表现出看涨趋势，且技术指标支持这一观点。展望层分析中，BOA看涨信号强于BeOA看跌信号，NOA认为上涨概率较高。综合各层级信号，基础面稳健，技术面看涨，展望层信号支持上涨，风险控制方面，各层级信号基本一致。因此，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-28'}
2025-07-28 23:16:13,670 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-28 23:16:13,671 - __main__ - INFO - 📈 第5天交易动作: {'META': 1.0}
2025-07-28 23:16:13,674 - __main__ - INFO - 💰 第5天净值变化: 收益率=0.0351, 净值=$1085894.78
2025-07-28 23:16:13,675 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-28 23:16:13,675 - __main__ - INFO - ============================================================
2025-07-28 23:16:13,675 - __main__ - INFO - 第 4 周性能分析 - 联盟: frozenset({'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'})
2025-07-28 23:16:13,675 - __main__ - INFO - 周总收益率: 0.0837
2025-07-28 23:16:13,675 - __main__ - INFO - 周夏普比率: 16.8487
2025-07-28 23:16:13,675 - __main__ - INFO - 交易天数: 5
2025-07-28 23:16:13,675 - __main__ - INFO - ============================================================
2025-07-28 23:49:26,846 - __main__ - INFO - ======================================================================
2025-07-28 23:49:26,846 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-28 23:49:26,846 - __main__ - INFO - ======================================================================
2025-07-28 23:49:26,846 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-28 23:49:26,847 - __main__ - INFO - 开始模拟联盟: {'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'}
2025-07-28 23:49:26,847 - __main__ - INFO - 📅 当前周数: 5
2025-07-28 23:49:26,847 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-28 23:49:26,847 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'})
2025-07-28 23:49:26,848 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-01-29 (第5周)
2025-07-28 23:49:26,849 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-01-29, end_date=2025-02-05
2025-07-28 23:49:26,849 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-28 23:49:26,947 - __main__ - INFO - 📅 设置当前模拟周数: 5 (来自参数)
2025-07-28 23:49:26,948 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 23:49:58,953 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 23:49:58,953 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 23:50:29,912 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 23:50:29,912 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 23:50:29,912 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-29, 累计收益=0.0000, 周收益=0.0000
2025-07-28 23:50:37,392 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 23:50:37,393 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 23:50:37,393 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 23:50:37,394 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 23:50:37,394 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 23:50:37,394 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 23:50:37,394 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 23:50:37,394 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对基础面进行了评估。FAA基本面分析显示，Meta Platforms的财务健康度良好，估值合理，且在竞争地位上处于领先。同时，NAA新闻分析显示，尽管有证券诉讼调查等负面消息，但整体市场情绪保持中性，且Meta Platforms发布了超出预期的业绩报告，这表明公司基本面稳健。接下来，我们进行了技术面分析。TAA技术分析显示，股票价格呈现上升趋势，成交量增加，表明市场参与度提高。此外，技术指标如RSI接近70，MACD线在信号线上方，短期移动平均线在长期移动平均线上方，均支持上升趋势。在展望层综合分析中，BOA看涨信号强度较高，BeOA看跌信号较弱，NOA认为上涨概率较高。最后，在风险评估中，我们注意到各层级信号基本一致，分歧度较低。综合以上分析，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-29'}
2025-07-28 23:50:37,394 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-28 23:50:37,394 - __main__ - INFO - 📈 第1天交易动作: {'META': 1.0}
2025-07-28 23:50:37,396 - __main__ - INFO - 💰 第1天净值变化: 收益率=0.0351, 净值=$1035119.68
2025-07-28 23:50:37,398 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 23:51:02,215 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 23:51:02,216 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 23:51:38,283 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 23:51:38,285 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 23:51:38,285 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-30, 累计收益=0.0351, 周收益=0.0351
2025-07-28 23:52:01,743 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 23:52:01,744 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 23:52:01,744 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 23:52:01,744 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 23:52:01,744 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 23:52:01,744 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 23:52:01,744 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 23:52:01,744 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '详细的分析过程，包括展望层智能体信号分析、决策理由、风险评估等，最后必须明确声明：**最终决策：买入/卖出/保持不变**\n\n### 基础面评估\n\n- **FAA基本面分析**：Meta Platforms（META）的财务健康度良好，估值合理，显示出稳健的基本面。NAA显示，Meta在2024年第四季度业绩超出预期，对股价产生积极影响。尽管面临挑战，但Meta对2025年的支出计划持乐观态度，这表明公司对未来发展有信心。\n- **NAA新闻分析**：Meta Platforms（META）的业绩超出预期，对股价产生积极影响。此外，Meta在AI领域的投资和扩张计划也受到市场关注，可能对股价产生长期影响。\n\n### 技术面分析\n\n- **TAA技术分析**：股价呈现上升趋势，动量增强。支撑位在$676.1，阻力位在$710.79。技术指标如RSI和MACD显示出上升趋势，但需注意RSI接近70，可能存在回调风险。\n\n### 展望层综合\n\n- **BOA看涨信号强度**：市场对Meta Platforms（META）的展望是看涨的，看涨因素包括业绩超出预期和AI领域的投资。\n- **BeOA看跌信号强度**：目前没有明显的看跌信号。\n- **NOA中性观点**：市场对Meta Platforms（META）的看法较为中性，但整体趋势偏向看涨。\n\n### 风险评估\n\n- 信号一致性：各层级信号基本一致，倾向于看涨。\n- 潜在风险：需关注RSI接近70可能带来的回调风险。\n\n**最终决策：买入**\n\n基于以上分析，Meta Platforms（META）显示出良好的基本面、技术面和展望层信号，且风险可控，因此建议买入。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-30'}
2025-07-28 23:52:01,745 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-28 23:52:01,745 - __main__ - INFO - 📈 第2天交易动作: {'META': 1.0}
2025-07-28 23:52:01,751 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 23:52:24,718 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 23:52:24,718 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 23:53:20,182 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 23:53:20,183 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 23:53:20,183 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-31, 累计收益=0.0353, 周收益=0.0353
2025-07-28 23:53:33,403 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 23:53:33,403 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 23:53:33,403 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 23:53:33,403 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 23:53:33,403 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 23:53:33,403 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 23:53:33,403 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 23:53:33,403 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对基础面进行了评估。FAA基本面分析显示，Meta Platforms的财务健康度良好，估值合理，其竞争优势处于领先地位。NAA新闻分析表明，近期市场新闻以中性为主，部分新闻带有略微乐观的情绪，如Meta Platforms和Microsoft的业绩报告，以及6G市场规模的预测。这些积极因素对Meta Platforms的股价产生了一定的正面影响。技术面分析显示，TAA技术分析结果显示股票价格呈现上升趋势，动量强劲，支撑位和阻力位明确，技术指标信号也显示出上升趋势。展望层分析中，BOA看涨信号强度较高，认为市场展现出积极的迹象。然而，BeOA看跌信号强度较低，NOA则认为市场不确定性和观望理由存在。在风险评估方面，各层级信号基本一致，分歧度较低，市场环境和流动性风险可控。综合以上分析，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-31'}
2025-07-28 23:53:33,403 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-28 23:53:33,403 - __main__ - INFO - 📈 第3天交易动作: {'META': 1.0}
2025-07-28 23:53:33,406 - __main__ - INFO - 💰 第3天净值变化: 收益率=-0.0079, 净值=$1027157.87
2025-07-28 23:53:33,407 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 23:54:04,507 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 23:54:04,507 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 23:54:27,927 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 23:54:27,928 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 23:54:27,928 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-03, 累计收益=0.0272, 周收益=0.0272
2025-07-28 23:54:50,573 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 23:54:50,574 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 23:54:50,574 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 23:54:50,574 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 23:54:50,574 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 23:54:50,574 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 23:54:50,574 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 23:54:50,574 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '详细的分析过程，包括展望层智能体信号分析、决策理由、风险评估等，最后必须明确声明：**最终决策：买入/卖出/保持不变**\n\n### 基础面评估\n- FAA基本面分析显示，META公司的财务健康度良好，估值合理，竞争地位领先。NAA新闻分析显示，今日没有相关市场新闻，因此无法进行情绪分析和影响评估，但根据历史经验，目前没有重大负面消息。\n\n### 技术面分析\n- TAA技术分析显示，META股票价格呈现上升趋势，支撑位在$675.00附近，阻力位在$700.00附近。技术评分设定为0.5，RSI可能轻微超买，MACD和移动平均线支持看涨趋势。\n\n### 展望层综合\n- BOA看涨信号强度较高，基于当前的市场趋势和财务健康评分，市场展望为看涨。BeOA看跌信号较弱，NOA认为上涨概率较高。\n\n### 风险评估\n- 各层级信号基本一致，分歧度较低。市场环境不确定性较高，但无重大负面消息。\n\n### 最终决策\n**最终决策：买入**。由于基础面稳健，技术面看涨，展望层信号支持上涨，且风险控制措施得当，因此建议买入META股票。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-03'}
2025-07-28 23:54:50,574 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-28 23:54:50,574 - __main__ - INFO - 📈 第4天交易动作: {'META': 1.0}
2025-07-28 23:54:50,577 - __main__ - INFO - 💰 第4天净值变化: 收益率=0.0244, 净值=$1052190.34
2025-07-28 23:54:50,578 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 23:55:12,620 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 23:55:12,621 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 23:55:40,807 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 23:55:40,807 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 23:55:40,807 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-04, 累计收益=0.0522, 周收益=0.0522
2025-07-28 23:55:52,458 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 23:55:52,459 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 23:55:52,459 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 23:55:52,459 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 23:55:52,459 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 23:55:52,459 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 23:55:52,459 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 23:55:52,459 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '详细的分析过程，包括展望层智能体信号分析、决策理由、风险评估等，最后必须明确声明：**最终决策：买入/卖出/保持不变**\n\n分析过程：\n\n1. 基础面评估：根据FAA和NAA的分析结果，公司财务健康度良好，估值合理。NAA显示目前没有重大负面消息，市场情绪稳定。\n\n2. 技术面分析：TAA显示股票价格短期内呈现上升趋势，支撑位在$691.61，阻力位在$710.79。技术评分0.5，分析信心度0.8。\n\n3. 展望层综合：BOA显示市场展望为看涨，BeOA没有明显的看跌信号，NOA认为市场目前处于中性状态。\n\n4. 风险评估：各层级信号基本一致，分歧度较低。市场环境稳定，流动性风险可控。\n\n最终决策：买入\n\n决策理由：基础面和技术面均显示公司财务状况良好，市场情绪稳定，技术面显示上升趋势，展望层智能体信号一致支持看涨。\n\n风险评估：各层级信号一致性较高，市场环境稳定，风险可控。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-04'}
2025-07-28 23:55:52,459 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-28 23:55:52,459 - __main__ - INFO - 📈 第5天交易动作: {'META': 1.0}
2025-07-28 23:55:52,461 - __main__ - INFO - 💰 第5天净值变化: 收益率=-0.0164, 净值=$1034984.73
2025-07-28 23:55:52,461 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-28 23:55:52,461 - __main__ - INFO - ============================================================
2025-07-28 23:55:52,461 - __main__ - INFO - 第 5 周性能分析 - 联盟: frozenset({'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'})
2025-07-28 23:55:52,461 - __main__ - INFO - 周总收益率: 0.0354
2025-07-28 23:55:52,461 - __main__ - INFO - 周夏普比率: 5.7000
2025-07-28 23:55:52,461 - __main__ - INFO - 交易天数: 5
2025-07-28 23:55:52,461 - __main__ - INFO - ============================================================
2025-07-29 00:28:51,170 - __main__ - INFO - ======================================================================
2025-07-29 00:28:51,170 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-29 00:28:51,170 - __main__ - INFO - ======================================================================
2025-07-29 00:28:51,171 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-29 00:28:51,171 - __main__ - INFO - 开始模拟联盟: {'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'}
2025-07-29 00:28:51,171 - __main__ - INFO - 📅 当前周数: 6
2025-07-29 00:28:51,171 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-29 00:28:51,171 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'})
2025-07-29 00:28:51,172 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-02-05 (第6周)
2025-07-29 00:28:51,172 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-02-05, end_date=2025-02-12
2025-07-29 00:28:51,172 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-29 00:28:51,256 - __main__ - INFO - 📅 设置当前模拟周数: 6 (来自参数)
2025-07-29 00:28:51,257 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 00:29:10,997 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 00:29:10,998 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 00:29:34,482 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 00:29:34,483 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 00:29:34,483 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-05, 累计收益=0.0000, 周收益=0.0000
2025-07-29 00:29:45,485 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 00:29:45,485 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 00:29:45,485 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 00:29:45,485 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 00:29:45,485 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 00:29:45,485 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 00:29:45,485 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 00:29:45,485 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对基础面进行了评估。根据FAA的分析，META公司的财务健康度良好，估值合理，且在竞争地位上处于领先。同时，NAA的分析显示，目前没有重大负面新闻影响META公司。这表明基础面支持买入决策。\n\n接下来，我们进行了技术面分析。TAA的分析表明，META股票价格呈现上升趋势，且动量强劲。支撑位和阻力位的分析也支持这一趋势。技术指标如RSI、MACD和移动平均线均显示出上升趋势，尽管RSI接近70，表明可能存在超买风险，但整体技术面仍然看涨。\n\n展望层智能体的分析中，BOA显示看涨信号强于BeOA，而NOA认为上涨概率较高。这进一步支持了我们的买入决策。\n\n在风险评估方面，我们注意到各层级信号基本一致，分歧度较低。因此，我们得出结论，当前市场环境和技术指标均支持买入META股票。\n\n**最终决策：买入**', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-05'}
2025-07-29 00:29:45,485 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 00:29:45,486 - __main__ - INFO - 📈 第1天交易动作: {'META': 1.0}
2025-07-29 00:29:45,488 - __main__ - INFO - 💰 第1天净值变化: 收益率=0.0094, 净值=$1009371.38
2025-07-29 00:29:45,489 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 00:30:04,572 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 00:30:04,573 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 00:30:27,021 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 00:30:27,024 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 00:30:27,024 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-06, 累计收益=0.0094, 周收益=0.0094
2025-07-29 00:30:30,437 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 00:30:30,437 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 00:30:30,437 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 00:30:30,437 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 00:30:30,438 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 00:30:30,438 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 00:30:30,438 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 00:30:30,438 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对基础面进行了评估。FAA基本面分析显示公司财务健康度良好，估值合理，且在NAA新闻分析中未发现重大负面消息，表明基础面稳健。接着，TAA技术分析显示股票价格呈现上升趋势，支撑位和阻力位明确，技术指标如RSI、MACD和移动平均线均显示看涨信号，技术面分析结果支持买入。展望层智能体中，BOA看涨信号强于BeOA看跌信号，NOA也认为上涨概率较高，这进一步支持了买入决策。在风险评估方面，各层级信号基本一致，分歧度较低，市场环境不确定性不高。因此，综合以上分析，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-06'}
2025-07-29 00:30:30,438 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 00:30:30,438 - __main__ - INFO - 📈 第2天交易动作: {'META': 1.0}
2025-07-29 00:30:30,444 - __main__ - INFO - 💰 第2天净值变化: 收益率=0.0216, 净值=$1031145.59
2025-07-29 00:30:30,447 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 00:30:56,844 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 00:30:56,845 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 00:31:23,493 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 00:31:23,493 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 00:31:23,493 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-07, 累计收益=0.0311, 周收益=0.0311
2025-07-29 00:31:28,003 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 00:31:28,005 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 00:31:28,005 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 00:31:28,005 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 00:31:28,006 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 00:31:28,006 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 00:31:28,006 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 00:31:28,006 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对基础面进行了评估。根据FAA和NAA的分析结果，Meta Platforms, Inc. (META) 的基本面稳健，财务健康度良好，估值合理。同时，NAA指出META的CEO访问白宫可能对股价产生积极影响，Pinterest Inc. (PINS) 的强劲营收增长也可能对META产生积极影响。在技术面分析中，TAA显示META股票价格呈现上升趋势，动量强劲，支撑位和阻力位明确，技术指标如RSI和MACD均支持上升趋势。展望层智能体中，BOA看涨信号强于BeOA，NOA认为市场展望为中性，但上涨概率较高。在风险评估方面，各层级信号基本一致，分歧度较低。因此，综合考虑各层级信息，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-07'}
2025-07-29 00:31:28,006 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 00:31:28,006 - __main__ - INFO - 📈 第3天交易动作: {'META': 1.0}
2025-07-29 00:31:28,008 - __main__ - INFO - 💰 第3天净值变化: 收益率=-0.0030, 净值=$1028065.26
2025-07-29 00:31:28,010 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 00:32:02,786 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 00:32:02,787 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 00:32:23,569 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 00:32:23,575 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 00:32:23,575 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-10, 累计收益=0.0281, 周收益=0.0281
2025-07-29 00:32:28,467 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 00:32:28,467 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 00:32:28,468 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 00:32:28,468 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 00:32:28,468 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 00:32:28,468 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 00:32:28,468 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 00:32:28,468 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对基础面进行了评估。FAA基本面分析显示公司财务健康度良好，估值合理，且在NAA新闻分析中未发现重大负面消息，这表明基础面稳健。接着，我们进行了技术面分析，TAA技术分析显示股票价格呈现上升趋势，且关键技术指标如RSI、MACD和移动平均线均显示出看涨信号，技术评分达到0.6，分析信心度为0.8。展望层智能体中，BOA看涨信号强度较高，预计市场将持续看涨。然而，BeOA看跌信号强度较低，NOA则认为市场上涨概率较高。在风险评估方面，各层级信号基本一致，分歧度较低，市场环境不确定性不高。综合以上分析，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-10'}
2025-07-29 00:32:28,468 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 00:32:28,468 - __main__ - INFO - 📈 第4天交易动作: {'META': 1.0}
2025-07-29 00:32:28,470 - __main__ - INFO - 💰 第4天净值变化: 收益率=-0.0166, 净值=$1011050.08
2025-07-29 00:32:28,472 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 00:32:58,922 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 00:32:58,922 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 00:33:23,843 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 00:33:23,843 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 00:33:23,843 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-11, 累计收益=0.0111, 周收益=0.0111
2025-07-29 00:33:30,414 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 00:33:30,414 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 00:33:30,415 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 00:33:30,415 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 00:33:30,415 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 00:33:30,415 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 00:33:30,415 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 00:33:30,415 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对基础面进行了评估。根据FAA和NAA的分析结果，META公司的财务健康度良好，估值合理，且NAA未发现重大负面消息，因此基础面评估为正面。接着，我们进行了技术面分析，TAA显示META股票价格呈现上升趋势，动量强劲，支撑位和阻力位明确，技术指标如RSI、MACD和移动平均线均显示出看涨信号，技术评分设为0.8。展望层智能体中，BOA显示看涨信号，BeOA则提出市场可能处于超买状态，存在一定风险，NOA建议保持观望。在风险评估方面，我们注意到市场可能处于超买状态，但整体信号一致性较高。综合以上分析，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-11'}
2025-07-29 00:33:30,415 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 00:33:30,415 - __main__ - INFO - 📈 第5天交易动作: {'META': 1.0}
2025-07-29 00:33:30,417 - __main__ - INFO - 💰 第5天净值变化: 收益率=0.0063, 净值=$1017390.03
2025-07-29 00:33:30,417 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-29 00:33:30,418 - __main__ - INFO - ============================================================
2025-07-29 00:33:30,418 - __main__ - INFO - 第 6 周性能分析 - 联盟: frozenset({'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'})
2025-07-29 00:33:30,418 - __main__ - INFO - 周总收益率: 0.0177
2025-07-29 00:33:30,418 - __main__ - INFO - 周夏普比率: 4.3026
2025-07-29 00:33:30,418 - __main__ - INFO - 交易天数: 5
2025-07-29 00:33:30,418 - __main__ - INFO - ============================================================
2025-07-29 01:05:35,823 - __main__ - INFO - ======================================================================
2025-07-29 01:05:35,823 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-29 01:05:35,823 - __main__ - INFO - ======================================================================
2025-07-29 01:05:35,823 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-29 01:05:35,823 - __main__ - INFO - 开始模拟联盟: {'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'}
2025-07-29 01:05:35,823 - __main__ - INFO - 📅 当前周数: 7
2025-07-29 01:05:35,823 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-29 01:05:35,823 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'})
2025-07-29 01:05:35,824 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-02-12 (第7周)
2025-07-29 01:05:35,824 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-02-12, end_date=2025-02-19
2025-07-29 01:05:35,824 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-29 01:05:35,914 - __main__ - INFO - 📅 设置当前模拟周数: 7 (来自参数)
2025-07-29 01:05:35,915 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 01:05:56,505 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 01:05:56,507 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 01:06:18,097 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 01:06:18,097 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 01:06:18,097 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-12, 累计收益=0.0000, 周收益=0.0000
2025-07-29 01:06:26,409 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 01:06:26,410 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 01:06:26,410 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 01:06:26,410 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 01:06:26,410 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 01:06:26,410 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 01:06:26,411 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 01:06:26,411 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '今日分析过程如下：\n\n1. **基础面评估**：根据FAA的基本面分析，公司财务健康度良好，估值处于合理水平。NAA显示今日没有相关新闻，因此市场情绪稳定，无重大负面消息。综合来看，基础面支持买入决策。\n\n2. **技术面分析**：TAA显示股票价格呈现上升趋势，支撑位价格约为$712.6，阻力位价格约为$727.1。技术指标如RSI、MACD和移动平均线均显示出看涨信号，技术面支持买入决策。\n\n3. **展望层综合**：BOA显示市场看涨，BeOA显示市场看跌，但NOA认为市场展望中性。考虑到技术面的强劲看涨信号，我们倾向于相信BOA的看涨展望。\n\n4. **风险评估**：各层级信号基本一致，分歧度较低。市场环境稳定，流动性风险可控。\n\n**最终决策：买入**', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-12'}
2025-07-29 01:06:26,411 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 01:06:26,411 - __main__ - INFO - 📈 第1天交易动作: {'META': 1.0}
2025-07-29 01:06:26,414 - __main__ - INFO - 💰 第1天净值变化: 收益率=-0.0077, 净值=$992310.65
2025-07-29 01:06:26,416 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 01:06:43,167 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 01:06:43,167 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 01:07:17,945 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 01:07:17,946 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 01:07:17,946 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-13, 累计收益=-0.0077, 周收益=-0.0077
2025-07-29 01:07:24,384 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 01:07:24,384 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 01:07:24,384 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 01:07:24,384 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 01:07:24,384 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 01:07:24,384 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 01:07:24,384 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 01:07:24,384 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对基础面进行了评估。FAA基本面分析显示公司财务健康度良好，估值合理，且在NAA新闻分析中未发现重大负面消息，表明基础面稳健。接着，我们进行了技术面分析，TAA技术分析显示股票价格呈现上升趋势，支撑位和阻力位明确，技术指标如RSI、MACD和移动平均线均显示出看涨信号，技术评分较高。展望层智能体中，BOA看涨信号强度较高，预计市场将呈现上升趋势，而BeOA看跌信号较弱，NOA则认为市场展望中性。在风险评估方面，各层级信号基本一致，分歧度较低，市场环境不确定性不高。综合以上分析，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-13'}
2025-07-29 01:07:24,384 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 01:07:24,385 - __main__ - INFO - 📈 第2天交易动作: {'META': 1.0}
2025-07-29 01:07:24,389 - __main__ - INFO - 💰 第2天净值变化: 收益率=-0.0359, 净值=$956667.31
2025-07-29 01:07:24,392 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 01:07:45,210 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 01:07:45,212 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 01:08:18,150 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 01:08:18,152 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 01:08:18,152 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-14, 累计收益=-0.0433, 周收益=-0.0433
2025-07-29 01:08:22,376 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 01:08:22,376 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 01:08:22,376 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 01:08:22,376 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 01:08:22,376 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 01:08:22,376 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 01:08:22,376 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 01:08:22,376 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对基础面进行了评估。根据FAA基本面分析，META的财务健康度良好，估值合理，显示出公司的稳健性。同时，NAA新闻分析显示，大部分新闻情绪中性，但部分新闻如公司扩张和新产品发布等带有略微乐观的情绪，这表明市场对META的正面看法。在技术面分析中，TAA技术分析显示META股票价格呈现上升趋势，支撑位和阻力位明确，技术指标如RSI、MACD和移动平均线均显示出看涨信号。展望层分析中，BOA看涨信号强于BeOA，NOA认为上涨概率较高，这进一步支持了看涨的观点。在风险评估方面，各层级信号基本一致，分歧度较低，市场环境相对稳定。综合以上分析，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-14'}
2025-07-29 01:08:22,376 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 01:08:22,376 - __main__ - INFO - 📈 第3天交易动作: {'META': 1.0}
2025-07-29 01:08:22,378 - __main__ - INFO - 💰 第3天净值变化: 收益率=-0.0027, 净值=$954072.15
2025-07-29 01:08:22,379 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 01:08:22,380 - __main__ - INFO - TAA检测到无价格数据，建议跳过当前日期: 2025-02-17
2025-07-29 01:09:46,280 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 01:09:46,282 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 01:10:11,396 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 01:10:11,397 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 01:10:11,397 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-17, 累计收益=-0.0459, 周收益=-0.0459
2025-07-29 01:10:27,542 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 01:10:27,542 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 01:10:27,542 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 01:10:27,543 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 01:10:27,543 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 01:10:27,543 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 01:10:27,543 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 01:10:27,543 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对基础面进行了评估。根据FAA基本面分析，Meta Platforms的财务健康度良好，估值合理，且在竞争地位上处于领先。NAA新闻分析显示，Meta Platforms和Google为Broadcom投资者带来了好消息，同时Goldman Sachs预测中国股市将因AI技术的快速实施而大幅上涨，这些因素对Meta Platforms的股价产生积极影响。在技术面分析中，由于TAA技术分析无法进行，我们无法提供具体的技术信号。在展望层综合分析中，BOA看涨信号强度较高，主要基于市场对AI技术的乐观预期和Meta Platforms的良好基本面。BeOA看跌信号强度较低，而NOA认为市场展望保持中性。在风险评估中，我们注意到市场情绪偏向看涨，但同时也存在不确定性。综合考虑各层级信息，我们认为买入条件得到满足，因此**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-17'}
2025-07-29 01:10:27,543 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 01:10:27,543 - __main__ - INFO - 📈 第4天交易动作: {'META': 1.0}
2025-07-29 01:10:27,548 - __main__ - INFO - 💰 第4天净值变化: 收益率=-0.0173, 净值=$937540.05
2025-07-29 01:10:27,551 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 01:10:54,266 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 01:10:54,268 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 01:11:14,550 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 01:11:14,551 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 01:11:14,551 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-18, 累计收益=-0.0625, 周收益=-0.0625
2025-07-29 01:11:32,022 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 01:11:32,023 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 01:11:32,023 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 01:11:32,023 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 01:11:32,023 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 01:11:32,023 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 01:11:32,023 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 01:11:32,023 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '经过对NAA、TAA和FAA的分析，以及BOA、BeOA和NOA的展望层综合分析，以下是我的决策过程和理由。\n\n**基础面评估**：FAA显示Meta Platforms财务健康度良好，估值合理。NAA分析显示市场新闻情绪整体中性，但包含积极因素，如新产品批准和市场增长预测。基础面评估良好。\n\n**技术面分析**：TAA分析显示股票价格呈现上升趋势，动量强劲，支撑位和阻力位明确，技术指标如RSI、MACD和移动平均线均显示出看涨信号。\n\n**展望层综合**：BOA看涨信号强于BeOA，NOA认为上涨概率较高，但市场情绪并未表现出明显的看涨或看跌倾向。\n\n**风险评估**：各层级信号基本一致，分歧度较低，市场环境不确定性较高，但整体风险可控。\n\n**最终决策**：基于以上分析，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-18'}
2025-07-29 01:11:32,023 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 01:11:32,023 - __main__ - INFO - 📈 第5天交易动作: {'META': 1.0}
2025-07-29 01:11:32,027 - __main__ - INFO - 💰 第5天净值变化: 收益率=0.0249, 净值=$960928.49
2025-07-29 01:11:32,027 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-29 01:11:32,027 - __main__ - INFO - ============================================================
2025-07-29 01:11:32,027 - __main__ - INFO - 第 7 周性能分析 - 联盟: frozenset({'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'})
2025-07-29 01:11:32,027 - __main__ - INFO - 周总收益率: -0.0387
2025-07-29 01:11:32,027 - __main__ - INFO - 周夏普比率: -6.2386
2025-07-29 01:11:32,028 - __main__ - INFO - 交易天数: 5
2025-07-29 01:11:32,028 - __main__ - INFO - ============================================================
2025-07-29 01:43:20,878 - __main__ - INFO - ======================================================================
2025-07-29 01:43:20,878 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-29 01:43:20,878 - __main__ - INFO - ======================================================================
2025-07-29 01:43:20,878 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-29 01:43:20,878 - __main__ - INFO - 开始模拟联盟: {'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'}
2025-07-29 01:43:20,878 - __main__ - INFO - 📅 当前周数: 8
2025-07-29 01:43:20,878 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-29 01:43:20,878 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'})
2025-07-29 01:43:20,879 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-02-19 (第8周)
2025-07-29 01:43:20,879 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-02-19, end_date=2025-02-26
2025-07-29 01:43:20,879 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-29 01:43:20,967 - __main__ - INFO - 📅 设置当前模拟周数: 8 (来自参数)
2025-07-29 01:43:20,969 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 01:44:04,419 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 01:44:04,421 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 01:44:49,683 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 01:44:49,684 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 01:44:49,684 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-19, 累计收益=0.0000, 周收益=0.0000
2025-07-29 01:45:04,838 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 01:45:04,839 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 01:45:04,839 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 01:45:04,839 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 01:45:04,839 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 01:45:04,839 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 01:45:04,839 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 01:45:04,839 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对基础面进行了评估。FAA基本面分析显示META的财务健康度良好，估值合理，且在NAA新闻分析中，虽然存在一些法律诉讼和高管变动的新闻，但整体情绪中性，没有重大负面消息。这表明基础面较为稳健。接着，我们进行了技术面分析，TAA技术分析显示META股票目前处于下跌趋势，但RSI指标显示市场可能存在超卖状态，存在反弹机会。展望层智能体中，BOA看涨信号强度较高，而BeOA看跌信号强度较低，NOA则认为市场展望中性。在风险评估方面，各层级信号基本一致，分歧度较低，市场环境不确定性不高。综合考虑以上因素，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-19'}
2025-07-29 01:45:04,839 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 01:45:04,839 - __main__ - INFO - 📈 第1天交易动作: {'META': 1.0}
2025-07-29 01:45:04,841 - __main__ - INFO - 💰 第1天净值变化: 收益率=0.0132, 净值=$1013169.96
2025-07-29 01:45:04,843 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 01:45:44,466 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 01:45:44,466 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 01:46:06,766 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 01:46:06,766 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 01:46:06,766 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-20, 累计收益=0.0132, 周收益=0.0132
2025-07-29 01:46:10,705 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 01:46:10,705 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 01:46:10,705 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 01:46:10,705 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 01:46:10,706 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 01:46:10,706 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 01:46:10,706 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 01:46:10,706 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在基础面评估中，FAA显示Meta Platforms财务健康度良好，估值合理，同时NAA分析显示市场情绪偏向积极，尽管存在一些中性或略微悲观的新闻，但整体上没有重大负面消息。技术面分析中，TAA显示股票价格呈现上升趋势，技术指标如RSI、MACD和移动平均线均显示出看涨信号。展望层分析中，BOA看涨信号强于BeOA，NOA认为市场上涨概率较高。综合各层级分析，信号一致性较高，风险控制方面，各层级信号也较为一致，因此最终决策为买入。**最终决策：买入**', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-20'}
2025-07-29 01:46:10,706 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 01:46:10,706 - __main__ - INFO - 📈 第2天交易动作: {'META': 1.0}
2025-07-29 01:46:10,709 - __main__ - INFO - 💰 第2天净值变化: 收益率=-0.0072, 净值=$1005834.79
2025-07-29 01:46:10,711 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 01:47:12,588 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 01:47:12,589 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 01:47:35,580 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 01:47:35,581 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 01:47:35,581 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-21, 累计收益=0.0058, 周收益=0.0058
2025-07-29 01:47:52,060 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 01:47:52,060 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 01:47:52,060 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 01:47:52,060 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 01:47:52,060 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 01:47:52,060 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 01:47:52,061 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 01:47:52,061 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对Meta Platforms (META) 进行了全面的多维度分析。首先，从基础面来看，FAA基本面分析显示META的财务健康度良好，估值合理，显示出公司的稳健性。NAA新闻分析中，虽然存在一些中性或略微看跌的消息，但整体情绪偏向乐观，特别是公司CEO对新收购计划的讨论和与行业同行的竞争分析，显示出公司的积极发展态势。技术面分析中，TAA显示META的价格趋势呈现波动上升，尽管最近有所回调，但整体动量仍然向上，支撑位和阻力位分析也支持这一观点。展望层分析中，BOA看涨信号强度较高，而BeOA看跌信号相对较弱，NOA则认为市场展望保持中性。综合各层级信号，我们认为META的买入条件基本满足。风险评估显示，各层级信号基本一致，分歧度较低，市场环境不确定性较高，但META的技术面和基本面较为稳健。因此，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-21'}
2025-07-29 01:47:52,061 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 01:47:52,061 - __main__ - INFO - 📈 第3天交易动作: {'META': 1.0}
2025-07-29 01:47:52,065 - __main__ - INFO - 💰 第3天净值变化: 收益率=-0.0059, 净值=$999933.32
2025-07-29 01:47:52,067 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 01:48:17,954 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 01:48:17,956 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 01:48:48,708 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 01:48:48,710 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 01:48:48,710 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-24, 累计收益=-0.0001, 周收益=-0.0001
2025-07-29 01:48:59,018 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 01:48:59,018 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 01:48:59,018 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 01:48:59,018 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 01:48:59,018 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 01:48:59,018 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 01:48:59,018 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 01:48:59,018 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对基础面进行了评估。FAA基本面分析显示，META公司的财务健康度良好，估值合理，且在竞争中处于领先地位。NAA新闻分析表明，市场情绪整体中性，但部分积极新闻，如Apple承诺在美国投资5000亿美元，可能对市场产生短期影响。技术面分析（TAA）显示，META股票价格出现明显下跌趋势，但短期内存在支撑位。展望层智能体（BOA和BeOA）分别给出了看涨和看跌的展望，而NOA则认为市场趋势中性。在风险评估方面，各层级信号存在一定分歧，但整体风险可控。综合以上分析，**最终决策：买入**。我们相信，尽管市场存在不确定性，但META公司的基本面和技术面均显示出一定的投资价值。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-24'}
2025-07-29 01:48:59,018 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 01:48:59,018 - __main__ - INFO - 📈 第4天交易动作: {'META': 1.0}
2025-07-29 01:48:59,021 - __main__ - INFO - 💰 第4天净值变化: 收益率=-0.0143, 净值=$985646.41
2025-07-29 01:48:59,022 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 01:49:21,790 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 01:49:21,790 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 01:49:57,246 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 01:49:57,246 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 01:49:57,246 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-25, 累计收益=-0.0144, 周收益=-0.0144
2025-07-29 01:50:06,636 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 01:50:06,637 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 01:50:06,637 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 01:50:06,638 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 01:50:06,638 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 01:50:06,638 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 01:50:06,638 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 01:50:06,638 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对Meta Platforms, Inc. (META)进行了全面的多维度分析。首先，从基础面来看，FAA基本面分析显示META的财务健康度良好，估值合理，且在竞争中处于领先地位。NAA新闻分析显示，尽管市场情绪较为悲观，但META的竞争对手在互动媒体和服务行业中受到评估，这可能对META构成潜在风险。然而，NAA也指出，META的竞争对手评估可能对META的股价产生影响，但影响程度尚不明确。技术面分析方面，TAA显示META股票处于下跌趋势，但RSI值在30以下，表明股票可能存在反弹机会。展望层分析中，BOA看涨信号强度较高，认为META的上涨概率和目标空间较大。然而，BeOA看跌信号强度也较高，指出市场情绪较为悲观，且META可能面临潜在风险。NOA认为市场表现出一定的看跌趋势，但同时也存在看涨的潜在因素。综合分析，各层级信号存在一定分歧，但考虑到META的基本面稳健和技术面的潜在反弹机会，我们倾向于看涨。因此，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-25'}
2025-07-29 01:50:06,638 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 01:50:06,638 - __main__ - INFO - 📈 第5天交易动作: {'META': 1.0}
2025-07-29 01:50:06,642 - __main__ - INFO - 💰 第5天净值变化: 收益率=-0.0097, 净值=$976094.02
2025-07-29 01:50:06,642 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-29 01:50:06,643 - __main__ - INFO - ============================================================
2025-07-29 01:50:06,643 - __main__ - INFO - 第 8 周性能分析 - 联盟: frozenset({'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'})
2025-07-29 01:50:06,643 - __main__ - INFO - 周总收益率: -0.0239
2025-07-29 01:50:06,643 - __main__ - INFO - 周夏普比率: -8.1920
2025-07-29 01:50:06,643 - __main__ - INFO - 交易天数: 5
2025-07-29 01:50:06,643 - __main__ - INFO - ============================================================
2025-07-29 02:22:14,829 - __main__ - INFO - ======================================================================
2025-07-29 02:22:14,829 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-29 02:22:14,829 - __main__ - INFO - ======================================================================
2025-07-29 02:22:14,829 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-29 02:22:14,829 - __main__ - INFO - 开始模拟联盟: {'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'}
2025-07-29 02:22:14,829 - __main__ - INFO - 📅 当前周数: 9
2025-07-29 02:22:14,829 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-29 02:22:14,830 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'})
2025-07-29 02:22:14,831 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-02-26 (第9周)
2025-07-29 02:22:14,831 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-02-26, end_date=2025-03-05
2025-07-29 02:22:14,831 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-29 02:22:14,916 - __main__ - INFO - 📅 设置当前模拟周数: 9 (来自参数)
2025-07-29 02:22:14,917 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 02:22:59,719 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 02:22:59,720 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 02:23:25,272 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 02:23:25,272 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 02:23:25,272 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-26, 累计收益=0.0000, 周收益=0.0000
2025-07-29 02:23:37,700 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 02:23:37,701 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 02:23:37,701 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 02:23:37,701 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 02:23:37,701 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 02:23:37,701 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 02:23:37,701 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 02:23:37,701 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对Meta Platforms, Inc. (META)进行了全面的多维度分析。基础面分析显示，META正在计划建设一个价值超过2000亿美元的AI数据中心，这表明公司对AI技术的重视和长期投资，对股价产生积极影响。同时，NAA分析显示，META的AI数据中心项目可能会对其股价产生积极影响，因为它表明公司对AI技术的长期承诺。技术面分析显示，META股票价格呈现上升趋势，支撑位在$658.0附近，阻力位在$683.0078附近，技术指标如RSI、MACD和移动平均线均支持多头市场。展望层分析中，BOA看涨信号强于BeOA看跌信号，NOA认为上涨概率较高。然而，我们也注意到市场存在不确定性因素，因此保持中性展望。综合以上分析，各层级信号基本一致，分歧度较低，因此我们得出**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-26'}
2025-07-29 02:23:37,701 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 02:23:37,701 - __main__ - INFO - 📈 第1天交易动作: {'META': 1.0}
2025-07-29 02:23:37,703 - __main__ - INFO - 💰 第1天净值变化: 收益率=-0.0097, 净值=$990308.50
2025-07-29 02:23:37,704 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 02:24:06,763 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 02:24:06,763 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 02:24:31,058 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 02:24:31,059 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 02:24:31,059 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-27, 累计收益=-0.0097, 周收益=-0.0097
2025-07-29 02:24:48,996 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 02:24:48,996 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 02:24:48,996 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 02:24:48,996 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 02:24:48,996 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 02:24:48,996 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 02:24:48,996 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 02:24:48,996 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '经过对NAA、TAA和FAA的分析，以及对BOA、BeOA和NOA的展望层综合评估，以下是详细的分析过程和决策理由。\n\n**基础面评估**：FAA显示Meta Platforms财务健康度良好，估值合理，竞争地位领先。NAA分析显示市场新闻整体情绪较为中性，但包含一些积极因素，如企业业绩超预期、新产品发布等，没有重大负面消息。因此，基础面评估为正面。\n\n**技术面分析**：TAA显示市场短期内可能存在下跌趋势，但RSI值低于50，表明市场可能处于超卖状态，存在反弹机会。MACD线与信号线交叉向下，移动平均线均向下，技术评分设定为-0.5，分析信心度为0.8。\n\n**展望层综合**：BOA倾向于看涨市场，认为尽管TAA显示短期内可能存在下跌趋势，但结合其他分析，市场有望上涨。BeOA看跌信号强度较弱，NOA认为市场展望为中性，存在短期下跌趋势，但平衡分析后，上涨概率较高。\n\n**风险评估**：各层级信号基本一致，分歧度较低。市场环境不确定性较高，但整体风险可控。\n\n**最终决策**：基于以上综合分析，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-27'}
2025-07-29 02:24:48,996 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 02:24:48,996 - __main__ - INFO - 📈 第2天交易动作: {'META': 1.0}
2025-07-29 02:24:49,000 - __main__ - INFO - 💰 第2天净值变化: 收益率=0.0234, 净值=$1013530.88
2025-07-29 02:24:49,002 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 02:25:15,898 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 02:25:15,899 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 02:25:59,211 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 02:25:59,211 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 02:25:59,211 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-28, 累计收益=0.0135, 周收益=0.0135
2025-07-29 02:26:16,230 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 02:26:16,230 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 02:26:16,230 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 02:26:16,230 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 02:26:16,230 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 02:26:16,230 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 02:26:16,230 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 02:26:16,230 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对Meta Platforms (META)进行了全面的多维度分析。\n\n**基础面评估**：FAA基本面分析显示META财务健康度良好，估值合理。NAA新闻分析表明，META正在开发一个AI应用程序，这可能对其股价产生积极影响。尽管如此，AppLovin (APP) 股价下跌可能对市场情绪产生负面影响。总体而言，基础面评估显示META基本面稳健，无重大负面消息。\n\n**技术面分析**：TAA技术分析显示META价格呈现波动上升的趋势，但近期出现回调。动量指标显示价格有所减弱。支撑位和阻力位分析显示，META可能存在潜在的支撑和阻力位。技术指标信号显示RSI中性，MACD显示趋势可能反转，移动平均线显示近期趋势向下。技术评分中性。\n\n**展望层综合**：BOA看涨信号强度较高，看好META的上涨潜力。BeOA看跌信号强度较低，但存在一定风险。NOA中性观点认为市场存在不确定性。展望层综合显示META上涨概率较高。\n\n**风险评估**：各层级信号基本一致，分歧度较低。市场环境不确定性较高，但META基本面和技术面表现良好。\n\n**最终决策**：基于以上分析，我们得出结论：**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-28'}
2025-07-29 02:26:16,230 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 02:26:16,230 - __main__ - INFO - 📈 第3天交易动作: {'META': 1.0}
2025-07-29 02:26:16,232 - __main__ - INFO - 💰 第3天净值变化: 收益率=0.0090, 净值=$1022647.32
2025-07-29 02:26:16,233 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 02:26:41,383 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 02:26:41,383 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 02:27:05,463 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 02:27:05,463 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 02:27:05,463 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-03, 累计收益=0.0226, 周收益=0.0226
2025-07-29 02:27:20,168 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 02:27:20,170 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 02:27:20,170 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 02:27:20,170 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 02:27:20,170 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 02:27:20,170 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 02:27:20,170 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 02:27:20,171 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '今日分析过程如下：\n\n1. **基础面评估**：根据FAA，公司财务健康度良好，估值合理；NAA显示今日无重大负面新闻，市场情绪稳定。综合来看，基础面表现良好。\n\n2. **技术面分析**：TAA显示当前市场趋势为看跌，支撑位价格为$650.00，阻力位价格为$680.00。技术指标如RSI、MACD和移动平均线均显示出下跌趋势。\n\n3. **展望层综合**：BOA倾向于看涨市场，但BeOA和NOA均显示看跌或中性展望。这表明市场存在一定的不确定性。\n\n4. **风险评估**：各层级信号存在一定分歧，技术面显示看跌，但基础面表现良好。市场环境不确定性较高。\n\n**最终决策：买入**。尽管技术面显示看跌，但基础面表现良好且估值合理，且市场存在不确定性，因此选择买入。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-03'}
2025-07-29 02:27:20,171 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 02:27:20,171 - __main__ - INFO - 📈 第4天交易动作: {'META': 1.0}
2025-07-29 02:27:20,175 - __main__ - INFO - 💰 第4天净值变化: 收益率=0.0423, 净值=$1065895.41
2025-07-29 02:27:20,176 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 02:27:41,715 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 02:27:41,715 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 02:28:06,565 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 02:28:06,565 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 02:28:06,566 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-04, 累计收益=0.0659, 周收益=0.0659
2025-07-29 02:28:23,165 - __main__ - INFO - 🎯 找到最终决策声明: sell
2025-07-29 02:28:23,166 - __main__ - WARNING - 修正决策不一致: 声明sell vs 字段hold
2025-07-29 02:28:23,166 - __main__ - INFO - 一致性修正：hold -> sell
2025-07-29 02:28:23,166 - __main__ - INFO - 🤖 TRA 输出: action=sell, position_size=1.0
2025-07-29 02:28:23,166 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: sell (信心度: 0.00)
2025-07-29 02:28:23,166 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 02:28:23,166 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 02:28:23,166 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '今日分析过程如下：\n\n1. **基础面评估**：根据FAA分析，公司财务健康度良好，估值处于合理水平。NAA显示今日没有相关新闻，因此市场情绪稳定，无重大负面消息。综合来看，基础面表现良好。\n\n2. **技术面分析**：TAA分析结果显示，股票价格呈现下降趋势，技术评分较低。关键技术指标如RSI、MACD和移动平均线均显示市场动能可能正在减弱，趋势可能向下。\n\n3. **展望层综合**：BOA显示看涨信号，但BeOA和NOA均显示中性或看跌信号。这表明市场存在一定的不确定性。\n\n4. **风险评估**：由于技术面显示看跌趋势，且展望层信号存在分歧，因此建议降低仓位或观望。\n\n**最终决策：卖出**。由于技术面显示看跌趋势，且基础面和展望层信号存在分歧，建议卖出当前持仓。', 'action': 'sell', 'trading_actions': {'META': -1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-04'}
2025-07-29 02:28:23,168 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': -1.0}
2025-07-29 02:28:23,168 - __main__ - INFO - 📈 第5天交易动作: {'META': -1.0}
2025-07-29 02:28:23,173 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-29 02:28:23,173 - __main__ - INFO - ============================================================
2025-07-29 02:28:23,173 - __main__ - INFO - 第 9 周性能分析 - 联盟: frozenset({'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'})
2025-07-29 02:28:23,173 - __main__ - INFO - 周总收益率: 0.0650
2025-07-29 02:28:23,173 - __main__ - INFO - 周夏普比率: 11.2404
2025-07-29 02:28:23,173 - __main__ - INFO - 交易天数: 5
2025-07-29 02:28:23,173 - __main__ - INFO - ============================================================
2025-07-29 03:00:36,193 - __main__ - INFO - ======================================================================
2025-07-29 03:00:36,193 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-29 03:00:36,193 - __main__ - INFO - ======================================================================
2025-07-29 03:00:36,193 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-29 03:00:36,193 - __main__ - INFO - 开始模拟联盟: {'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'}
2025-07-29 03:00:36,193 - __main__ - INFO - 📅 当前周数: 10
2025-07-29 03:00:36,193 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-29 03:00:36,193 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'})
2025-07-29 03:00:36,195 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-03-05 (第10周)
2025-07-29 03:00:36,195 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-03-05, end_date=2025-03-12
2025-07-29 03:00:36,195 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-29 03:00:36,286 - __main__ - INFO - 📅 设置当前模拟周数: 10 (来自参数)
2025-07-29 03:00:36,287 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 03:00:59,584 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 03:00:59,586 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 03:01:21,811 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 03:01:21,811 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 03:01:21,811 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-05, 累计收益=0.0000, 周收益=0.0000
2025-07-29 03:01:39,260 - __main__ - INFO - 🎯 找到最终决策声明: sell
2025-07-29 03:01:39,261 - __main__ - WARNING - 修正决策不一致: 声明sell vs 字段hold
2025-07-29 03:01:39,261 - __main__ - INFO - 一致性修正：hold -> sell
2025-07-29 03:01:39,261 - __main__ - INFO - 🤖 TRA 输出: action=sell, position_size=1.0
2025-07-29 03:01:39,261 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: sell (信心度: 0.00)
2025-07-29 03:01:39,261 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 03:01:39,261 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 03:01:39,261 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '今日分析过程如下：\n\n1. **基础面评估**：根据FAA和NAA的分析结果，公司财务健康度良好，估值合理。NAA显示今日没有相关新闻，因此无重大负面消息。基础面评估结果为正面。\n\n2. **技术面分析**：TAA显示股票可能正在经历一段上升后的回调，趋势方向中性或看跌。技术评分较低，分析信心度为0.7。\n\n3. **展望层综合**：BOA显示市场展望为看涨，但BeOA显示市场展望中性至看跌，NOA认为市场展望为中性。展望层信号不一致，分析信心度较低。\n\n4. **风险评估**：由于展望层信号不一致，且技术面显示趋势可能转向看跌，建议降低仓位或观望。\n\n**最终决策：卖出**。由于基础面良好，但技术面和展望层信号不一致，且存在潜在的市场风险，建议卖出以规避风险。', 'action': 'sell', 'trading_actions': {'META': -1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-05'}
2025-07-29 03:01:39,261 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': -1.0}
2025-07-29 03:01:39,261 - __main__ - INFO - 📈 第1天交易动作: {'META': -1.0}
2025-07-29 03:01:39,264 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 03:01:51,756 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 03:01:51,756 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 03:02:14,953 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 03:02:14,954 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 03:02:14,954 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-06, 累计收益=0.0000, 周收益=0.0000
2025-07-29 03:02:30,337 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 03:02:30,337 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 03:02:30,337 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 03:02:30,337 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 03:02:30,337 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 03:02:30,337 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 03:02:30,337 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 03:02:30,337 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '今日分析结果显示，基础面分析显示公司财务健康，估值合理，竞争优势领先。新闻分析由于缺乏相关新闻，无法进行情绪分析和影响评估。技术分析显示股票价格呈现上升趋势，但最近几个交易日有波动，动量指标显示动量可能减弱。支撑位和阻力位分析显示存在潜在的支撑和阻力位。展望层分析中，看涨展望基于财务健康和竞争优势，看跌展望主要基于技术分析显示的价格趋势减弱和技术指标信号，中性展望认为市场展望为中性，技术分析显示价格趋势有所减弱，但基本面分析表明公司的估值合理。综合分析，基础面和技术面信号存在分歧，但基础面较为稳健。因此，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-06'}
2025-07-29 03:02:30,337 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 03:02:30,337 - __main__ - INFO - 📈 第2天交易动作: {'META': 1.0}
2025-07-29 03:02:30,339 - __main__ - INFO - 💰 第2天净值变化: 收益率=-0.0116, 净值=$988395.99
2025-07-29 03:02:30,340 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 03:02:54,684 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 03:02:54,684 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 03:03:17,208 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 03:03:17,209 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 03:03:17,209 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-07, 累计收益=-0.0116, 周收益=-0.0116
2025-07-29 03:03:33,374 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 03:03:33,374 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 03:03:33,374 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 03:03:33,374 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 03:03:33,375 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 03:03:33,375 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 03:03:33,375 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 03:03:33,375 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在基础面评估中，FAA显示公司财务健康度良好，估值合理，同时NAA分析显示市场新闻情绪整体中性，但部分新闻显示出对AI和科技行业的乐观态度。技术面分析中，TAA显示股票价格呈现波动上升的趋势，但近期波动较大，技术指标RSI接近50，市场中性，MACD显示短期趋势可能看涨但动能减弱。展望层综合分析中，BOA看涨信号强度较高，而BeOA看跌信号强度较低，NOA认为市场整体中性。风险评估显示各层级信号基本一致，分歧度较低。因此，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-07'}
2025-07-29 03:03:33,375 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 03:03:33,375 - __main__ - INFO - 📈 第3天交易动作: {'META': 1.0}
2025-07-29 03:03:33,379 - __main__ - INFO - 💰 第3天净值变化: 收益率=0.0084, 净值=$996714.63
2025-07-29 03:03:33,381 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 03:03:45,721 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 03:03:45,721 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 03:04:07,748 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 03:04:07,748 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 03:04:07,749 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-10, 累计收益=-0.0033, 周收益=-0.0033
2025-07-29 03:04:27,447 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 03:04:27,448 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 03:04:27,448 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 03:04:27,448 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 03:04:27,448 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 03:04:27,448 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 03:04:27,448 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 03:04:27,448 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对基础面进行了评估。根据FAA和NAA的分析结果，公司财务健康度良好，估值合理，且NAA未发现重大负面消息，因此基础面评估为正面。接着，我们进行了技术面分析，TAA显示股票价格呈现下降趋势，动量指标有所回升但整体趋势向下，支撑位和阻力位分析显示存在潜在的支撑和阻力区域。技术指标如RSI、MACD和移动平均线均显示看跌趋势。展望层智能体中，BOA信号显示看涨，但BeOA信号显示看跌，NOA则认为市场展望中性。在风险评估方面，各层级信号存在一定分歧，但整体风险可控。综合以上分析，**最终决策：买入**。我们相信，尽管技术面显示看跌，但基础面的稳健和展望层智能体的看涨信号为我们提供了买入的理由。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-10'}
2025-07-29 03:04:27,448 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 03:04:27,448 - __main__ - INFO - 📈 第4天交易动作: {'META': 1.0}
2025-07-29 03:04:27,452 - __main__ - INFO - 💰 第4天净值变化: 收益率=-0.0122, 净值=$984527.99
2025-07-29 03:04:27,454 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 03:04:54,152 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 03:04:54,155 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 03:05:20,001 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 03:05:20,002 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 03:05:20,002 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-11, 累计收益=-0.0155, 周收益=-0.0155
2025-07-29 03:05:24,186 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 03:05:24,186 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 03:05:24,186 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 03:05:24,186 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 03:05:24,186 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 03:05:24,186 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 03:05:24,186 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 03:05:24,186 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对基础面进行了评估。根据FAA基本面分析，公司财务健康度良好，估值合理，显示出稳健的基本面。同时，NAA新闻分析显示，目前没有重大负面消息，市场情绪稳定。在技术面分析中，TAA技术分析表明股票价格呈现上升趋势，支撑位和阻力位明确，技术指标如RSI、MACD和移动平均线均显示出看涨信号。展望层智能体中，BOA看涨信号强于BeOA，NOA认为上涨概率较高。然而，由于缺乏具体新闻数据，我们无法对市场情绪进行精确评估。在风险评估方面，各层级信号基本一致，分歧度较低，市场环境相对稳定。综合以上分析，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-11'}
2025-07-29 03:05:24,187 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 03:05:24,187 - __main__ - INFO - 📈 第5天交易动作: {'META': 1.0}
2025-07-29 03:05:24,191 - __main__ - INFO - 💰 第5天净值变化: 收益率=-0.0231, 净值=$961740.76
2025-07-29 03:05:24,191 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-29 03:05:24,191 - __main__ - INFO - ============================================================
2025-07-29 03:05:24,192 - __main__ - INFO - 第 10 周性能分析 - 联盟: frozenset({'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'})
2025-07-29 03:05:24,192 - __main__ - INFO - 周总收益率: -0.0386
2025-07-29 03:05:24,192 - __main__ - INFO - 周夏普比率: -11.3541
2025-07-29 03:05:24,192 - __main__ - INFO - 交易天数: 5
2025-07-29 03:05:24,192 - __main__ - INFO - ============================================================
2025-07-29 03:37:16,455 - __main__ - INFO - ======================================================================
2025-07-29 03:37:16,455 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-29 03:37:16,455 - __main__ - INFO - ======================================================================
2025-07-29 03:37:16,455 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-29 03:37:16,456 - __main__ - INFO - 开始模拟联盟: {'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'}
2025-07-29 03:37:16,456 - __main__ - INFO - 📅 当前周数: 11
2025-07-29 03:37:16,456 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-29 03:37:16,456 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'})
2025-07-29 03:37:16,456 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-03-12 (第11周)
2025-07-29 03:37:16,457 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-03-12, end_date=2025-03-19
2025-07-29 03:37:16,457 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-29 03:37:16,542 - __main__ - INFO - 📅 设置当前模拟周数: 11 (来自参数)
2025-07-29 03:37:16,543 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 03:37:37,148 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 03:37:37,149 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 03:38:07,887 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 03:38:07,888 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 03:38:07,888 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-12, 累计收益=0.0000, 周收益=0.0000
2025-07-29 03:38:19,048 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 03:38:19,049 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 03:38:19,049 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 03:38:19,049 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 03:38:19,050 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 03:38:19,050 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 03:38:19,050 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 03:38:19,050 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '今日分析过程如下：\n\n1. **基础面评估**：根据FAA和NAA的分析结果，META公司财务健康度良好，估值合理。尽管今日没有相关新闻，但根据历史数据，公司基本面稳健，无重大负面消息。\n\n2. **技术面分析**：TAA显示META价格在经历一段上涨后出现下跌，但近期有所回升。RSI接近中性区域，MACD看涨，短期移动平均线看涨，技术评分设为0（中性）。\n\n3. **展望层综合**：BOA显示市场展望为看涨，BeOA显示市场展望为看跌，NOA认为市场展望为中性。综合考虑，BOA的看涨信号略强于BeOA的看跌信号。\n\n4. **风险评估**：各层级信号基本一致，分歧度较低。市场环境不确定性较高，但无重大负面消息。\n\n**最终决策：买入**。理由是，虽然市场环境存在不确定性，但META公司基本面稳健，技术面显示中性趋势，且看涨信号略强于看跌信号。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-12'}
2025-07-29 03:38:19,051 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 03:38:19,051 - __main__ - INFO - 📈 第1天交易动作: {'META': 1.0}
2025-07-29 03:38:19,053 - __main__ - INFO - 💰 第1天净值变化: 收益率=-0.0231, 净值=$976854.67
2025-07-29 03:38:19,055 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 03:38:38,737 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 03:38:38,737 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 03:39:01,777 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 03:39:01,777 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 03:39:01,777 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-13, 累计收益=-0.0231, 周收益=-0.0231
2025-07-29 03:39:04,700 - __main__ - INFO - 🎯 找到最终决策声明: sell
2025-07-29 03:39:04,701 - __main__ - WARNING - 修正决策不一致: 声明sell vs 字段hold
2025-07-29 03:39:04,701 - __main__ - INFO - 一致性修正：hold -> sell
2025-07-29 03:39:04,701 - __main__ - INFO - 🤖 TRA 输出: action=sell, position_size=1.0
2025-07-29 03:39:04,701 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: sell (信心度: 0.00)
2025-07-29 03:39:04,701 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 03:39:04,701 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 03:39:04,701 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在基础面评估中，FAA显示公司财务健康度良好，估值合理，但NAA未发现重大负面消息。TAA显示股票价格呈现下降趋势，动量指标显示下降加速，支撑位和阻力位分析表明股票可能接近支撑位。展望层智能体中，BOA看涨信号强度较弱，BeOA看跌信号强度较强，NOA保持中性。综合分析，基础面和技术面信号存在分歧，但技术面显示的下降趋势较为明显。因此，**最终决策：卖出**。', 'action': 'sell', 'trading_actions': {'META': -1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-13'}
2025-07-29 03:39:04,701 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': -1.0}
2025-07-29 03:39:04,701 - __main__ - INFO - 📈 第2天交易动作: {'META': -1.0}
2025-07-29 03:39:04,704 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 03:39:40,647 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 03:39:40,647 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 03:40:06,764 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 03:40:06,765 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 03:40:06,765 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-14, 累计收益=-0.0231, 周收益=-0.0231
2025-07-29 03:40:25,090 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 03:40:25,090 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 03:40:25,090 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 03:40:25,090 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 03:40:25,090 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 03:40:25,090 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 03:40:25,090 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 03:40:25,090 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对META进行了全面的多维度分析。首先，从基础面来看，FAA显示META的财务健康度良好，估值合理，显示出公司具备较强的基本面支撑。NAA分析显示，尽管存在法律诉讼相关的负面新闻，但大部分新闻情绪中性，且Meta Platforms可能投资60亿美元于美国的消息对股价可能产生积极影响。技术面分析（TAA）显示，META的价格趋势呈现上升趋势，支撑位和阻力位明确，技术指标如RSI、MACD和移动平均线均显示出看涨信号。展望层分析（BOA、BeOA、NOA）中，BOA看涨信号强于BeOA，NOA认为上涨概率较高。综合以上分析，各层级信号基本一致，风险控制方面，信号分歧度较低，市场环境相对稳定。因此，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-14'}
2025-07-29 03:40:25,091 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 03:40:25,091 - __main__ - INFO - 📈 第3天交易动作: {'META': 1.0}
2025-07-29 03:40:25,093 - __main__ - INFO - 💰 第3天净值变化: 收益率=-0.0094, 净值=$967642.04
2025-07-29 03:40:25,094 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 03:40:48,954 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 03:40:48,955 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 03:41:08,943 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 03:41:08,945 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 03:41:08,945 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-17, 累计收益=-0.0324, 周收益=-0.0324
2025-07-29 03:41:15,103 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 03:41:15,103 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 03:41:15,103 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 03:41:15,103 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 03:41:15,103 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 03:41:15,103 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 03:41:15,103 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 03:41:15,103 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '今日分析结果显示，基础面评估显示公司财务健康度良好，估值合理，且NAA分析显示无重大负面消息。技术面分析显示TAA分析结果显示上升趋势，支撑位价格为$598.00，阻力位价格为$625.66，技术评分较高，表明市场趋势看涨。展望层分析中，BOA看涨信号强度较高，BeOA看跌信号较弱，NOA认为上涨概率较高。综合分析，各层级信号基本一致，风险控制措施得当，因此**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-17'}
2025-07-29 03:41:15,103 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 03:41:15,103 - __main__ - INFO - 📈 第4天交易动作: {'META': 1.0}
2025-07-29 03:41:15,105 - __main__ - INFO - 💰 第4天净值变化: 收益率=0.0024, 净值=$969968.94
2025-07-29 03:41:15,107 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 03:41:48,107 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 03:41:48,108 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 03:42:09,294 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 03:42:09,294 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 03:42:09,295 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-18, 累计收益=-0.0300, 周收益=-0.0300
2025-07-29 03:42:24,135 - __main__ - WARNING - 未找到明确的最终决策声明，保持action=hold
2025-07-29 03:42:24,136 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-29 03:42:24,136 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-29 03:42:24,136 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 03:42:24,136 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 03:42:24,136 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '根据提供的信息，我们进行了以下分析：\n\n1. **基础面评估**：FAA显示META公司财务健康，估值合理，竞争力领先。NAA没有发现重大负面新闻，市场情绪稳定。\n2. **技术面分析**：TAA显示META股价呈现下跌趋势，RSI接近超卖，MACD看跌，移动平均线向下，技术评分-0.5。\n3. **展望层综合**：BOA看涨信号强度较弱，BeOA看跌信号强度较强，NOA认为市场展望中性。\n4. **风险评估**：各层级信号存在分歧，技术面显示看跌趋势，但基础面显示公司基本面良好。\n\n**最终决策**：考虑到技术面显示的看跌趋势和基础面的稳定性，以及信号的一致性较低，建议保持观望。', 'action': 'hold', 'trading_actions': {'__HOLD__': 0.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-18'}
2025-07-29 03:42:24,136 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 0.0}
2025-07-29 03:42:24,136 - __main__ - INFO - 📈 第5天交易动作: {'__HOLD__': 0.0}
2025-07-29 03:42:24,140 - __main__ - INFO - 💰 第5天净值变化: 收益率=0.0060, 净值=$975809.94
2025-07-29 03:42:24,140 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-29 03:42:24,140 - __main__ - INFO - ============================================================
2025-07-29 03:42:24,140 - __main__ - INFO - 第 11 周性能分析 - 联盟: frozenset({'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'})
2025-07-29 03:42:24,140 - __main__ - INFO - 周总收益率: -0.0241
2025-07-29 03:42:24,141 - __main__ - INFO - 周夏普比率: -7.4302
2025-07-29 03:42:24,141 - __main__ - INFO - 交易天数: 5
2025-07-29 03:42:24,141 - __main__ - INFO - ============================================================
2025-07-29 04:14:06,304 - __main__ - INFO - ======================================================================
2025-07-29 04:14:06,304 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-29 04:14:06,304 - __main__ - INFO - ======================================================================
2025-07-29 04:14:06,304 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-29 04:14:06,304 - __main__ - INFO - 开始模拟联盟: {'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'}
2025-07-29 04:14:06,304 - __main__ - INFO - 📅 当前周数: 12
2025-07-29 04:14:06,304 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-29 04:14:06,304 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'})
2025-07-29 04:14:06,306 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-03-19 (第12周)
2025-07-29 04:14:06,306 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-03-19, end_date=2025-03-26
2025-07-29 04:14:06,306 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-29 04:14:06,404 - __main__ - INFO - 📅 设置当前模拟周数: 12 (来自参数)
2025-07-29 04:14:06,405 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 04:14:28,876 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 04:14:28,876 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 04:14:51,491 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 04:14:51,492 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 04:14:51,493 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-19, 累计收益=0.0000, 周收益=0.0000
2025-07-29 04:15:06,987 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 04:15:06,987 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 04:15:06,987 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 04:15:06,987 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 04:15:06,987 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 04:15:06,987 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 04:15:06,987 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 04:15:06,987 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对基础面进行了评估。根据FAA和NAA的分析，公司财务健康度良好，估值合理，且目前没有出现重大负面新闻。这表明基础面支持买入。接着，我们进行了技术面分析，TAA显示股票价格呈现下跌趋势，但RSI指标显示股票可能处于超卖状态，存在反弹机会。展望层智能体中，BOA和NOA的观点较为中性，而BeOA则显示看跌信号。在风险评估方面，各层级信号存在一定分歧，但整体来看，基础面和技术面信号较为一致。因此，我们得出最终决策：买入。**最终决策：买入**', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-19'}
2025-07-29 04:15:06,988 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 04:15:06,988 - __main__ - INFO - 📈 第1天交易动作: {'META': 1.0}
2025-07-29 04:15:06,992 - __main__ - INFO - 💰 第1天净值变化: 收益率=0.0114, 净值=$1011420.04
2025-07-29 04:15:06,995 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 04:15:30,948 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 04:15:30,948 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 04:16:08,206 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 04:16:08,206 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 04:16:08,206 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-20, 累计收益=0.0114, 周收益=0.0114
2025-07-29 04:16:29,203 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 04:16:29,204 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 04:16:29,204 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 04:16:29,204 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 04:16:29,204 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 04:16:29,204 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 04:16:29,204 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 04:16:29,204 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '详细的分析过程，包括展望层智能体信号分析、决策理由、风险评估等，最后必须明确声明：**最终决策：买入/卖出/保持不变**\n\n### 基础面评估\n\n#### FAA基本面分析\nMeta Platforms的财务健康度良好，估值合理，且在竞争中处于领先地位。\n\n#### NAA新闻分析\n大部分新闻情绪偏向乐观，尤其是关于Meta Platforms和其竞争对手的分析，以及一些公司的积极发展动态。\n\n#### 基础面综合\n基础面显示Meta Platforms基本面稳健，且市场情绪积极。\n\n### 技术面分析\n\n#### TAA技术分析\n技术面显示股票价格经历了一段上涨后有所下跌，但显示出一定的短期动量。支撑位在$580-$590区间，阻力位在$600-$610区间。\n\n#### 技术面综合\n技术面略微看涨，但市场处于中性状态。\n\n### 展望层综合\n\n#### BOA看涨信号强度\n市场展望为看涨，看涨因素包括Meta Platforms的市场地位和竞争对手分析，以及公司的积极发展。\n\n#### BeOA看跌信号强度\n尽管存在潜在风险因素，但看跌信号不强。\n\n#### NOA中性观点\n市场整体展望为中性，存在不确定性。\n\n#### 展望层综合\n展望层提供方向性指引，但需与分析层相互验证。\n\n### 风险评估\n\n#### 信号一致性\n各层级信号基本一致，分歧度较低。\n\n#### 潜在风险\n市场环境不确定性较高，需关注潜在风险。\n\n### 最终决策\n**最终决策：买入**\n\n基于以上分析，Meta Platforms基本面稳健，市场情绪积极，技术面略微看涨，展望层提供方向性指引，且风险可控，因此建议买入。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-20'}
2025-07-29 04:16:29,204 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 04:16:29,204 - __main__ - INFO - 📈 第2天交易动作: {'META': 1.0}
2025-07-29 04:16:29,209 - __main__ - INFO - 💰 第2天净值变化: 收益率=0.0208, 净值=$1032427.08
2025-07-29 04:16:29,211 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 04:17:31,242 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 04:17:31,244 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 04:17:51,112 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 04:17:51,112 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 04:17:51,112 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-21, 累计收益=0.0324, 周收益=0.0324
2025-07-29 04:18:07,356 - __main__ - INFO - 🎯 找到最终决策声明: sell
2025-07-29 04:18:07,357 - __main__ - WARNING - 修正决策不一致: 声明sell vs 字段hold
2025-07-29 04:18:07,358 - __main__ - INFO - 一致性修正：hold -> sell
2025-07-29 04:18:07,358 - __main__ - INFO - 🤖 TRA 输出: action=sell, position_size=1.0
2025-07-29 04:18:07,358 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: sell (信心度: 0.00)
2025-07-29 04:18:07,358 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 04:18:07,358 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 04:18:07,358 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对META的基础面进行了评估。FAA基本面分析显示，META的财务健康度良好，估值处于合理水平。然而，NAA新闻分析揭示了一系列法律诉讼和投资调查，这可能对公司的长期前景产生负面影响。技术面分析由TAA提供，显示META股票价格呈现下降趋势，技术指标如RSI和MACD均不支持买入信号。展望层智能体BOA和BeOA分别给出了看涨和看跌的展望，而NOA则持中性观点。考虑到这些因素，我们的风险评估显示信号存在分歧，风险较高。因此，根据我们的综合决策框架，**最终决策：卖出**。', 'action': 'sell', 'trading_actions': {'META': -1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-21'}
2025-07-29 04:18:07,358 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': -1.0}
2025-07-29 04:18:07,358 - __main__ - INFO - 📈 第3天交易动作: {'META': -1.0}
2025-07-29 04:18:07,364 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 04:18:57,267 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 04:18:57,268 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 04:19:31,569 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 04:19:31,569 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 04:19:31,570 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-24, 累计收益=0.0324, 周收益=0.0324
2025-07-29 04:19:34,924 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 04:19:34,924 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 04:19:34,924 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 04:19:34,924 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 04:19:34,924 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 04:19:34,924 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 04:19:34,924 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 04:19:34,925 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在基础面评估中，FAA显示META公司财务健康度良好，估值合理，显示出较强的竞争力。NAA分析显示，尽管有部分负面新闻，但大部分新闻情绪中性，部分乐观，表明市场情绪稳定。在技术面分析中，TAA显示META股票价格经历了先下跌后上涨的趋势，最近几个交易日价格上涨，动量增强，技术指标如RSI、MACD和移动平均线均显示出看涨趋势。展望层分析中，BOA看涨信号强于BeOA看跌信号，NOA认为上涨概率较高。综合各层级信息，各层级信号基本一致，风险控制方面，信号分歧度较低，市场环境不确定性不高。因此，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-24'}
2025-07-29 04:19:34,925 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 04:19:34,925 - __main__ - INFO - 📈 第4天交易动作: {'META': 1.0}
2025-07-29 04:19:34,927 - __main__ - INFO - 💰 第4天净值变化: 收益率=0.0191, 净值=$1052183.02
2025-07-29 04:19:34,928 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 04:19:59,525 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 04:19:59,526 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 04:20:35,366 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 04:20:35,368 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 04:20:35,368 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-25, 累计收益=0.0522, 周收益=0.0522
2025-07-29 04:20:54,593 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 04:20:54,595 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 04:20:54,595 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 04:20:54,595 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 04:20:54,595 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 04:20:54,595 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 04:20:54,596 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 04:20:54,596 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '详细的分析过程，包括展望层智能体信号分析、决策理由、风险评估等，最后必须明确声明：**最终决策：买入/卖出/保持不变**\n\n### 基础面评估\n\n根据FAA基本面分析，Meta Platforms的财务健康度良好，估值合理。NAA新闻分析显示，市场情绪较为中性，但部分新闻显示出乐观情绪，特别是Meta Platforms的财报分析。因此，基础面评估为正面。\n\n### 技术面分析\n\nTAA技术分析显示，股票价格呈现波动上升的趋势，动量指标增强。支撑位在$600.00-$610.00，阻力位在$630.00-$640.00。技术指标如RSI、MACD和移动平均线均显示出看涨信号。\n\n### 展望层综合\n\nBOA看涨信号强度较高，市场整体情绪偏向中性，但部分关键事件显示出乐观情绪。BeOA看跌信号较弱，NOA认为市场展望为中性。因此，展望层综合评估为看涨。\n\n### 风险评估\n\n各层级信号基本一致，分歧度较低。市场环境不确定性较高，但整体风险可控。\n\n### 最终决策声明\n\n基于以上分析，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-25'}
2025-07-29 04:20:54,596 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 04:20:54,596 - __main__ - INFO - 📈 第5天交易动作: {'META': 1.0}
2025-07-29 04:20:54,600 - __main__ - INFO - 💰 第5天净值变化: 收益率=0.0219, 净值=$1075223.64
2025-07-29 04:20:54,600 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-29 04:20:54,600 - __main__ - INFO - ============================================================
2025-07-29 04:20:54,600 - __main__ - INFO - 第 12 周性能分析 - 联盟: frozenset({'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'})
2025-07-29 04:20:54,601 - __main__ - INFO - 周总收益率: 0.0732
2025-07-29 04:20:54,601 - __main__ - INFO - 周夏普比率: 28.2409
2025-07-29 04:20:54,601 - __main__ - INFO - 交易天数: 5
2025-07-29 04:20:54,601 - __main__ - INFO - ============================================================
2025-07-29 04:53:01,857 - __main__ - INFO - ======================================================================
2025-07-29 04:53:01,857 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-29 04:53:01,857 - __main__ - INFO - ======================================================================
2025-07-29 04:53:01,857 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-29 04:53:01,857 - __main__ - INFO - 开始模拟联盟: {'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'}
2025-07-29 04:53:01,857 - __main__ - INFO - 📅 当前周数: 13
2025-07-29 04:53:01,857 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-29 04:53:01,857 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'})
2025-07-29 04:53:01,857 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-03-26 (第13周)
2025-07-29 04:53:01,857 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-03-26, end_date=2025-04-02
2025-07-29 04:53:01,857 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-29 04:53:01,949 - __main__ - INFO - 📅 设置当前模拟周数: 13 (来自参数)
2025-07-29 04:53:01,951 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 04:53:40,439 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 04:53:40,440 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 04:54:05,894 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 04:54:05,895 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 04:54:05,895 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-26, 累计收益=0.0000, 周收益=0.0000
2025-07-29 04:54:23,397 - __main__ - INFO - 🎯 找到最终决策声明: sell
2025-07-29 04:54:23,397 - __main__ - INFO - 🤖 TRA 输出: action=sell, position_size=1.0
2025-07-29 04:54:23,397 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: sell (信心度: 0.00)
2025-07-29 04:54:23,397 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 04:54:23,397 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 04:54:23,397 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '```json\n{\n  "analysis": "在本次分析中，我们首先对基础面进行了评估。FAA基本面分析显示，META公司的财务健康度良好，估值处于合理水平。NAA新闻分析表明，尽管市场新闻以中性情绪为主，但亚马逊对英伟达的潜在挑战和Meta Platforms在元宇宙领域的投资引起了市场关注，这些因素可能对META公司产生积极影响。然而，我们也注意到一些公司如AppLovin和Open Lending因潜在的法律问题而受到关注，这可能对整个市场情绪产生负面影响。\n\n技术面分析方面，TAA技术分析显示META的价格趋势偏弱，尽管在3月10日达到低点后有所回升，但整体趋势仍然偏弱。技术指标如RSI接近中性区域，MACD显示看跌信号，移动平均线也呈现向下趋势，这些指标表明短期内META可能存在下跌风险。\n\n展望层综合分析中，BOA看涨信号强度较弱，而BeOA看跌信号强度明显。NOA认为市场表现出中性情绪，没有明显的看涨或看跌理由。因此，展望层分析结果并不一致。\n\n在风险评估方面，我们发现各层级信号存在一定分歧，特别是技术面和展望层信号。这表明市场存在一定的不确定性。\n\n综合以上分析，**最终决策：卖出**。我们建议在当前市场环境下，考虑到技术面和展望层信号的不一致性，以及市场存在的不确定性，卖出META股票可能是较为谨慎的选择。",\n  "action": "sell"\n}\n```', 'action': 'sell', 'trading_actions': {'META': -1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-26'}
2025-07-29 04:54:23,398 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': -1.0}
2025-07-29 04:54:23,398 - __main__ - INFO - 📈 第1天交易动作: {'META': -1.0}
2025-07-29 04:54:23,403 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 04:54:56,553 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 04:54:56,553 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 04:55:31,281 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 04:55:31,281 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 04:55:31,281 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-27, 累计收益=0.0000, 周收益=0.0000
2025-07-29 04:55:46,070 - __main__ - INFO - 🎯 找到最终决策声明: sell
2025-07-29 04:55:46,070 - __main__ - WARNING - 修正决策不一致: 声明sell vs 字段hold
2025-07-29 04:55:46,070 - __main__ - INFO - 一致性修正：hold -> sell
2025-07-29 04:55:46,070 - __main__ - INFO - 🤖 TRA 输出: action=sell, position_size=1.0
2025-07-29 04:55:46,070 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: sell (信心度: 0.00)
2025-07-29 04:55:46,070 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 04:55:46,070 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 04:55:46,070 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对基础面进行了评估。根据FAA和NAA的分析结果，Meta Platforms (META) 的基本面表现稳健，财务健康度较高，估值合理。尽管近期有法律诉讼等负面新闻，但整体市场情绪较为稳定，没有出现重大负面消息。在技术面分析中，TAA显示META股票价格在经历了前期的下跌后，目前处于震荡状态，支撑位在$600，阻力位在$620-$630区间。技术指标方面，RSI可能低于50，MACD可能显示看跌信号，移动平均线可能短期均线低于长期均线，整体技术面偏向中性或看跌。展望层智能体中，BOA看涨信号强度较弱，BeOA看跌信号强度明显，NOA认为市场展望中性。综合考虑各层级信息，我们发现基础面和技术面存在一定分歧，风险控制方面需要谨慎。因此，**最终决策：卖出**。', 'action': 'sell', 'trading_actions': {'META': -1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-27'}
2025-07-29 04:55:46,070 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': -1.0}
2025-07-29 04:55:46,070 - __main__ - INFO - 📈 第2天交易动作: {'META': -1.0}
2025-07-29 04:55:46,074 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 04:56:45,753 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 04:56:45,754 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 04:57:16,726 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 04:57:16,728 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 04:57:16,728 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-28, 累计收益=0.0000, 周收益=0.0000
2025-07-29 04:57:30,583 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-29 04:57:30,584 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-29 04:57:30,584 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-29 04:57:30,584 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 04:57:30,584 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 04:57:30,584 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对META进行了全面的多维度分析。首先，从基础面来看，FAA显示META的财务健康度良好，估值合理，但NAA指出存在一些潜在的负面新闻，如股东权利律师事务所的调查和IPO价格低于预期等，这可能会对市场情绪产生一定影响。技术面分析显示，TAA指出META股价呈现下降趋势，动量较弱，支撑位和阻力位分析也显示出短期内股价可能面临压力。展望层分析中，BOA看涨信号强度不足，而BeOA看跌信号强度较高，NOA则认为市场存在不确定性。综合以上分析，我们认为各层级信号存在分歧，风险控制方面，信号一致性较低，因此建议保持观望态度。**最终决策：保持不变**', 'action': 'hold', 'trading_actions': {'__HOLD__': 0.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-28'}
2025-07-29 04:57:30,584 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 0.0}
2025-07-29 04:57:30,584 - __main__ - INFO - 📈 第3天交易动作: {'__HOLD__': 0.0}
2025-07-29 04:57:30,587 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 04:57:57,937 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 04:57:57,937 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 04:58:32,881 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 04:58:32,882 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 04:58:32,882 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-31, 累计收益=0.0000, 周收益=0.0000
2025-07-29 04:58:53,386 - __main__ - INFO - 🎯 找到最终决策声明: sell
2025-07-29 04:58:53,388 - __main__ - INFO - 🤖 TRA 输出: action=sell, position_size=1.0
2025-07-29 04:58:53,388 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: sell (信心度: 0.00)
2025-07-29 04:58:53,388 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 04:58:53,388 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 04:58:53,388 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '```json\n{\n  "analysis": "在本次分析中，我们首先对基础面进行了评估。根据FAA和NAA的分析结果，我们发现META公司的财务健康度良好，估值合理，且近期新闻对市场情绪的影响较为正面，尤其是科技和医疗行业。然而，我们也注意到一些中性情绪的新闻，如市场分析和对特定公司法律诉讼的报道，这表明市场情绪并非完全乐观。\n\n接下来，我们进行了技术面分析。TAA显示META股价呈现下降趋势，但最近几个交易日有轻微的反弹迹象。支撑位在$560-$565区域，阻力位在$580-$590区域。技术指标如RSI接近30，MACD看跌，移动平均线向下，表明短期内市场趋势偏空。\n\n在展望层综合分析中，BOA显示市场展望为看涨，主要基于情绪评分乐观和积极的市场因素。然而，BeOA显示市场展望为看跌，主要基于情绪评分中的中性情绪和潜在的市场风险。NOA认为市场展望为中性，市场情绪偏向乐观，但同时也存在中性情绪的新闻。\n\n在风险评估方面，我们发现各层级信号存在一定分歧，尤其是在基础面和技术面之间。因此，我们降低了仓位或保持观望。\n\n基于以上分析，**最终决策：卖出**。我们建议在当前市场环境下，考虑到技术面的看跌趋势和展望层信号的不一致性，采取卖出策略以规避潜在风险。",\n  "action": "sell"\n}\n```', 'action': 'sell', 'trading_actions': {'META': -1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-31'}
2025-07-29 04:58:53,388 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': -1.0}
2025-07-29 04:58:53,388 - __main__ - INFO - 📈 第4天交易动作: {'META': -1.0}
2025-07-29 04:58:53,392 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 04:59:34,691 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 04:59:34,693 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 04:59:58,550 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 04:59:58,550 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 04:59:58,550 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-01, 累计收益=0.0000, 周收益=0.0000
2025-07-29 05:00:18,752 - __main__ - WARNING - 未找到明确的最终决策声明，保持action=hold
2025-07-29 05:00:18,752 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-29 05:00:18,752 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-29 05:00:18,752 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 05:00:18,753 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 05:00:18,753 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '今日分析过程如下：\n\n1. **基础面评估**：根据FAA分析，META公司财务健康度良好，估值处于合理水平。NAA分析显示，今日没有相关市场新闻，因此无重大负面消息。综合来看，基础面评估为正面。\n\n2. **技术面分析**：TAA分析显示，META股价在近期经历了先下跌后上涨的趋势，4月1日显示出一定的上涨趋势。支撑位在$570.0附近，阻力位在$600.0附近。技术评分由于缺乏具体指标数据，无法确定。\n\n3. **展望层综合**：BOA分析显示市场展望看涨，BeOA分析显示市场展望看跌，NOA分析显示市场展望中性。由于缺乏具体数据，无法确定看涨或看跌信号的强度。\n\n4. **风险评估**：由于缺乏具体的技术指标和市场新闻，风险评估难以进行。但根据当前情况，建议保持现有的风险控制措施。\n\n**最终决策**：基于以上分析，考虑到基础面和技术面的正面信号，以及展望层的中性观点，最终决策为买入。\n\n', 'action': 'hold', 'trading_actions': {'__HOLD__': 0.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-01'}
2025-07-29 05:00:18,753 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 0.0}
2025-07-29 05:00:18,753 - __main__ - INFO - 📈 第5天交易动作: {'__HOLD__': 0.0}
2025-07-29 05:00:18,757 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-29 05:00:18,758 - __main__ - INFO - ============================================================
2025-07-29 05:00:18,758 - __main__ - INFO - 第 13 周性能分析 - 联盟: frozenset({'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'})
2025-07-29 05:00:18,758 - __main__ - INFO - 周总收益率: 0.0000
2025-07-29 05:00:18,758 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-29 05:00:18,758 - __main__ - INFO - 交易天数: 5
2025-07-29 05:00:18,758 - __main__ - INFO - ============================================================
2025-07-29 05:32:20,424 - __main__ - INFO - ======================================================================
2025-07-29 05:32:20,424 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-29 05:32:20,424 - __main__ - INFO - ======================================================================
2025-07-29 05:32:20,424 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-29 05:32:20,424 - __main__ - INFO - 开始模拟联盟: {'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'}
2025-07-29 05:32:20,424 - __main__ - INFO - 📅 当前周数: 14
2025-07-29 05:32:20,424 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-29 05:32:20,424 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'})
2025-07-29 05:32:20,425 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-04-02 (第14周)
2025-07-29 05:32:20,425 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-04-02, end_date=2025-04-09
2025-07-29 05:32:20,425 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-29 05:32:20,521 - __main__ - INFO - 📅 设置当前模拟周数: 14 (来自参数)
2025-07-29 05:32:20,522 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 05:32:50,490 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 05:32:50,492 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 05:33:02,991 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 05:33:02,992 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 05:33:02,992 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-02, 累计收益=0.0000, 周收益=0.0000
2025-07-29 05:33:15,890 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 05:33:15,892 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 05:33:15,892 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 05:33:15,892 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 05:33:15,892 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 05:33:15,892 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 05:33:15,893 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 05:33:15,893 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '今日分析过程如下：\n\n1. **基础面评估**：根据FAA和NAA的分析，META公司财务健康度良好，估值合理。尽管今日没有相关新闻，但根据历史数据，公司基本面稳健，无重大负面消息。\n\n2. **技术面分析**：TAA显示META价格波动较大，但短期内可能呈上升趋势。RSI和MACD指标显示市场处于中性状态，但移动平均线显示短期内价格可能呈上升趋势。\n\n3. **展望层综合**：BOA显示看涨信号，认为市场波动较大，但技术分析和财务健康评分均显示出积极信号。BeOA显示看跌信号，考虑到当前的市场环境和技术分析结果。NOA认为市场展望为中性，技术分析显示META股价波动较大，但短期内可能呈上升趋势。\n\n4. **风险评估**：各层级信号基本一致，分歧度较低。市场环境不确定性较高，但公司基本面稳健。\n\n**最终决策：买入**。理由是技术面显示短期内价格可能呈上升趋势，基础面良好，且展望层信号显示看涨。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-02'}
2025-07-29 05:33:15,893 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 05:33:15,893 - __main__ - INFO - 📈 第1天交易动作: {'META': 1.0}
2025-07-29 05:33:15,896 - __main__ - INFO - 💰 第1天净值变化: 收益率=0.0096, 净值=$1009649.30
2025-07-29 05:33:15,899 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 05:33:55,954 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 05:33:55,955 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 05:34:27,928 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 05:34:27,928 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 05:34:27,929 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-03, 累计收益=0.0096, 周收益=0.0096
2025-07-29 05:34:45,649 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 05:34:45,649 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 05:34:45,649 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 05:34:45,649 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 05:34:45,649 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 05:34:45,649 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 05:34:45,649 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 05:34:45,649 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '今日分析过程如下：\n\n1. **基础面评估**：FAA显示META公司财务健康度良好，估值合理，竞争力领先。NAA显示今日没有相关市场新闻，因此无法评估市场情绪变化，但根据历史数据，META公司基本面稳健，无重大负面消息。\n\n2. **技术面分析**：TAA显示META股票价格历史趋势显示为熊市，但RSI指标低于30，表明股票可能处于超卖状态，存在反弹机会。MACD线与信号线交叉向下，短期内可能存在下跌趋势，但长期移动平均线显示长期趋势可能向下。\n\n3. **展望层综合**：BOA显示看涨信号，但BeOA显示看跌信号，NOA认为市场展望中性。由于信号不一致，需谨慎对待。\n\n4. **风险评估**：各层级信号存在分歧，风险控制需加强。\n\n**最终决策：买入**。尽管存在信号分歧，但META公司基本面稳健，技术面显示超卖状态，存在反弹机会。综合考虑，决定买入META股票。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-03'}
2025-07-29 05:34:45,650 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 05:34:45,650 - __main__ - INFO - 📈 第2天交易动作: {'META': 1.0}
2025-07-29 05:34:45,656 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 05:35:30,742 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 05:35:30,743 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 05:35:55,965 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 05:35:55,965 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 05:35:55,965 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-04, 累计收益=0.0106, 周收益=0.0106
2025-07-29 05:36:14,707 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 05:36:14,708 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 05:36:14,708 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 05:36:14,708 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 05:36:14,708 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 05:36:14,708 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 05:36:14,708 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 05:36:14,708 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对Meta Platforms, Inc. (META)进行了全面的多维度分析。首先，从基础面评估来看，FAA基本面分析显示META的财务健康度良好，估值合理，且在竞争中处于领先地位。NAA新闻分析中，虽然存在一些负面新闻，如Bakkt Holdings, Inc. (BKKT)被提起集体诉讼，以及美国总统特朗普的新关税可能对大型科技公司的AI基础设施项目产生负面影响，但整体情绪评分较低，没有出现重大负面消息。技术面分析中，TAA显示META股票价格趋势呈现熊市，但RSI值低于30，表明股票可能处于超卖状态，存在反弹机会。展望层分析中，BOA看涨信号强度较高，主要基于META发布庆祝Blue Rodeo乐队的新邮票等积极因素。然而，BeOA看跌信号也较强，主要基于负面新闻和事件对投资者情绪的影响。NOA中性展望认为市场表现出一定的波动性，但未出现明显的看涨或看跌趋势。综合分析，各层级信号存在一定分歧，但考虑到META的基本面稳健和技术面的超卖状态，我们倾向于看涨。因此，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-04'}
2025-07-29 05:36:14,708 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 05:36:14,708 - __main__ - INFO - 📈 第3天交易动作: {'META': 1.0}
2025-07-29 05:36:14,712 - __main__ - INFO - 💰 第3天净值变化: 收益率=0.0101, 净值=$1020832.74
2025-07-29 05:36:14,715 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 05:36:55,048 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 05:36:55,050 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 05:37:20,373 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 05:37:20,374 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 05:37:20,374 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-07, 累计收益=0.0208, 周收益=0.0208
2025-07-29 05:37:34,962 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-29 05:37:34,963 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-29 05:37:34,963 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-29 05:37:34,964 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 05:37:34,964 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 05:37:34,964 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '今日分析结果显示，META股票的基础面评估显示公司财务健康度良好，估值合理，且在竞争中处于领先地位。然而，NAA新闻分析显示今日没有相关市场新闻，因此无法评估市场情绪变化。技术面分析显示，META股票价格趋势呈熊市，但RSI指标显示市场可能存在反弹机会。展望层智能体中，BOA看涨信号强度较弱，BeOA看跌信号强度较强，而NOA认为市场展望为中性。综合考虑各层级信息，基础面和技术面存在分歧，且展望层信号不一致，因此建议保持观望态度。**最终决策：保持不变**', 'action': 'hold', 'trading_actions': {'__HOLD__': 0.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-07'}
2025-07-29 05:37:34,964 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 0.0}
2025-07-29 05:37:34,964 - __main__ - INFO - 📈 第4天交易动作: {'__HOLD__': 0.0}
2025-07-29 05:37:34,966 - __main__ - INFO - 💰 第4天净值变化: 收益率=0.0036, 净值=$1024460.18
2025-07-29 05:37:34,968 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 05:38:11,971 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 05:38:11,972 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 05:38:39,766 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 05:38:39,766 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 05:38:39,766 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-08, 累计收益=0.0245, 周收益=0.0245
2025-07-29 05:38:46,053 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 05:38:46,053 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 05:38:46,053 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 05:38:46,053 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 05:38:46,053 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 05:38:46,053 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 05:38:46,053 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 05:38:46,053 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '```json\n{\n  "analysis": "在本次分析中，我们首先对基础面进行了评估。根据FAA的分析，META公司的财务健康度良好，估值合理，且在竞争中处于领先地位。NAA的分析显示，尽管4月8日没有相关新闻，但市场情绪稳定，没有出现重大负面消息。这表明基础面整体稳健。\n\n接下来，我们进行了技术面分析。TAA的分析指出，虽然4月8日股票价格有所下跌，但成交量减少，这可能表明市场参与者在价格下跌时持谨慎态度。此外，TAA初步判断当前趋势可能为看跌，但缺乏具体的技术指标值，因此分析信心度设定为0.5。\n\n在展望层综合分析中，BOA维持对META股票的看涨展望，尽管短期内价格有所下跌，但基于长期趋势和公司基本面分析，看涨信号依然存在。BeOA则认为市场目前显示出看跌趋势，尤其是考虑到TAA指出股票价格在2025-04-08有所下跌。NOA则认为当前市场展望为中性，主要基于缺乏市场新闻和关键事件。\n\n在风险评估方面，由于信号存在分歧，我们建议降低仓位或观望。综合考虑各层级信息，我们认为当前市场环境存在一定的不确定性。\n\n**最终决策：买入/卖出/保持不变**。基于以上分析，我们建议保持不变，因为基础面稳健，但技术面和展望层存在分歧，且市场环境存在不确定性。"\n\n  "action": "hold"\n}\n```', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-08'}
2025-07-29 05:38:46,054 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 05:38:46,054 - __main__ - INFO - 📈 第5天交易动作: {'META': 1.0}
2025-07-29 05:38:46,056 - __main__ - INFO - 💰 第5天净值变化: 收益率=0.0040, 净值=$1028589.45
2025-07-29 05:38:46,056 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-29 05:38:46,056 - __main__ - INFO - ============================================================
2025-07-29 05:38:46,056 - __main__ - INFO - 第 14 周性能分析 - 联盟: frozenset({'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'})
2025-07-29 05:38:46,056 - __main__ - INFO - 周总收益率: 0.0283
2025-07-29 05:38:46,056 - __main__ - INFO - 周夏普比率: 24.6146
2025-07-29 05:38:46,056 - __main__ - INFO - 交易天数: 5
2025-07-29 05:38:46,056 - __main__ - INFO - ============================================================
2025-07-29 06:10:39,738 - __main__ - INFO - ======================================================================
2025-07-29 06:10:39,738 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-29 06:10:39,738 - __main__ - INFO - ======================================================================
2025-07-29 06:10:39,738 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-29 06:10:39,739 - __main__ - INFO - 开始模拟联盟: {'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'}
2025-07-29 06:10:39,739 - __main__ - INFO - 📅 当前周数: 15
2025-07-29 06:10:39,739 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-29 06:10:39,739 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'})
2025-07-29 06:10:39,740 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-04-09 (第15周)
2025-07-29 06:10:39,740 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-04-09, end_date=2025-04-16
2025-07-29 06:10:39,740 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-29 06:10:39,828 - __main__ - INFO - 📅 设置当前模拟周数: 15 (来自参数)
2025-07-29 06:10:39,830 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 06:11:15,770 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 06:11:15,772 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 06:11:50,257 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 06:11:50,258 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 06:11:50,258 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-09, 累计收益=0.0000, 周收益=0.0000
2025-07-29 06:11:54,687 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 06:11:54,688 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 06:11:54,688 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 06:11:54,688 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 06:11:54,688 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 06:11:54,688 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 06:11:54,688 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 06:11:54,688 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '今日分析结果显示，META股票在基础面、技术面和展望层均显示出一定的看涨信号。在基础面评估中，FAA显示META公司财务健康，估值合理，且在新闻分析中未发现重大负面消息。技术面分析中，TAA显示META股票呈现出上升趋势，且成交量的增加支持了这一趋势。展望层分析中，BOA的看涨信号强于BeOA的看跌信号，NOA认为上涨概率较高。然而，由于缺乏具体的技术指标计算值，如RSI、MACD和移动平均线，我们无法对这些指标进行详细分析。在风险控制方面，由于信号一致性较高，我们建议保持谨慎，并密切关注市场动态。**最终决策：买入**', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-09'}
2025-07-29 06:11:54,688 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 06:11:54,688 - __main__ - INFO - 📈 第1天交易动作: {'META': 1.0}
2025-07-29 06:11:54,691 - __main__ - INFO - 💰 第1天净值变化: 收益率=0.0033, 净值=$1003345.41
2025-07-29 06:11:54,692 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 06:12:56,356 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 06:12:56,358 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 06:13:32,398 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 06:13:32,399 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 06:13:32,399 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-10, 累计收益=0.0033, 周收益=0.0033
2025-07-29 06:13:40,938 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 06:13:40,938 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 06:13:40,938 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 06:13:40,938 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 06:13:40,938 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 06:13:40,938 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 06:13:40,938 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 06:13:40,938 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '今日分析结果显示，META股票在基础面、技术面和展望层均显示出一定的复杂性。基础面分析显示，FAA给予META较高的财务健康评分和合理的估值评估，表明公司基本面稳健。然而，NAA未发现重大负面新闻，但也没有明显的正面消息，市场情绪较为平静。技术面分析显示，TAA指出META股价呈现熊市趋势，但存在潜在的支撑位，且RSI指标显示市场可能处于超卖状态。展望层分析中，BOA看好META的未来，但BeOA和NOA均未给出明确的看涨或看跌信号，而是保持中性态度。综合考虑，各层级信号存在一定分歧，但基础面和技术面分析显示META具备一定的投资价值。因此，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-10'}
2025-07-29 06:13:40,938 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 06:13:40,938 - __main__ - INFO - 📈 第2天交易动作: {'META': 1.0}
2025-07-29 06:13:40,940 - __main__ - INFO - 💰 第2天净值变化: 收益率=0.0078, 净值=$1011123.50
2025-07-29 06:13:40,941 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 06:14:39,144 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 06:14:39,145 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 06:15:05,171 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 06:15:05,171 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 06:15:05,171 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-11, 累计收益=0.0111, 周收益=0.0111
2025-07-29 06:15:23,567 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 06:15:23,567 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 06:15:23,567 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 06:15:23,567 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 06:15:23,567 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 06:15:23,567 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 06:15:23,567 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 06:15:23,567 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对基础面进行了评估。根据FAA和NAA的分析结果，Meta公司的财务健康度良好，估值合理，且在近期新闻中，尽管存在一些诉讼和关税问题，但也有关注到科技股的反弹和Meta推出新安全工具等积极消息。这表明基础面整体稳健，没有明显的恶化迹象。\n\n接下来，我们进行了技术面分析。TAA显示，Meta股票价格在近期呈现下跌趋势，技术指标如RSI、MACD和移动平均线均显示出看跌信号。这表明短期内股票可能继续下跌。\n\n展望层智能体中，BOA和BeOA分别给出了看涨和看跌的展望。然而，NOA认为市场展望为中性，这表明市场存在不确定性。在风险评估方面，我们发现各层级信号存在一定程度的分歧，特别是在基础面和技术面之间。\n\n综合以上分析，我们认为当前市场环境存在一定的不确定性，且技术面显示看跌信号。因此，我们决定采取观望策略，等待市场进一步明确方向。\n\n**最终决策：买入/卖出/保持不变**：保持不变', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-11'}
2025-07-29 06:15:23,568 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 06:15:23,568 - __main__ - INFO - 📈 第3天交易动作: {'META': 1.0}
2025-07-29 06:15:23,572 - __main__ - INFO - 💰 第3天净值变化: 收益率=0.0044, 净值=$1015556.18
2025-07-29 06:15:23,575 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 06:15:49,517 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 06:15:49,518 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 06:16:25,469 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 06:16:25,470 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 06:16:25,471 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-14, 累计收益=0.0156, 周收益=0.0156
2025-07-29 06:16:43,697 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 06:16:43,698 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 06:16:43,698 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 06:16:43,698 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 06:16:43,698 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 06:16:43,698 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 06:16:43,698 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 06:16:43,698 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '今日分析过程如下：\n\n1. **基础面评估**：根据FAA和NAA的分析，虽然今日没有相关新闻，但META公司的财务健康度良好，估值合理，且在竞争中处于领先地位。因此，基础面评估为正面。\n\n2. **技术面分析**：TAA显示META股票价格呈现波动性下降趋势，支撑位在$528.28，阻力位在$557.77。技术指标如RSI、MACD和移动平均线均指向看跌趋势，技术评分较低。\n\n3. **展望层综合**：BOA显示长期基本面分析显示积极信号，但BeOA认为技术分析显示看跌趋势。NOA认为当前市场展望为中性。因此，展望层信号存在分歧。\n\n4. **风险评估**：由于技术面信号显示看跌趋势，且展望层信号存在分歧，建议降低仓位或观望。\n\n**最终决策：买入/卖出/保持不变**', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-14'}
2025-07-29 06:16:43,699 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 06:16:43,699 - __main__ - INFO - 📈 第4天交易动作: {'META': 1.0}
2025-07-29 06:16:43,703 - __main__ - INFO - 💰 第4天净值变化: 收益率=0.0111, 净值=$1026860.89
2025-07-29 06:16:43,706 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 06:17:08,254 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 06:17:08,256 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 06:17:47,238 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 06:17:47,239 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 06:17:47,239 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-15, 累计收益=0.0269, 周收益=0.0269
2025-07-29 06:17:51,806 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 06:17:51,806 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 06:17:51,806 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 06:17:51,806 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 06:17:51,806 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 06:17:51,806 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 06:17:51,806 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 06:17:51,806 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在基础面评估中，FAA显示META公司财务健康度良好，估值合理，显示出公司的稳健性。NAA分析显示，尽管今日没有相关新闻，但市场情绪稳定，没有出现重大负面消息。在技术面分析中，TAA显示META股票在2025-04-15当天呈现看跌趋势，但RSI接近超卖区域，表明市场可能接近底部。展望层智能体中，BOA认为尽管TAA显示看跌，但结合其他分析，META有上涨潜力。BeOA则认为市场存在下跌风险，但NOA认为市场展望中性，上涨概率较高。在风险评估中，各层级信号存在一定分歧，但整体风险可控。综合以上分析，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-15'}
2025-07-29 06:17:51,806 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 06:17:51,806 - __main__ - INFO - 📈 第5天交易动作: {'META': 1.0}
2025-07-29 06:17:51,808 - __main__ - INFO - 💰 第5天净值变化: 收益率=-0.0276, 净值=$998564.26
2025-07-29 06:17:51,808 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-29 06:17:51,808 - __main__ - INFO - ============================================================
2025-07-29 06:17:51,809 - __main__ - INFO - 第 15 周性能分析 - 联盟: frozenset({'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'})
2025-07-29 06:17:51,809 - __main__ - INFO - 周总收益率: -0.0009
2025-07-29 06:17:51,809 - __main__ - INFO - 周夏普比率: -0.3049
2025-07-29 06:17:51,809 - __main__ - INFO - 交易天数: 5
2025-07-29 06:17:51,809 - __main__ - INFO - ============================================================
2025-07-29 06:49:44,879 - __main__ - INFO - ======================================================================
2025-07-29 06:49:44,879 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-29 06:49:44,879 - __main__ - INFO - ======================================================================
2025-07-29 06:49:44,879 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-29 06:49:44,879 - __main__ - INFO - 开始模拟联盟: {'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'}
2025-07-29 06:49:44,879 - __main__ - INFO - 📅 当前周数: 16
2025-07-29 06:49:44,880 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-29 06:49:44,880 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'})
2025-07-29 06:49:44,881 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-04-16 (第16周)
2025-07-29 06:49:44,881 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-04-16, end_date=2025-04-23
2025-07-29 06:49:44,881 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-29 06:49:44,969 - __main__ - INFO - 📅 设置当前模拟周数: 16 (来自参数)
2025-07-29 06:49:44,970 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 06:50:27,183 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 06:50:27,183 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 06:50:57,126 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 06:50:57,127 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 06:50:57,127 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-16, 累计收益=0.0000, 周收益=0.0000
2025-07-29 06:51:12,718 - __main__ - INFO - 🎯 找到最终决策声明: sell
2025-07-29 06:51:12,718 - __main__ - INFO - 🤖 TRA 输出: action=sell, position_size=1.0
2025-07-29 06:51:12,718 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: sell (信心度: 0.00)
2025-07-29 06:51:12,718 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 06:51:12,718 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 06:51:12,718 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '```json\n{\n  "analysis": "在本次分析中，我们首先对基础面进行了评估。根据FAA的分析，META公司的财务健康度良好，估值合理，且在竞争中处于领先地位。NAA的分析显示，尽管今日没有相关新闻，但市场情绪稳定，没有出现重大负面消息。这表明基础面整体稳健。\n\n接下来，我们进行了技术面分析。TAA的分析指出，META股价呈现下降趋势，但动量有所减弱，且接近潜在的支撑位。技术指标如RSI接近超卖区域，MACD看跌，移动平均线也显示看跌趋势，技术评分设定为-0.5。\n\n展望层智能体的分析中，BOA看好META的长期增长潜力，但TAA和BeOA的分析显示市场目前处于中性状态，且TAA指出META股价呈现下降趋势。NOA认为市场展望为中性，主要基于技术分析显示META股价呈现下降趋势，但动量有所减弱。\n\n在风险评估方面，我们注意到技术指标和价格趋势之间存在分歧，因此建议降低仓位或观望。\n\n综合以上分析，我们得出以下结论：**最终决策：卖出**。由于技术面显示看跌趋势，且基础面和技术面存在分歧，我们建议采取卖出策略，以规避潜在的风险。\n\n",\n  "action": "sell"\n}\n```', 'action': 'sell', 'trading_actions': {'META': -1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-16'}
2025-07-29 06:51:12,718 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': -1.0}
2025-07-29 06:51:12,718 - __main__ - INFO - 📈 第1天交易动作: {'META': -1.0}
2025-07-29 06:51:12,721 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 06:51:54,970 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 06:51:54,970 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 06:52:27,179 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 06:52:27,181 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 06:52:27,181 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-17, 累计收益=0.0000, 周收益=0.0000
2025-07-29 06:52:39,812 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 06:52:39,813 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 06:52:39,813 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 06:52:39,813 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 06:52:39,814 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 06:52:39,814 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 06:52:39,814 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 06:52:39,814 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在基础面评估中，FAA显示META公司财务健康度良好，估值合理，且在竞争中处于领先地位。NAA分析显示，尽管今日没有相关新闻，但市场情绪评分低，没有相关新闻，这可能对META股价产生负面影响。在技术面分析中，TAA显示META股价在短期内可能看跌，但长期趋势尚不明朗。展望层智能体中，BOA看好META的长期前景，而BeOA则认为短期内存在下跌风险，NOA则持中性观点。综合分析，虽然技术面显示短期看跌信号，但基础面和展望层智能体提供了一定的支撑。风险评估显示，各层级信号存在一定分歧，但整体风险可控。因此，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-17'}
2025-07-29 06:52:39,814 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 06:52:39,814 - __main__ - INFO - 📈 第2天交易动作: {'META': 1.0}
2025-07-29 06:52:39,817 - __main__ - INFO - 💰 第2天净值变化: 收益率=-0.0127, 净值=$987311.20
2025-07-29 06:52:39,818 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 06:52:39,819 - __main__ - INFO - TAA检测到无价格数据，建议跳过当前日期: 2025-04-18
2025-07-29 06:53:00,026 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 06:53:00,027 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 06:53:51,013 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 06:53:51,014 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 06:53:51,014 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-18, 累计收益=-0.0127, 周收益=-0.0127
2025-07-29 06:53:55,671 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 06:53:55,671 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 06:53:55,671 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 06:53:55,671 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 06:53:55,671 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 06:53:55,671 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 06:53:55,671 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 06:53:55,671 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在基础面评估中，FAA显示Meta的财务健康度良好，估值合理，显示出公司的领先竞争地位。NAA的分析结果显示，尽管存在一些负面新闻，如特斯拉涉嫌操纵里程表和英伟达股票下跌，但整体情绪中性或略微乐观，没有对Meta产生重大负面影响。在技术面分析中，由于TAA无法进行技术分析，我们无法提供具体的技术信号。在展望层综合中，BOA显示市场整体展望为看涨，尽管存在一些负面新闻和事件，但积极因素仍然占据主导地位。BeOA的分析显示，市场存在一些负面因素，但整体风险可控。NOA认为当前市场展望为中性，主要受多种因素影响。在风险评估中，考虑到新闻的来源和可信度，以及情绪的传播和放大效应，我们评估信号的一致性程度较高，潜在风险可控。综合以上分析，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-18'}
2025-07-29 06:53:55,672 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 06:53:55,672 - __main__ - INFO - 📈 第3天交易动作: {'META': 1.0}
2025-07-29 06:53:55,676 - __main__ - INFO - 💰 第3天净值变化: 收益率=-0.0162, 净值=$971269.02
2025-07-29 06:53:55,679 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 06:53:57,311 - __main__ - ERROR - LLM分析请求失败: Error code: 400, with error text {"contentFilter":[{"level":1,"role":"user"}],"error":{"code":"1301","message":"系统检测到输入或生成内容可能包含不安全或敏感内容，请您避免输入易产生敏感内容的提示语，感谢您的配合。"}}
2025-07-29 06:53:57,312 - __main__ - WARNING - 🤖 NAA: 数据验证失败，使用默认响应
2025-07-29 06:53:57,312 - __main__ - ERROR - 智能体 NAA LLM调用失败: the JSON object must be str, bytes or bytearray, not NoneType
2025-07-29 06:54:31,516 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 06:54:31,517 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 06:54:56,546 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 06:54:56,547 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 06:54:56,548 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-21, 累计收益=-0.0287, 周收益=-0.0287
2025-07-29 06:55:02,306 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 06:55:02,307 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 06:55:02,307 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 06:55:02,307 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 06:55:02,307 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 06:55:02,307 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 06:55:02,307 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 06:55:02,307 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在基础面评估中，FAA显示META公司财务健康，估值合理，同时NAA未发现重大负面消息，表明基本面稳健。技术面分析中，TAA显示META股价呈现下降趋势，但RSI接近超卖区域，暗示短期内可能存在反弹机会。展望层分析中，BOA认为尽管技术分析显示看跌趋势，但存在支持看涨的因素，而BeOA则认为市场展望为看跌。NOA认为市场目前呈现出中性至看跌的趋势，但存在不确定性。综合分析，虽然技术面显示看跌，但基本面稳健且存在看涨信号，因此最终决策为买入。风险评估显示各层级信号基本一致，分歧度较低，市场环境不确定性较高，但考虑到META的财务健康和潜在反弹机会，买入决策较为合理。**最终决策：买入**', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-21'}
2025-07-29 06:55:02,307 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 06:55:02,307 - __main__ - INFO - 📈 第4天交易动作: {'META': 1.0}
2025-07-29 06:55:02,312 - __main__ - INFO - 💰 第4天净值变化: 收益率=-0.0226, 净值=$949358.46
2025-07-29 06:55:02,315 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 06:55:53,999 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 06:55:54,000 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 06:56:17,723 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 06:56:17,724 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 06:56:17,724 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-22, 累计收益=-0.0506, 周收益=-0.0506
2025-07-29 06:56:34,182 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 06:56:34,183 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 06:56:34,183 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 06:56:34,183 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 06:56:34,183 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 06:56:34,183 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 06:56:34,183 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 06:56:34,183 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在基础面评估中，FAA显示Meta Platforms的财务健康度良好，且估值合理。NAA分析显示，尽管市场新闻以中性为主，但Meta Platforms和Amazon等大型科技公司受到关注，这可能对股价产生一定影响。在技术面分析中，TAA显示META股价在经历了先上涨后下跌的波动后，4月22日的收盘价较前一日有所上涨，显示出一定的短期动量。RSI值表明市场处于中立状态，MACD线在零轴上方，显示市场趋势可能看涨。展望层分析中，BOA看涨信号强于BeOA，NOA认为上涨概率较高。综合分析，各层级信号基本一致，分歧度较低，因此，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'META': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-22'}
2025-07-29 06:56:34,183 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'META': 1.0}
2025-07-29 06:56:34,183 - __main__ - INFO - 📈 第5天交易动作: {'META': 1.0}
2025-07-29 06:56:34,188 - __main__ - INFO - 💰 第5天净值变化: 收益率=-0.0159, 净值=$934254.09
2025-07-29 06:56:34,188 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-29 06:56:34,188 - __main__ - INFO - ============================================================
2025-07-29 06:56:34,188 - __main__ - INFO - 第 16 周性能分析 - 联盟: frozenset({'FAA', 'TAA', 'TRA', 'NOA', 'NAA', 'BOA', 'BeOA'})
2025-07-29 06:56:34,188 - __main__ - INFO - 周总收益率: -0.0674
2025-07-29 06:56:34,188 - __main__ - INFO - 周夏普比率: -28.8525
2025-07-29 06:56:34,188 - __main__ - INFO - 交易天数: 5
2025-07-29 06:56:34,190 - __main__ - INFO - ============================================================

日期	当日收益率	当日净值
2025-01-06	0.0344	$1,033,663.50
2025-01-07	-0.0040	$1,029,540.73
2025-01-08	-0.0105	$1,018,762.12
2025-01-08	0.0028	$1,002,829.36
2025-01-09	-0.0082	$994,564.19
2025-01-10	-0.0049	$989,694.26
2025-01-13	-0.0400	$950,117.46
2025-01-15	0.0121	$1,012,067.10
2025-01-16	0.0079	$1,020,057.72
2025-01-17	-0.0043	$1,015,674.60
2025-01-20	-0.0070	$1,008,549.78
2025-01-21	0.0107	$1,019,318.18
2025-01-22	0.0149	$1,014,917.45
2025-01-23	-0.0076	$1,007,237.53
2025-01-24	0.0090	$1,016,297.71
2025-01-27	0.0322	$1,049,052.40
2025-01-28	0.0351	$1,085,894.78
2025-01-29	0.0351	$1,035,119.68
2025-01-31	-0.0079	$1,027,157.87
2025-02-03	0.0244	$1,052,190.34
2025-02-04	-0.0164	$1,034,984.73
2025-02-05	0.0094	$1,009,371.38
2025-02-06	0.0216	$1,031,145.59
2025-02-07	-0.0030	$1,028,065.26
2025-02-10	-0.0166	$1,011,050.08
2025-02-11	0.0063	$1,017,390.03
2025-02-12	-0.0077	$992,310.65
2025-02-13	-0.0359	$956,667.31
2025-02-14	-0.0027	$954,072.15
2025-02-17	-0.0173	$937,540.05
2025-02-18	0.0249	$960,928.49
2025-02-19	0.0132	$1,013,169.96
2025-02-20	-0.0072	$1,005,834.79
2025-02-21	-0.0059	$999,933.32
2025-02-24	-0.0143	$985,646.41
2025-02-25	-0.0097	$976,094.02
2025-02-26	-0.0097	$990,308.50
2025-02-27	0.0234	$1,013,530.88
2025-02-28	0.0090	$1,022,647.32
2025-03-03	0.0423	$1,065,895.41
2025-03-06	-0.0116	$988,395.99
2025-03-07	0.0084	$996,714.63
2025-03-10	-0.0122	$984,527.99
2025-03-11	-0.0231	$961,740.76
2025-03-12	-0.0231	$976,854.67
2025-03-14	-0.0094	$967,642.04
2025-03-17	0.0024	$969,968.94
2025-03-18	0.0060	$975,809.94
2025-03-19	0.0114	$1,011,420.04
2025-03-20	0.0208	$1,032,427.08
2025-03-24	0.0191	$1,052,183.02
2025-03-25	0.0219	$1,075,223.64
2025-04-02	0.0096	$1,009,649.30
2025-04-04	0.0101	$1,020,832.74
2025-04-07	0.0036	$1,024,460.18
2025-04-08	0.0040	$1,028,589.45
2025-04-09	0.0033	$1,003,345.41
2025-04-10	0.0078	$1,011,123.50
2025-04-11	0.0044	$1,015,556.18
2025-04-14	0.0111	$1,026,860.89
2025-04-15	-0.0276	$998,564.26
2025-04-17	-0.0127	$987,311.20
2025-04-18	-0.0162	$971,269.02
2025-04-21	-0.0226	$949,358.46
2025-04-22	-0.0159	$934,254.09

