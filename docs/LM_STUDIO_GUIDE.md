# LM Studio 集成指南

## 🚀 概述

LM Studio 是一个本地运行大语言模型的桌面应用程序，支持通过 OpenAI 兼容的 API 接口与外部应用程序集成。本系统已集成 LM Studio 支持，让您可以使用本地模型进行分析。

## 📋 前置要求

### 1. 安装 LM Studio
- 从 [LM Studio 官网](https://lmstudio.ai/) 下载并安装
- 支持 Windows、macOS 和 Linux

### 2. 安装 Python 依赖
```bash
pip install openai
```

### 3. 下载并加载模型
1. 打开 LM Studio
2. 在 "Search" 标签页搜索并下载模型（推荐轻量级模型如 Llama 3.2 3B）
3. 在 "Chat" 标签页加载模型
4. 切换到 "Local Server" 标签页启动服务器

## ⚙️ 配置

### 1. 环境变量（可选）
```bash
# LM Studio 服务器地址（默认: http://localhost:1234/v1）
export LMSTUDIO_BASE_URL="http://localhost:1234/v1"

# API 密钥（默认: lm-studio）
export LMSTUDIO_API_KEY="lm-studio"
```

### 2. 系统配置
在 `config/system_config.json` 中设置：
```json
{
  "llm_integration": {
    "primary_provider": "lmstudio",
    "model_config": {
      "lmstudio": {
        "model": "your-loaded-model-name",
        "temperature": 0.0,
        "top_p": 0.5,
        "max_tokens": 1000,
        "timeout": 30
      }
    }
  }
}
```

## 🔧 使用方法

### 1. 启动 LM Studio 服务器
1. 打开 LM Studio
2. 在 "Local Server" 标签页中：
   - 选择要使用的模型
   - 点击 "Start Server"
   - 确认服务器运行在 `http://localhost:1234`

### 2. 检查连接
```bash
# 检查 LM Studio 环境
python check_llm_setup.py --provider lmstudio
```

### 3. 运行系统
```bash
# 使用 LM Studio 运行 OPRO 系统
python run_opro_system.py --provider lmstudio --mode evaluation

# 快速测试
python run_opro_system.py --provider lmstudio --mode evaluation --quick-test
```

### 4. 代码中使用
```python
from contribution_assessment.llm_interface import LLMInterface

# 创建 LM Studio 接口
llm = LLMInterface(provider="lmstudio")

# 进行分析
result = llm.analyze("分析当前市场趋势", model="your-model-name")
```

## 📊 模型推荐

### 轻量级模型（适合快速测试）
- **Llama 3.2 3B Instruct** - 平衡性能和速度
- **Phi-3 Mini** - 微软开发的小型模型
- **Gemma 2B** - Google 的轻量级模型

### 中等规模模型（更好性能）
- **Llama 3.1 8B Instruct** - 更强的推理能力
- **Mistral 7B Instruct** - 优秀的指令跟随能力
- **CodeLlama 7B** - 适合代码相关任务

### 获取模型名称
在 LM Studio 中加载模型后，可以通过以下方式获取准确的模型名称：
```bash
curl http://localhost:1234/v1/models
```

## 🔍 故障排除

### 1. 连接失败
- 确认 LM Studio 服务器已启动
- 检查端口是否被占用（默认 1234）
- 验证防火墙设置

### 2. 模型未找到
- 确认模型已在 LM Studio 中加载
- 检查配置文件中的模型名称是否正确
- 使用 `/v1/models` 端点查看可用模型

### 3. 响应缓慢
- 考虑使用更小的模型
- 调整 `max_tokens` 参数
- 检查系统资源使用情况

### 4. 内存不足
- 选择更小的模型
- 关闭其他占用内存的应用程序
- 考虑使用量化版本的模型

## 💡 最佳实践

### 1. 模型选择
- 开发测试：使用 3B 参数的模型
- 生产环境：根据硬件配置选择 7B-13B 模型
- 特殊任务：选择专门优化的模型

### 2. 性能优化
- 使用 GPU 加速（如果可用）
- 启用模型量化以减少内存使用
- 合理设置 `max_tokens` 避免过长响应

### 3. 稳定性
- 定期重启 LM Studio 服务器
- 监控内存使用情况
- 备份重要的模型配置

## 🔗 相关链接

- [LM Studio 官方文档](https://lmstudio.ai/docs)
- [OpenAI API 兼容性文档](https://lmstudio.ai/docs/api/openai-api)
- [模型下载中心](https://huggingface.co/models)

## 📝 注意事项

1. **本地运行**：LM Studio 在本地运行，无需互联网连接
2. **硬件要求**：确保有足够的 RAM 和 GPU 内存
3. **模型许可**：注意模型的使用许可和限制
4. **数据隐私**：所有数据处理都在本地进行，保护隐私安全
