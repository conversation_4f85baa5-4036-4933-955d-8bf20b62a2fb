# 需求文档

## 介绍

本规格说明旨在解决多智能体优化系统中服务接口不匹配导致的运行失败问题。当前系统在执行周期性优化时出现多个关键错误，包括TradingSimulator缺少必要方法、MockShapleyService参数不匹配、以及AssessmentResult对象属性缺失等问题。这些问题阻止了系统的正常运行，需要通过统一服务接口和完善Mock实现来解决。

## 需求

### 需求 1

**用户故事：** 作为系统开发者，我希望TradingSimulator具有完整的联盟模拟接口，以便PhaseCoordinator能够正确调用交易模拟功能

#### 验收标准

1. 当PhaseCoordinator调用simulation_service.simulate_coalitions_batch时，TradingSimulator应该具有simulate_coalition方法
2. 当TradingSimulator接收联盟列表时，应该能够为每个联盟执行交易模拟
3. 当模拟完成时，应该返回包含效用值的结果对象
4. 如果模拟失败，应该提供清晰的错误信息而不是AttributeError

### 需求 2

**用户故事：** 作为系统开发者，我希望MockShapleyService与IShapleyService接口完全兼容，以便在测试和开发环境中能够正常运行

#### 验收标准

1. 当PhaseCoordinator调用shapley_service.calculate_periodic_shapley时，MockShapleyService应该接受所有必要的参数包括target_agents
2. 当MockShapleyService接收到联盟效用数据时，应该能够计算并返回Shapley值
3. 当计算完成时，应该返回符合预期格式的Shapley结果对象
4. 如果参数不匹配，应该提供参数兼容性而不是抛出unexpected keyword argument错误

### 需求 3

**用户故事：** 作为系统开发者，我希望AssessmentResult对象具有完整的属性结构，以便系统能够正确访问评估结果

#### 验收标准

1. 当AssessmentResult对象被创建时，应该包含coalition_result属性
2. 当系统尝试访问coalition_result时，应该返回有效的联盟结果数据
3. 当AssessmentResult包含错误信息时，应该提供结构化的错误详情
4. 如果某些属性缺失，应该提供默认值而不是AttributeError

### 需求 4

**用户故事：** 作为系统开发者，我希望服务工厂能够正确创建和注入真实服务实现，以便替换当前的Mock服务

#### 验收标准

1. 当ServiceFactory创建PhaseCoordinator时，应该能够选择注入真实服务或Mock服务
2. 当配置指定使用真实服务时，应该创建完整功能的服务实现
3. 当服务创建失败时，应该提供清晰的错误信息和回退机制
4. 如果依赖注入出现问题，应该记录详细的诊断信息

### 需求 5

**用户故事：** 作为系统运维人员，我希望系统具有更好的错误处理和诊断能力，以便快速定位和解决运行时问题

#### 验收标准

1. 当任何阶段执行失败时，应该提供详细的错误上下文信息
2. 当服务接口不匹配时，应该提供具体的接口差异说明
3. 当Mock服务被使用时，应该在日志中明确标识
4. 如果系统回退到原始实现失败，应该提供替代的恢复策略

### 需求 6

**用户故事：** 作为系统测试人员，我希望能够在不同的服务实现之间切换，以便进行全面的集成测试

#### 验收标准

1. 当运行测试时，应该能够通过配置选择Mock服务或真实服务
2. 当使用Mock服务时，应该提供与真实服务相同的接口和行为
3. 当切换服务实现时，不应该影响其他组件的正常运行
4. 如果服务实现不兼容，应该在启动时进行验证并报告问题