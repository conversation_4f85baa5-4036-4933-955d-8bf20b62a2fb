# 改进子集联盟检测功能需求文档

## 介绍

当前的周期性优化系统在阶段2（子集联盟运行）中遇到问题：系统无法正确获取子集联盟，导致跳过了重要的子集联盟模拟阶段。这影响了Shapley值计算的准确性，因为Shapley值需要所有可能联盟的特征函数值来进行精确计算。

## 需求

### 需求1：子集联盟获取功能

**用户故事：** 作为系统管理员，我希望系统能够正确获取和管理所有子集联盟，以便在周期性优化中进行完整的联盟模拟。

#### 验收标准

1. WHEN 完整联盟完成一周交易 THEN 系统 SHALL 能够获取到所有有效的子集联盟
2. WHEN 获取子集联盟时 THEN 系统 SHALL 排除完整联盟本身，只返回真正的子集
3. WHEN 子集联盟列表为空时 THEN 系统 SHALL 记录详细的调试信息说明原因
4. WHEN 子集联盟获取失败时 THEN 系统 SHALL 提供备用的联盟生成方案

### 需求2：联盟状态持久化

**用户故事：** 作为开发者，我希望联盟管理器能够持久化联盟状态，以便在周期性优化时能够访问之前生成的联盟数据。

#### 验收标准

1. WHEN 联盟生成完成时 THEN 系统 SHALL 将有效联盟保存到联盟管理器的状态中
2. WHEN 周期性优化触发时 THEN 系统 SHALL 能够从联盟管理器获取之前保存的联盟数据
3. WHEN 联盟数据不存在时 THEN 系统 SHALL 能够重新生成联盟数据
4. WHEN 联盟数据过期时 THEN 系统 SHALL 自动刷新联盟数据

### 需求3：子集联盟模拟优化

**用户故事：** 作为系统用户，我希望子集联盟模拟能够高效运行，不会因为联盟数量过多而影响系统性能。

#### 验收标准

1. WHEN 子集联盟数量超过阈值时 THEN 系统 SHALL 智能选择最重要的联盟进行模拟
2. WHEN 运行子集联盟模拟时 THEN 系统 SHALL 使用简洁日志模式以提高性能
3. WHEN 子集联盟模拟完成时 THEN 系统 SHALL 提供模拟结果的统计摘要
4. WHEN 子集联盟模拟失败时 THEN 系统 SHALL 继续执行后续阶段而不中断整个流程

### 需求4：调试和监控增强

**用户故事：** 作为开发者，我希望系统提供详细的调试信息，以便快速定位和解决子集联盟相关的问题。

#### 验收标准

1. WHEN 子集联盟获取过程开始时 THEN 系统 SHALL 记录联盟管理器的当前状态
2. WHEN 子集联盟过滤过程执行时 THEN 系统 SHALL 记录过滤前后的联盟数量
3. WHEN 子集联盟模拟过程中 THEN 系统 SHALL 提供进度指示和性能指标
4. WHEN 任何阶段出现异常时 THEN 系统 SHALL 记录详细的错误信息和上下文

### 需求5：备用联盟生成机制

**用户故事：** 作为系统管理员，我希望当主要的联盟获取方法失败时，系统能够使用备用方法生成必要的子集联盟。

#### 验收标准

1. WHEN 主要联盟获取方法失败时 THEN 系统 SHALL 自动尝试备用联盟生成方法
2. WHEN 使用备用方法时 THEN 系统 SHALL 基于当前目标智能体动态生成关键子集联盟
3. WHEN 备用联盟生成完成时 THEN 系统 SHALL 验证生成的联盟是否满足Shapley计算的最低要求
4. WHEN 所有联盟获取方法都失败时 THEN 系统 SHALL 记录警告但继续执行Shapley计算

### 需求6：联盟数据一致性

**用户故事：** 作为系统架构师，我希望确保联盟数据在整个评估流程中保持一致性，避免数据不同步导致的计算错误。

#### 验收标准

1. WHEN 联盟生成阶段完成时 THEN 系统 SHALL 确保联盟数据在所有相关组件中同步
2. WHEN 周期性优化开始时 THEN 系统 SHALL 验证联盟数据的完整性和有效性
3. WHEN 发现数据不一致时 THEN 系统 SHALL 自动修复或重新生成联盟数据
4. WHEN 联盟数据更新时 THEN 系统 SHALL 通知所有依赖组件进行相应更新