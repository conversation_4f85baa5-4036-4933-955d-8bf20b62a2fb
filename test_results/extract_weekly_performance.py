#!/usr/bin/env python3
"""
提取日志文件中的周性能分析数据
"""

import re
import json
from datetime import datetime

def extract_weekly_performance(log_file_path):
    """从日志文件中提取17周的性能分析数据"""
    
    weekly_data = []
    
    with open(log_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 使用正则表达式匹配周性能分析块
    # 匹配模式：以"第 X 周性能分析"开始，到下一个分隔符结束
    pattern = r'============================================================\n.*?第 (\d+) 周性能分析 - 联盟: (.*?)\n.*?周总收益率: ([-\d\.]+)\n.*?周夏普比率: ([-\d\.]+)\n.*?交易天数: (\d+)\n.*?============================================================'
    
    matches = re.findall(pattern, content, re.DOTALL)
    
    # 目标联盟：完整的7个智能体
    target_agents = {'FAA', 'TAA', 'BOA', 'BeOA', 'TRA', 'NAA', 'NOA'}
    
    for match in matches:
        week_num = int(match[0])
        coalition = match[1].strip()
        return_rate = float(match[2])
        sharpe_ratio = float(match[3])
        trading_days = int(match[4])
        
        # 解析frozenset字符串，提取智能体集合
        # 从 "frozenset({'FAA', 'BeOA', ...})" 中提取智能体名称
        if coalition.startswith("frozenset(") and coalition.endswith(")"):
            agents_str = coalition[10:-1]  # 去掉 "frozenset(" 和 ")"
            if agents_str.startswith("{") and agents_str.endswith("}"):
                agents_str = agents_str[1:-1]  # 去掉 "{" 和 "}"
                # 解析智能体名称
                agents = set()
                for agent in agents_str.split(", "):
                    agent = agent.strip().strip("'\"")
                    if agent:
                        agents.add(agent)
                
                # 只保留包含完整7个智能体的联盟记录
                if agents == target_agents:
                    weekly_data.append({
                        "周数": week_num,
                        "联盟": coalition,
                        "周总收益率": return_rate,
                        "周夏普比率": sharpe_ratio,
                        "交易天数": trading_days
                    })
    
    # 按周数排序
    weekly_data.sort(key=lambda x: x["周数"])
    
    return weekly_data

def main():
    log_file_path = "/Users/<USER>/Code/Multi_Agent_Optimization/test_results/GOOG_0101_0430.log"
    
    print("开始提取日志文件中的周性能分析数据...")
    weekly_performance = extract_weekly_performance(log_file_path)
    
    print(f"\n提取到 {len(weekly_performance)} 条周性能记录:\n")
    
    print("="*80)
    print(f"{'周数':>4} | {'联盟':^50} | {'收益率':>8} | {'夏普比率':>10} | {'交易天数':>6}")
    print("="*80)
    
    for data in weekly_performance:
        print(f"{data['周数']:>4} | {data['联盟']:<50} | {data['周总收益率']:>8.4f} | {data['周夏普比率']:>10.4f} | {data['交易天数']:>6}")
    
    print("="*80)
    
    # 保存为JSON文件
    output_file = "/Users/<USER>/Code/Multi_Agent_Optimization/weekly_performance_summary.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(weekly_performance, f, ensure_ascii=False, indent=2)
    
    print(f"\n详细数据已保存到: {output_file}")
    
    # 统计信息
    if weekly_performance:
        total_returns = [data['周总收益率'] for data in weekly_performance]
        avg_return = sum(total_returns) / len(total_returns)
        
        # 计算累计收益率 (复合收益)
        cumulative_return = 1.0  # 初始投资为1
        for weekly_return in total_returns:
            cumulative_return *= (1 + weekly_return)
        
        total_return = cumulative_return - 1  # 总收益率
        
        print(f"\n统计摘要:")
        print(f"总周数: {len(weekly_performance)}")
        print(f"平均周收益率: {avg_return:.4f} ({avg_return*100:.2f}%)")
        print(f"总收益率 (累计): {total_return:.4f} ({total_return*100:.2f}%)")
        print(f"年化收益率 (假设52周/年): {((cumulative_return)**(52/len(weekly_performance)) - 1):.4f} ({((cumulative_return)**(52/len(weekly_performance)) - 1)*100:.2f}%)")
        print(f"最高周收益率: {max(total_returns):.4f} ({max(total_returns)*100:.2f}%)")
        print(f"最低周收益率: {min(total_returns):.4f} ({min(total_returns)*100:.2f}%)")
        
        # 计算盈利周数和亏损周数
        profit_weeks = len([r for r in total_returns if r > 0])
        loss_weeks = len([r for r in total_returns if r < 0])
        flat_weeks = len([r for r in total_returns if r == 0])
        
        print(f"\n周度表现分布:")
        print(f"盈利周数: {profit_weeks} ({profit_weeks/len(total_returns)*100:.1f}%)")
        print(f"亏损周数: {loss_weeks} ({loss_weeks/len(total_returns)*100:.1f}%)")
        print(f"平盘周数: {flat_weeks} ({flat_weeks/len(total_returns)*100:.1f}%)")
        
        # 找出表现最好和最差的周
        best_week = max(weekly_performance, key=lambda x: x['周总收益率'])
        worst_week = min(weekly_performance, key=lambda x: x['周总收益率'])
        
        print(f"\n表现最好的周: 第{best_week['周数']}周, 收益率: {best_week['周总收益率']:.4f} ({best_week['周总收益率']*100:.2f}%)")
        print(f"表现最差的周: 第{worst_week['周数']}周, 收益率: {worst_week['周总收益率']:.4f} ({worst_week['周总收益率']*100:.2f}%)")
        
        # 计算累计收益率曲线
        print(f"\n累计收益率曲线:")
        cumulative = 1.0
        for i, data in enumerate(weekly_performance):
            cumulative *= (1 + data['周总收益率'])
            print(f"第{data['周数']:2d}周结束: 累计收益率 = {(cumulative-1)*100:6.2f}%, 净值 = {cumulative:.4f}")
        
        print(f"\n最终结果:")
        print(f"初始投资: 100%")
        print(f"最终净值: {cumulative:.4f}")
        print(f"总收益率: {(cumulative-1)*100:.2f}%")

if __name__ == "__main__":
    main()
