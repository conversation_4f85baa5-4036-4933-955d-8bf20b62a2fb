#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的净值变化行提取脚本
"""

import sys
import re


def extract_and_display_net_value_changes(file_path: str):
    """
    提取并显示包含"💰 第X天净值变化"的行

    Args:
        file_path: 日志文件路径
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        net_value_lines = []
        for line_num, line in enumerate(lines, 1):
            if '💰 第' in line and '天净值变化' in line:
                net_value_lines.append((line_num, line.strip()))

        if not net_value_lines:
            print("未找到包含'💰 第X天净值变化'的行")
            return

        print(f"找到 {len(net_value_lines)} 条净值变化记录:\n")
        print("行号\t时间戳\t\t\t天数\t收益率\t\t净值")
        print("-" * 80)

        for line_num, line in net_value_lines:
            # 解析行内容
            match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}).*?💰 第(\d+)天净值变化: 收益率=([^,]+), 净值=\$(.+)', line)
            if match:
                timestamp = match.group(1)
                day_number = match.group(2)
                return_rate = match.group(3)
                net_value = match.group(4)
                print(f"{line_num}\t{timestamp}\t第{day_number}天\t{return_rate}\t\t${net_value}")
            else:
                print(f"{line_num}\t{line}")

        # 统计信息
        print(f"\n统计信息:")
        print(f"总记录数: {len(net_value_lines)}")

        # 解析收益数据进行统计
        returns = []
        net_values = []
        for _, line in net_value_lines:
            match = re.search(r'收益率=([^,]+), 净值=\$(.+)', line)
            if match:
                try:
                    returns.append(float(match.group(1)))
                    net_values.append(float(match.group(2)))
                except ValueError:
                    pass

        if returns:
            positive = sum(1 for r in returns if r > 0)
            negative = sum(1 for r in returns if r < 0)
            zero = sum(1 for r in returns if r == 0)

            print(f"正收益记录: {positive}")
            print(f"负收益记录: {negative}")
            print(f"零收益记录: {zero}")
            print(f"最大收益率: {max(returns):.4f}")
            print(f"最小收益率: {min(returns):.4f}")

        if net_values:
            print(f"最高净值: ${max(net_values):,.2f}")
            print(f"最低净值: ${min(net_values):,.2f}")

    except FileNotFoundError:
        print(f"错误: 文件 '{file_path}' 不存在")
    except Exception as e:
        print(f"处理文件时出错: {e}")


def main():
    """主函数"""
    # 默认文件路径
    default_file = "test_results/NVDA_phase1.md"

    # 获取输入文件路径
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    else:
        input_file = default_file

    print(f"正在提取文件中的净值变化行: {input_file}\n")
    extract_and_display_net_value_changes(input_file)


if __name__ == "__main__":
    main()
