"""
模拟服务实现

提供交易模拟和性能评估的服务实现，包括：
- 单个联盟和批量联盟模拟
- 并发执行管理和资源控制
- 性能指标计算和监控
- 与基础设施组件集成
"""

import time
import uuid
import threading
import concurrent.futures
from typing import Dict, Any, List, Set, Optional, FrozenSet
from datetime import datetime
import logging
from collections import defaultdict
import numpy as np

from ..interfaces.simulation_service import (
    ISimulationService,
    SimulationError,
    ConfigurationError,
    SimulationTimeoutError
)
from ..dto.phase_results_dto import SimulationResult
from ..trading_simulator import TradingSimulator
from ..infrastructure.event_bus import IEventBus
from ..infrastructure.error_handler import IErrorHandler, ErrorContext
from ..infrastructure.configuration_manager import IConfigurationManager


class SimulationService(ISimulationService):
    """
    模拟服务实现
    
    负责执行交易模拟和性能评估，支持单个联盟和批量模拟，
    集成了并发执行管理、性能监控和错误处理等功能。
    """
    
    def __init__(self,
                 trading_simulator: Optional[TradingSimulator] = None,
                 event_bus: Optional[IEventBus] = None,
                 error_handler: Optional[IErrorHandler] = None,
                 config_manager: Optional[IConfigurationManager] = None,
                 logger: Optional[logging.Logger] = None):
        """
        初始化模拟服务
        
        Args:
            trading_simulator: 交易模拟器实例
            event_bus: 事件总线实例
            error_handler: 错误处理器实例
            config_manager: 配置管理器实例
            logger: 日志记录器
        """
        self.logger = logger or self._create_default_logger()
        self.trading_simulator = trading_simulator
        self.event_bus = event_bus
        self.error_handler = error_handler
        self.config_manager = config_manager
        
        # 服务统计信息
        self._stats = {
            "total_simulations": 0,
            "successful_simulations": 0,
            "failed_simulations": 0,
            "total_execution_time": 0.0,
            "average_execution_time": 0.0,
            "last_simulation_time": None,
            "concurrent_stats": {
                "total_concurrent_tasks": 0,
                "successful_concurrent_tasks": 0,
                "failed_concurrent_tasks": 0,
                "concurrent_execution_time": 0.0
            },
            "performance_metrics": {
                "min_execution_time": float('inf'),
                "max_execution_time": 0.0,
                "total_coalitions_simulated": 0,
                "average_performance_score": 0.0
            }
        }
        
        # 从配置管理器加载配置
        self._config = self._load_service_config()
        
        # 活动模拟任务跟踪
        self._active_simulations = {}
        self._simulation_lock = threading.Lock()
        
        # 模拟历史记录
        self._simulation_history = []
        self._history_lock = threading.Lock()
        
        # 并发控制
        self._max_concurrent_simulations = self._config.get("max_concurrent_simulations", 10)
        self._current_concurrent_count = 0
        
        # 注册错误恢复策略
        self._register_error_recovery_strategies()
        
        # 订阅相关事件
        self._subscribe_to_events()
        
        self.logger.info("模拟服务初始化完成")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.SimulationService")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def simulate_coalition(
        self, 
        coalition: FrozenSet[str],
        agents: Dict[str, Any],
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        模拟单个联盟的交易表现
        
        Args:
            coalition: 要模拟的联盟
            agents: 智能体配置
            config: 模拟配置参数
            
        Returns:
            Dict[str, Any]: 模拟结果，包含性能指标和详细数据
            
        Raises:
            SimulationError: 模拟执行失败时抛出
        """
        simulation_id = str(uuid.uuid4())
        start_time = time.time()
        
        # 发布模拟开始事件
        self._publish_event("simulation_started", {
            "simulation_id": simulation_id,
            "coalition": list(coalition),
            "coalition_size": len(coalition)
        })
        
        # 记录活动模拟
        with self._simulation_lock:
            self._active_simulations[simulation_id] = {
                "coalition": coalition,
                "start_time": start_time,
                "status": "running",
                "progress": 0.0
            }
        
        try:
            # 验证配置
            effective_config = self._merge_configs(config)
            if not self.validate_simulation_config(effective_config):
                raise ConfigurationError("无效的模拟配置")
            
            # 验证智能体配置
            if not agents:
                raise SimulationError("智能体配置不能为空")
            
            # 检查联盟中的智能体是否都存在
            missing_agents = coalition - set(agents.keys())
            if missing_agents:
                raise SimulationError(f"联盟中缺少智能体: {missing_agents}")
            
            self.logger.info(f"开始模拟联盟: {set(coalition)}")
            
            # 更新进度
            self._update_simulation_progress(simulation_id, 0.1)
            
            # 获取模拟参数
            simulation_days = effective_config.get("simulation_days")
            current_week_number = effective_config.get("current_week_number")
            
            # 确保current_week_number有值，默认为1
            if current_week_number is None:
                current_week_number = 1
                self.logger.info(f"⚠️ 未指定当前周数，使用默认值: {current_week_number}")
            else:
                self.logger.info(f"📅 当前周数: {current_week_number}")
            
            # 调用交易模拟器
            if not self.trading_simulator:
                raise SimulationError("交易模拟器未初始化")
            
            # 更新进度
            self._update_simulation_progress(simulation_id, 0.2)
            
            # 判断是否为完整联盟（包含所有可用智能体）
            all_available_agents = set(agents.keys())
            is_full_coalition = set(coalition) == all_available_agents
            
            self.logger.info(f"📊 联盟类型判断: {'完整联盟' if is_full_coalition else '子集联盟'} "
                           f"({len(coalition)}/{len(all_available_agents)} 智能体)")
            
            # 执行模拟（启用5日循环模式）
            simulation_result = self.trading_simulator.run_simulation_for_coalition(
                coalition,
                agents,
                simulation_days,
                current_week_number=current_week_number,  # 显式指定参数名
                stop_after_one_week=True,  # 启用5日循环模式
                is_full_coalition=is_full_coalition,  # 传递联盟类型标志
                config=config  # 传递配置以支持状态继承
            )
            
            # 更新进度
            self._update_simulation_progress(simulation_id, 0.8)
            
            # 处理模拟结果
            if isinstance(simulation_result, dict):
                sharpe_ratio = simulation_result.get("sharpe_ratio", 0.0)
                daily_returns = simulation_result.get("daily_returns", [])
                weekly_data = simulation_result.get("weekly_data", [])
                total_days = simulation_result.get("total_days", 0)
                simulation_time = simulation_result.get("simulation_time", 0.0)
                error = simulation_result.get("error")
                
                if error:
                    raise SimulationError(f"模拟执行失败: {error}")
            else:
                # 简单返回值处理
                sharpe_ratio = float(simulation_result)
                daily_returns = []
                weekly_data = []
                total_days = 0
                simulation_time = 0.0
            
            # 计算性能指标
            performance_metrics = self.calculate_performance_metrics(daily_returns)
            
            # 更新进度
            self._update_simulation_progress(simulation_id, 1.0)
            
            execution_time = time.time() - start_time
            
            # 构建结果
            result = {
                "simulation_id": simulation_id,
                "coalition": coalition,
                "sharpe_ratio": sharpe_ratio,
                "daily_returns": daily_returns,
                "weekly_data": weekly_data,
                "total_days": total_days,
                "simulation_time": simulation_time,
                "performance_metrics": performance_metrics,
                "execution_time": execution_time,
                "timestamp": datetime.now().isoformat(),
                "success": True
            }
            
            # 更新统计信息
            self._update_stats(execution_time, success=True)
            
            # 记录历史
            self._add_to_history(simulation_id, coalition, result)
            
            # 发布成功事件
            self._publish_event("simulation_completed", {
                "simulation_id": simulation_id,
                "coalition": list(coalition),
                "sharpe_ratio": sharpe_ratio,
                "execution_time": execution_time
            })
            
            self.logger.info(f"联盟模拟完成: {set(coalition)}, 夏普比率={sharpe_ratio:.4f}, 耗时={execution_time:.3f}s")
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            
            # 处理错误
            error_context = ErrorContext(
                operation="simulate_coalition",
                component="SimulationService",
                additional_data={
                    "coalition": list(coalition),
                    "coalition_size": len(coalition),
                    "simulation_id": simulation_id
                }
            )
            
            if self.error_handler:
                error_info = self.error_handler.handle_error(e, error_context)
                error_message = f"联盟模拟失败: {error_info.message}"
            else:
                error_message = f"联盟模拟失败: {str(e)}"
            
            # 更新统计
            self._update_stats(execution_time, success=False)
            
            # 发布失败事件
            self._publish_event("simulation_failed", {
                "simulation_id": simulation_id,
                "coalition": list(coalition),
                "error": str(e),
                "execution_time": execution_time
            })
            
            self.logger.error(error_message)
            
            # 记录失败历史
            self._add_to_history(simulation_id, coalition, {
                "error": error_message,
                "execution_time": execution_time,
                "success": False
            })
            
            raise SimulationError(error_message) from e
        
        finally:
            # 清理活动模拟记录
            with self._simulation_lock:
                if simulation_id in self._active_simulations:
                    del self._active_simulations[simulation_id]
    
    def simulate_coalitions_batch(
        self,
        coalitions: Set[FrozenSet[str]],
        agents: Dict[str, Any],
        config: Optional[Dict[str, Any]] = None,
        max_concurrent: int = 5
    ) -> SimulationResult:
        """
        批量模拟多个联盟
        
        Args:
            coalitions: 要模拟的联盟集合
            agents: 智能体配置
            config: 模拟配置参数
            max_concurrent: 最大并发数
            
        Returns:
            SimulationResult: 批量模拟结果
            
        Raises:
            SimulationError: 批量模拟失败时抛出
        """
        start_time = time.time()
        
        # 发布批量模拟开始事件
        self._publish_event("batch_simulation_started", {
            "total_coalitions": len(coalitions),
            "max_concurrent": max_concurrent
        })
        
        try:
            # 验证输入
            if not coalitions:
                raise SimulationError("联盟集合不能为空")
            
            if not agents:
                raise SimulationError("智能体配置不能为空")
            
            # 合并配置
            effective_config = self._merge_configs(config)
            
            # 限制并发数
            actual_max_concurrent = max(max_concurrent, self._max_concurrent_simulations)
            
            self.logger.info(f"开始批量模拟: {len(coalitions)} 个联盟，最大并发数: {actual_max_concurrent}")
            
            # 分阶段执行：完整联盟详细日志 + 子集联盟简洁日志
            result = self._run_phased_simulation(coalitions, agents, effective_config, actual_max_concurrent)
            
            execution_time = time.time() - start_time
            
            # 创建结果对象
            simulation_result = SimulationResult(
                success=True,
                execution_time=execution_time,
                coalition_values=result["coalition_values"],
                coalition_daily_returns=result["coalition_daily_returns"],
                active_agents=result.get("active_agents", agents),  # 包含运行时智能体实例
                simulation_stats=result["simulation_stats"],
                simulator_stats=result.get("simulator_stats", {}),
                concurrent_stats=result.get("concurrent_stats")
            )
            
            # 发布成功事件
            self._publish_event("batch_simulation_completed", {
                "total_coalitions": len(coalitions),
                "successful_simulations": simulation_result.simulation_stats.get("successful_simulations", 0),
                "failed_simulations": simulation_result.simulation_stats.get("failed_simulations", 0),
                "execution_time": execution_time
            })
            
            self.logger.info(f"批量模拟完成: 成功 {simulation_result.simulation_stats.get('successful_simulations', 0)} 个, "
                           f"失败 {simulation_result.simulation_stats.get('failed_simulations', 0)} 个, "
                           f"耗时 {execution_time:.3f}s")
            
            return simulation_result
            
        except Exception as e:
            execution_time = time.time() - start_time
            
            # 处理错误
            error_context = ErrorContext(
                operation="simulate_coalitions_batch",
                component="SimulationService",
                additional_data={
                    "coalitions_count": len(coalitions),
                    "max_concurrent": max_concurrent
                }
            )
            
            if self.error_handler:
                error_info = self.error_handler.handle_error(e, error_context)
                error_message = f"批量模拟失败: {error_info.message}"
            else:
                error_message = f"批量模拟失败: {str(e)}"
            
            # 发布失败事件
            self._publish_event("batch_simulation_failed", {
                "total_coalitions": len(coalitions),
                "error": str(e),
                "execution_time": execution_time
            })
            
            self.logger.error(error_message)
            
            # 返回失败结果
            return SimulationResult(
                success=False,
                execution_time=execution_time,
                error=error_message,
                error_context={"original_error": str(e)}
            )
    
    def _run_phased_simulation(
        self,
        coalitions: Set[FrozenSet[str]],
        agents: Dict[str, Any],
        config: Dict[str, Any],
        max_concurrent: int
    ) -> Dict[str, Any]:
        """
        分阶段运行模拟：完整联盟详细日志 + 子集联盟简洁日志
        
        Args:
            coalitions: 联盟集合
            agents: 智能体配置
            config: 模拟配置
            max_concurrent: 最大并发数
            
        Returns:
            Dict[str, Any]: 模拟结果
        """
        self.logger.info("🚀 开始分阶段交易模拟...")
        
        # 找到完整联盟（包含所有智能体的联盟）
        all_agents = set()
        for coalition in coalitions:
            all_agents.update(coalition)
        
        full_coalition = frozenset(all_agents)
        subset_coalitions = {c for c in coalitions if c != full_coalition}
        
        self.logger.info(f"📊 模拟计划:")
        self.logger.info(f"  - 完整联盟: {set(full_coalition)} (详细日志)")
        self.logger.info(f"  - 子集联盟: {len(subset_coalitions)} 个 (简洁日志)")
        
        coalition_values = {}
        coalition_daily_returns = {}
        simulation_stats = {
            "total_coalitions": len(coalitions),
            "successful_simulations": 0,
            "failed_simulations": 0,
            "total_simulation_time": 0.0,
            "phased_execution": True,
            "full_coalition_detailed": True,
            "subset_coalitions_simplified": True
        }
        
        start_time = time.time()
        
        # 阶段1: 运行完整联盟（详细日志）
        self.logger.info("=" * 70)
        self.logger.info("🔍 阶段1: 完整联盟详细分析")
        self.logger.info("=" * 70)
        
        if full_coalition in coalitions:
            try:
                self.logger.info("📝 启用详细日志模式 - 将显示完整的LLM输入输出")
                
                # 设置详细日志模式
                self._set_detailed_logging_mode(True)
                
                # 模拟完整联盟
                result = self.simulate_coalition(full_coalition, agents, config)
                
                # 处理结果
                coalition_values[full_coalition] = result["sharpe_ratio"]
                coalition_daily_returns[full_coalition] = result["daily_returns"]
                simulation_stats["successful_simulations"] += 1
                
                self.logger.info(f"✅ 完整联盟模拟完成: 夏普比率 = {result['sharpe_ratio']:.4f}")
                
            except Exception as e:
                self.logger.error(f"❌ 完整联盟模拟失败: {e}")
                coalition_values[full_coalition] = 0.0
                coalition_daily_returns[full_coalition] = []
                simulation_stats["failed_simulations"] += 1
            finally:
                # 恢复简洁日志模式
                self._set_detailed_logging_mode(False)
        else:
            self.logger.warning("⚠️ 完整联盟不在模拟列表中，跳过阶段1")
        
        # 阶段2: 运行子集联盟（简洁日志）
        self.logger.info("=" * 70)
        self.logger.info("⚡ 阶段2: 子集联盟快速模拟")
        self.logger.info("=" * 70)
        self.logger.info("📝 启用简洁日志模式 - 只显示联盟组合和结果")
        
        if subset_coalitions:
            # 检查是否启用并发执行
            if len(subset_coalitions) > 5:
                self.logger.info(f"🚀 启用并发执行: {len(subset_coalitions)} 个子集联盟")
                
                # 并发执行子集联盟
                subset_results = self._run_concurrent_simulation(
                    subset_coalitions,
                    agents,
                    config,
                    max_concurrent
                )
                
                # 合并结果
                coalition_values.update(subset_results["coalition_values"])
                coalition_daily_returns.update(subset_results["coalition_daily_returns"])
                simulation_stats["successful_simulations"] += subset_results["successful_simulations"]
                simulation_stats["failed_simulations"] += subset_results["failed_simulations"]
                
            else:
                # 串行执行
                self.logger.info(f"📝 使用串行执行: {len(subset_coalitions)} 个子集联盟")
                
                for i, coalition in enumerate(subset_coalitions):
                    coalition_set = set(coalition)
                    self.logger.info(f"运行子集 {i+1}/{len(subset_coalitions)}: {coalition_set}")
                    
                    try:
                        result = self.simulate_coalition(coalition, agents, config)
                        coalition_values[coalition] = result["sharpe_ratio"]
                        coalition_daily_returns[coalition] = result["daily_returns"]
                        simulation_stats["successful_simulations"] += 1
                        
                    except Exception as e:
                        self.logger.error(f"子集 {coalition_set} 模拟失败: {e}")
                        coalition_values[coalition] = 0.0
                        coalition_daily_returns[coalition] = []
                        simulation_stats["failed_simulations"] += 1
        else:
            self.logger.warning("⚠️ 没有子集联盟需要模拟")
        
        # 获取模拟器统计信息
        simulator_stats = {}
        if self.trading_simulator:
            simulator_stats = self.trading_simulator.get_stats()
        
        simulation_stats["total_simulation_time"] = time.time() - start_time
        
        self.logger.info("=" * 70)
        self.logger.info(f"✅ 分阶段交易模拟完成:")
        self.logger.info(f"  - 成功: {simulation_stats['successful_simulations']} 个")
        self.logger.info(f"  - 失败: {simulation_stats['failed_simulations']} 个")
        self.logger.info(f"  - 总耗时: {simulation_stats['total_simulation_time']:.2f}s")
        self.logger.info("=" * 70)
        
        return {
            "coalition_values": coalition_values,
            "coalition_daily_returns": coalition_daily_returns,
            "active_agents": agents,  # 包含运行时智能体实例
            "simulation_stats": simulation_stats,
            "simulator_stats": simulator_stats
        }
    
    def _run_concurrent_simulation(
        self,
        coalitions: Set[FrozenSet[str]],
        agents: Dict[str, Any],
        config: Dict[str, Any],
        max_concurrent: int
    ) -> Dict[str, Any]:
        """
        并发运行多个联盟的模拟
        
        Args:
            coalitions: 联盟集合
            agents: 智能体配置
            config: 模拟配置
            max_concurrent: 最大并发数
            
        Returns:
            Dict[str, Any]: 并发模拟结果
        """
        start_time = time.time()
        coalition_values = {}
        coalition_daily_returns = {}
        successful_simulations = 0
        failed_simulations = 0
        
        # 使用线程锁保护共享资源
        results_lock = threading.Lock()
        
        def simulate_single_coalition(coalition: FrozenSet[str]) -> Dict[str, Any]:
            """单个联盟的模拟任务"""
            try:
                result = self.simulate_coalition(coalition, agents, config)
                
                # 线程安全地更新结果
                with results_lock:
                    coalition_values[coalition] = result["sharpe_ratio"]
                    coalition_daily_returns[coalition] = result["daily_returns"]
                
                return {"success": True, "coalition": coalition, "sharpe_ratio": result["sharpe_ratio"]}
                
            except Exception as e:
                # 线程安全地更新错误结果
                with results_lock:
                    coalition_values[coalition] = 0.0
                    coalition_daily_returns[coalition] = []
                
                return {"success": False, "coalition": coalition, "error": str(e)}
        
        # 执行并发模拟
        completed_tasks = 0
        total_tasks = len(coalitions)
        
        self.logger.info(f"🚀 开始并发执行 {total_tasks} 个联盟...")
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_concurrent) as executor:
            # 提交所有任务
            future_to_coalition = {
                executor.submit(simulate_single_coalition, coalition): coalition
                for coalition in coalitions
            }
            
            # 处理完成的任务
            for future in concurrent.futures.as_completed(future_to_coalition):
                completed_tasks += 1
                coalition = future_to_coalition[future]
                
                try:
                    result = future.result()
                    if result["success"]:
                        successful_simulations += 1
                    else:
                        failed_simulations += 1
                        
                    # 每完成10个任务或全部完成时显示进度
                    if completed_tasks % 10 == 0 or completed_tasks == total_tasks:
                        self.logger.info(f"📊 并发模拟进度: {completed_tasks}/{total_tasks} ({completed_tasks/total_tasks*100:.1f}%)")
                        
                except Exception as e:
                    self.logger.error(f"联盟任务执行异常 {set(coalition)}: {e}")
                    failed_simulations += 1
        
        total_time = time.time() - start_time
        
        self.logger.info(f"✅ 并发模拟完成: 成功 {successful_simulations} 个，失败 {failed_simulations} 个，总耗时 {total_time:.2f}s")
        
        return {
            "coalition_values": coalition_values,
            "coalition_daily_returns": coalition_daily_returns,
            "active_agents": agents,  # 包含运行时智能体实例
            "successful_simulations": successful_simulations,
            "failed_simulations": failed_simulations,
            "concurrent_execution_time": total_time
        }
    
    def calculate_performance_metrics(
        self, 
        daily_returns: List[float],
        benchmark_returns: Optional[List[float]] = None
    ) -> Dict[str, float]:
        """
        计算性能指标
        
        Args:
            daily_returns: 日收益率列表
            benchmark_returns: 基准收益率（可选）
            
        Returns:
            Dict[str, float]: 性能指标字典（包含Sharpe比率、最大回撤等）
        """
        try:
            if not daily_returns:
                return {
                    "sharpe_ratio": 0.0,
                    "max_drawdown": 0.0,
                    "volatility": 0.0,
                    "total_return": 0.0,
                    "average_return": 0.0,
                    "win_rate": 0.0
                }
            
            returns_array = np.array(daily_returns)
            
            # 基本统计
            total_return = np.prod(1 + returns_array) - 1
            average_return = np.mean(returns_array)
            volatility = np.std(returns_array)
            
            # 夏普比率
            risk_free_rate = self._config.get("risk_free_rate", 0.02)
            daily_risk_free = risk_free_rate / 252  # 年化转日化
            excess_returns = returns_array - daily_risk_free
            sharpe_ratio = np.mean(excess_returns) / np.std(excess_returns) if np.std(excess_returns) > 0 else 0.0
            
            # 最大回撤
            cumulative_returns = np.cumprod(1 + returns_array)
            rolling_max = np.maximum.accumulate(cumulative_returns)
            drawdowns = (cumulative_returns - rolling_max) / rolling_max
            max_drawdown = np.min(drawdowns)
            
            # 胜率
            win_rate = np.sum(returns_array > 0) / len(returns_array)
            
            metrics = {
                "sharpe_ratio": float(sharpe_ratio),
                "max_drawdown": float(max_drawdown),
                "volatility": float(volatility),
                "total_return": float(total_return),
                "average_return": float(average_return),
                "win_rate": float(win_rate)
            }
            
            # 如果有基准收益率，计算相对指标
            if benchmark_returns and len(benchmark_returns) == len(daily_returns):
                benchmark_array = np.array(benchmark_returns)
                excess_returns_vs_benchmark = returns_array - benchmark_array
                
                metrics.update({
                    "alpha": float(np.mean(excess_returns_vs_benchmark)),
                    "beta": float(np.cov(returns_array, benchmark_array)[0, 1] / np.var(benchmark_array)),
                    "information_ratio": float(np.mean(excess_returns_vs_benchmark) / np.std(excess_returns_vs_benchmark))
                        if np.std(excess_returns_vs_benchmark) > 0 else 0.0
                })
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"计算性能指标失败: {e}")
            return {
                "sharpe_ratio": 0.0,
                "max_drawdown": 0.0,
                "volatility": 0.0,
                "total_return": 0.0,
                "average_return": 0.0,
                "win_rate": 0.0,
                "error": str(e)
            }
    
    def validate_simulation_config(self, config: Dict[str, Any]) -> bool:
        """
        验证模拟配置的有效性
        
        Args:
            config: 模拟配置
            
        Returns:
            bool: 配置是否有效
            
        Raises:
            ConfigurationError: 配置无效时抛出
        """
        try:
            # 必需配置项检查
            required_fields = ["start_date", "end_date", "stocks"]
            for field in required_fields:
                if field not in config:
                    raise ConfigurationError(f"缺少必需配置项: {field}")
            
            # 日期格式检查
            try:
                from datetime import datetime
                datetime.fromisoformat(config["start_date"])
                datetime.fromisoformat(config["end_date"])
            except ValueError:
                raise ConfigurationError("日期格式不正确，应为ISO格式")
            
            # 数值范围检查
            if config.get("starting_cash", 0) <= 0:
                raise ConfigurationError("起始资金必须大于0")
            
            if config.get("risk_free_rate", 0) < 0:
                raise ConfigurationError("无风险收益率不能为负")
            
            # 股票列表检查
            if not isinstance(config["stocks"], list) or not config["stocks"]:
                raise ConfigurationError("股票列表不能为空")
            
            # 并发配置检查
            max_concurrent = config.get("max_concurrent_simulations", 10)
            if not isinstance(max_concurrent, int) or max_concurrent <= 0:
                raise ConfigurationError("最大并发数必须是正整数")
            
            return True
            
        except ConfigurationError:
            raise
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            raise ConfigurationError(f"配置验证失败: {e}")
    
    def get_simulation_status(self, simulation_id: str) -> Dict[str, Any]:
        """
        获取模拟执行状态
        
        Args:
            simulation_id: 模拟任务ID
            
        Returns:
            Dict[str, Any]: 状态信息
        """
        with self._simulation_lock:
            if simulation_id in self._active_simulations:
                simulation = self._active_simulations[simulation_id]
                return {
                    "simulation_id": simulation_id,
                    "status": simulation["status"],
                    "progress": simulation["progress"],
                    "coalition": list(simulation["coalition"]),
                    "start_time": simulation["start_time"],
                    "elapsed_time": time.time() - simulation["start_time"],
                    "active": True
                }
            else:
                # 检查历史记录
                with self._history_lock:
                    for record in self._simulation_history:
                        if record.get("simulation_id") == simulation_id:
                            return {
                                "simulation_id": simulation_id,
                                "status": "completed" if record.get("success") else "failed",
                                "progress": 1.0,
                                "coalition": list(record.get("coalition", [])),
                                "execution_time": record.get("execution_time", 0.0),
                                "active": False,
                                "completed": True
                            }
                
                return {
                    "simulation_id": simulation_id,
                    "status": "not_found",
                    "active": False,
                    "error": "模拟任务不存在"
                }
    
    def cancel_simulation(self, simulation_id: str) -> bool:
        """
        取消正在执行的模拟
        
        Args:
            simulation_id: 模拟任务ID
            
        Returns:
            bool: 是否成功取消
        """
        with self._simulation_lock:
            if simulation_id in self._active_simulations:
                # 标记为取消状态
                self._active_simulations[simulation_id]["status"] = "cancelled"
                
                # 发布取消事件
                self._publish_event("simulation_cancelled", {
                    "simulation_id": simulation_id,
                    "coalition": list(self._active_simulations[simulation_id]["coalition"])
                })
                
                self.logger.info(f"模拟任务已取消: {simulation_id}")
                return True
            else:
                self.logger.warning(f"尝试取消不存在的模拟任务: {simulation_id}")
                return False
    
    def get_simulation_history(
        self, 
        coalition: Optional[FrozenSet[str]] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        获取模拟历史记录
        
        Args:
            coalition: 特定联盟（可选）
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 历史记录列表
        """
        with self._history_lock:
            # 过滤记录
            filtered_history = []
            for record in self._simulation_history:
                if coalition is None or record.get("coalition") == coalition:
                    filtered_history.append(record.copy())
            
            # 按时间戳排序（最新的在前）
            filtered_history.sort(key=lambda x: x.get("timestamp", ""), reverse=True)
            
            # 限制返回数量
            return filtered_history[:limit]
    
    def _merge_configs(self, user_config: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """合并用户配置和默认配置"""
        merged_config = self._config.copy()
        if user_config:
            merged_config.update(user_config)
        return merged_config
    
    def _set_detailed_logging_mode(self, detailed: bool) -> None:
        """设置详细日志模式"""
        if self.trading_simulator and hasattr(self.trading_simulator, 'set_detailed_logging'):
            self.trading_simulator.set_detailed_logging(detailed)
    
    def _update_simulation_progress(self, simulation_id: str, progress: float) -> None:
        """更新模拟进度"""
        with self._simulation_lock:
            if simulation_id in self._active_simulations:
                self._active_simulations[simulation_id]["progress"] = progress
    
    def _add_to_history(self, simulation_id: str, coalition: FrozenSet[str], result: Dict[str, Any]) -> None:
        """添加到历史记录"""
        with self._history_lock:
            history_record = {
                "simulation_id": simulation_id,
                "coalition": coalition,
                "timestamp": datetime.now().isoformat(),
                **result
            }
            self._simulation_history.append(history_record)
            
            # 限制历史记录数量
            max_history = self._config.get("max_history_records", 1000)
            if len(self._simulation_history) > max_history:
                self._simulation_history = self._simulation_history[-max_history:]
    
    def _publish_event(self, event_type: str, data: Dict[str, Any]) -> None:
        """发布事件"""
        if self.event_bus:
            try:
                self.event_bus.publish(
                    event_type=event_type,
                    data=data,
                    source="SimulationService"
                )
            except Exception as e:
                self.logger.warning(f"发布事件失败: {event_type}, 错误: {e}")
    
    def _update_stats(self, execution_time: float, success: bool) -> None:
        """更新服务统计信息"""
        self._stats["total_simulations"] += 1
        self._stats["total_execution_time"] += execution_time
        self._stats["last_simulation_time"] = datetime.now().isoformat()
        
        if success:
            self._stats["successful_simulations"] += 1
        else:
            self._stats["failed_simulations"] += 1
        
        # 计算平均执行时间
        if self._stats["total_simulations"] > 0:
            self._stats["average_execution_time"] = (
                self._stats["total_execution_time"] / self._stats["total_simulations"]
            )
        
        # 更新性能指标
        perf_metrics = self._stats["performance_metrics"]
        if execution_time < perf_metrics["min_execution_time"]:
            perf_metrics["min_execution_time"] = execution_time
        if execution_time > perf_metrics["max_execution_time"]:
            perf_metrics["max_execution_time"] = execution_time
        
        # 更新联盟计数
        if success:
            perf_metrics["total_coalitions_simulated"] += 1
    
    def _load_service_config(self) -> Dict[str, Any]:
        """从配置管理器加载服务配置"""
        default_config = {
            "start_date": "2023-01-01",
            "end_date": "2023-01-31",
            "stocks": ["AAPL"],
            "starting_cash": 1000000,
            "risk_free_rate": 0.02,
            "simulation_days": None,
            "max_concurrent_simulations": 60,
            "timeout_seconds": 300,
            "performance_monitoring": True,
            "event_publishing": True,
            "max_history_records": 1000,
            "enable_detailed_logging": False,
            "simulation_timeout": 600
        }
        
        if self.config_manager:
            try:
                # 尝试从配置管理器获取模拟服务配置
                service_config = {}

                # 加载各项配置 - 支持两种键名格式
                config_mappings = [
                    ("start_date", ["start_date", "simulation.start_date"]),
                    ("end_date", ["end_date", "simulation.end_date"]),
                    ("stocks", ["stocks", "simulation.stocks"]),
                    ("starting_cash", ["starting_cash", "simulation.starting_cash"]),
                    ("risk_free_rate", ["risk_free_rate", "simulation.risk_free_rate"]),
                    ("simulation_days", ["simulation_days", "simulation.simulation_days"]),
                    ("max_concurrent_simulations", ["max_concurrent_simulations", "simulation.max_concurrent_simulations"]),
                    ("timeout_seconds", ["timeout_seconds", "simulation.timeout_seconds"]),
                    ("performance_monitoring", ["performance_monitoring", "simulation.performance_monitoring"]),
                    ("event_publishing", ["event_publishing", "simulation.event_publishing"]),
                    ("max_history_records", ["max_history_records", "simulation.max_history_records"]),
                    ("enable_detailed_logging", ["enable_detailed_logging", "simulation.enable_detailed_logging"]),
                    ("simulation_timeout", ["simulation_timeout", "simulation.simulation_timeout"])
                ]

                for config_name, possible_keys in config_mappings:
                    value = None
                    for key in possible_keys:
                        value = self.config_manager.get_config(key)
                        if value is not None:
                            break
                    if value is not None:
                        service_config[config_name] = value

                # 合并默认配置和用户配置
                default_config.update(service_config)

                self.logger.debug(f"已加载服务配置: {service_config}")

            except Exception as e:
                self.logger.warning(f"加载服务配置失败，使用默认配置: {e}")
        
        return default_config
    
    def _register_error_recovery_strategies(self) -> None:
        """注册错误恢复策略"""
        if not self.error_handler:
            return
        
        try:
            # 注册模拟错误的恢复策略
            def simulation_error_recovery(error: Exception, context: ErrorContext) -> Any:
                """模拟错误恢复策略"""
                self.logger.info("尝试模拟错误恢复...")
                
                # 如果是超时错误，建议减少模拟复杂度
                if "timeout" in str(error).lower():
                    return {"recovery_action": "reduce_complexity", "success": True}
                
                # 如果是配置错误，使用默认配置
                if "configuration" in str(error).lower():
                    return {"recovery_action": "use_default_config", "success": True}
                
                return None
            
            # 注册超时错误的恢复策略
            def timeout_error_recovery(error: Exception, context: ErrorContext) -> Any:
                """超时错误恢复策略"""
                self.logger.info("尝试超时错误恢复...")
                return {"recovery_action": "extend_timeout", "success": True}
            
            self.error_handler.register_recovery_strategy(
                SimulationError, simulation_error_recovery
            )
            self.error_handler.register_recovery_strategy(
                SimulationTimeoutError, timeout_error_recovery
            )
            
            self.logger.debug("错误恢复策略注册完成")
            
        except Exception as e:
            self.logger.warning(f"注册错误恢复策略失败: {e}")
    
    def _subscribe_to_events(self) -> None:
        """订阅相关事件"""
        if not self.event_bus:
            return
        
        try:
            # 订阅系统配置更新事件
            def on_config_updated(event):
                """配置更新事件处理器"""
                self.logger.info("收到配置更新事件，重新加载服务配置")
                self._config = self._load_service_config()
            
            # 订阅性能监控事件
            def on_performance_alert(event):
                """性能告警事件处理器"""
                alert_data = event.data
                self.logger.warning(f"收到性能告警: {alert_data}")
            
            self.event_bus.subscribe("config_updated", on_config_updated)
            self.event_bus.subscribe("performance_alert", on_performance_alert)
            
            self.logger.debug("事件订阅完成")
            
        except Exception as e:
            self.logger.warning(f"事件订阅失败: {e}")
    
    def get_service_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        return self._stats.copy()
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取服务健康状态"""
        total_simulations = self._stats["total_simulations"]
        success_rate = 0.0
        
        if total_simulations > 0:
            success_rate = (self._stats["successful_simulations"] / total_simulations) * 100
        
        # 判断服务健康状态
        health_status = "healthy"
        if success_rate < 50:
            health_status = "critical"
        elif success_rate < 80:
            health_status = "warning"
        
        return {
            "status": health_status,
            "success_rate": success_rate,
            "total_simulations": total_simulations,
            "average_execution_time": self._stats["average_execution_time"],
            "last_simulation": self._stats["last_simulation_time"],
            "active_simulations": len(self._active_simulations),
            "performance_metrics": self._stats["performance_metrics"],
            "concurrent_stats": self._stats["concurrent_stats"],
            "configuration": {
                "max_concurrent_simulations": self._config.get("max_concurrent_simulations", 10),
                "timeout_seconds": self._config.get("timeout_seconds", 300),
                "performance_monitoring": self._config.get("performance_monitoring", True)
            }
        }
    
    def reset_stats(self) -> None:
        """重置服务统计信息"""
        self._stats = {
            "total_simulations": 0,
            "successful_simulations": 0,
            "failed_simulations": 0,
            "total_execution_time": 0.0,
            "average_execution_time": 0.0,
            "last_simulation_time": None,
            "concurrent_stats": {
                "total_concurrent_tasks": 0,
                "successful_concurrent_tasks": 0,
                "failed_concurrent_tasks": 0,
                "concurrent_execution_time": 0.0
            },
            "performance_metrics": {
                "min_execution_time": float('inf'),
                "max_execution_time": 0.0,
                "total_coalitions_simulated": 0,
                "average_performance_score": 0.0
            }
        }
        
        self.logger.info("服务统计信息已重置")
        
        # 发布统计重置事件
        self._publish_event("stats_reset", {
            "timestamp": datetime.now().isoformat(),
            "component": "SimulationService"
        })