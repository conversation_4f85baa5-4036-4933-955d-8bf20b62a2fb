"""
联盟生成服务实现

提供智能体联盟生成和管理的服务实现，包括：
- 联盟生成和剪枝
- 联盟验证和分析
- 性能监控和统计收集
- 与基础设施组件集成
"""

import time
from typing import Dict, Any, List, Set, Optional, FrozenSet
from datetime import datetime
import logging

from ..interfaces.coalition_service import (
    ICoalitionService, 
    CoalitionGenerationError, 
    CoalitionValidationError
)
from ..dto.phase_results_dto import CoalitionResult
from ..coalition_manager import CoalitionManager
from ..infrastructure.event_bus import IEventBus
from ..infrastructure.error_handler import IErrorHandler, ErrorContext
from ..infrastructure.configuration_manager import IConfigurationManager


class CoalitionService(ICoalitionService):
    """
    联盟生成服务实现
    
    负责智能体联盟的生成、验证和管理，集成了事件发布、
    错误处理和配置管理等基础设施组件。
    """
    
    def __init__(self,
                 coalition_manager: Optional[CoalitionManager] = None,
                 event_bus: Optional[IEventBus] = None,
                 error_handler: Optional[IErrorHandler] = None,
                 config_manager: Optional[IConfigurationManager] = None,
                 logger: Optional[logging.Logger] = None):
        """
        初始化联盟服务
        
        Args:
            coalition_manager: 联盟管理器实例
            event_bus: 事件总线实例
            error_handler: 错误处理器实例
            config_manager: 配置管理器实例
            logger: 日志记录器
        """
        self.logger = logger or self._create_default_logger()
        self.coalition_manager = coalition_manager or CoalitionManager(logger=self.logger)
        self.event_bus = event_bus
        self.error_handler = error_handler
        self.config_manager = config_manager
        
        # 服务统计信息
        self._stats = {
            "total_generations": 0,
            "successful_generations": 0,
            "failed_generations": 0,
            "total_execution_time": 0.0,
            "average_execution_time": 0.0,
            "last_generation_time": None,
            "performance_metrics": {
                "min_execution_time": float('inf'),
                "max_execution_time": 0.0,
                "total_coalitions_generated": 0,
                "total_coalitions_pruned": 0
            }
        }
        
        # 从配置管理器加载配置
        self._config = self._load_service_config()
        
        # 注册错误恢复策略
        self._register_error_recovery_strategies()
        
        # 订阅相关事件
        self._subscribe_to_events()
        
        self.logger.info("联盟服务初始化完成")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.CoalitionService")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def generate_coalitions(
        self, 
        agents: Dict[str, Any],
        max_coalitions: Optional[int] = None,
        pruning_enabled: bool = True
    ) -> CoalitionResult:
        """
        生成智能体联盟
        
        Args:
            agents: 智能体配置字典，格式为 {agent_id: agent_config}
            max_coalitions: 最大联盟数量限制
            pruning_enabled: 是否启用联盟剪枝
            
        Returns:
            CoalitionResult: 联盟生成结果
            
        Raises:
            CoalitionGenerationError: 联盟生成失败时抛出
        """
        start_time = time.time()
        
        # 发布联盟生成开始事件
        self._publish_event("coalition_generation_started", {
            "agent_count": len(agents),
            "max_coalitions": max_coalitions,
            "pruning_enabled": pruning_enabled
        })
        
        try:
            # 提取智能体列表和配置
            target_agents = list(agents.keys())
            
            # 从配置中获取分析智能体、展望智能体和交易智能体
            analyst_agents = self._extract_analyst_agents(target_agents)
            outlook_agents = self._extract_outlook_agents(target_agents)
            trader_agent = self._extract_trader_agent(target_agents)
            
            self.logger.info(f"开始联盟生成: 目标智能体={len(target_agents)}, "
                           f"分析智能体={len(analyst_agents)}, 展望智能体={len(outlook_agents)}, "
                           f"交易智能体={trader_agent}")
            
            # 生成和剪枝联盟
            if pruning_enabled:
                valid_coalitions, pruned_coalitions = self.coalition_manager.generate_pruned_coalitions(
                    target_agents, analyst_agents, trader_agent, outlook_agents
                )
            else:
                # 如果不启用剪枝，生成所有可能的联盟
                all_coalitions = self.coalition_manager._generate_all_subsets(target_agents)
                valid_coalitions = {frozenset(coalition) for coalition in all_coalitions}
                pruned_coalitions = set()
            
            # # 限制联盟数量（如果指定）
            # if max_coalitions and len(valid_coalitions) > max_coalitions:
            #     self.logger.info(f"限制联盟数量: {len(valid_coalitions)} -> {max_coalitions}")
            #     valid_coalitions = self._select_coalitions_intelligently(
            #         list(valid_coalitions), max_coalitions
            #     )
            
            # 分析联盟结构
            coalition_analysis = self.coalition_manager.analyze_coalition_structure(
                valid_coalitions, target_agents
            )
            
            # 获取生成统计
            generation_stats = self.coalition_manager.get_stats()
            
            execution_time = time.time() - start_time
            
            # 创建结果对象
            result = CoalitionResult(
                success=True,
                execution_time=execution_time,
                valid_coalitions=valid_coalitions,
                pruned_coalitions=pruned_coalitions,
                coalition_analysis=coalition_analysis,
                generation_stats=generation_stats
            )
            
            # 更新服务统计
            self._update_stats(execution_time, success=True)
            
            # 监控性能
            self._monitor_performance(execution_time, "generate_coalitions")
            
            # 更新性能指标统计
            self._stats["performance_metrics"]["total_coalitions_generated"] += len(valid_coalitions)
            self._stats["performance_metrics"]["total_coalitions_pruned"] += len(pruned_coalitions)
            
            # 发布成功事件
            self._publish_event("coalition_generation_completed", {
                "valid_coalitions_count": len(valid_coalitions),
                "pruned_coalitions_count": len(pruned_coalitions),
                "execution_time": execution_time
            })
            
            self.logger.info(f"联盟生成完成: 有效联盟={len(valid_coalitions)}, "
                           f"剪枝联盟={len(pruned_coalitions)}, 耗时={execution_time:.3f}s")
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            
            # 处理错误
            error_context = ErrorContext(
                operation="generate_coalitions",
                component="CoalitionService",
                additional_data={
                    "agent_count": len(agents),
                    "max_coalitions": max_coalitions,
                    "pruning_enabled": pruning_enabled
                }
            )
            
            if self.error_handler:
                error_info = self.error_handler.handle_error(e, error_context)
                error_message = f"联盟生成失败: {error_info.message}"
            else:
                error_message = f"联盟生成失败: {str(e)}"
            
            # 更新统计
            self._update_stats(execution_time, success=False)
            
            # 发布失败事件
            self._publish_event("coalition_generation_failed", {
                "error": str(e),
                "execution_time": execution_time
            })
            
            self.logger.error(error_message)
            
            # 返回失败结果
            return CoalitionResult(
                success=False,
                execution_time=execution_time,
                error=error_message,
                error_context={"original_error": str(e)}
            )
    
    def validate_coalition(self, coalition: FrozenSet[str]) -> bool:
        """
        验证联盟的有效性
        
        Args:
            coalition: 要验证的联盟
            
        Returns:
            bool: 联盟是否有效
        """
        try:
            # 使用CoalitionManager的验证方法
            is_valid, violations = self.coalition_manager.validate_coalition_constraints(coalition)
            
            if not is_valid:
                self.logger.debug(f"联盟验证失败: {coalition}, 违规: {violations}")
            
            return is_valid
            
        except Exception as e:
            if self.error_handler:
                error_context = ErrorContext(
                    operation="validate_coalition",
                    component="CoalitionService",
                    additional_data={"coalition": list(coalition)}
                )
                self.error_handler.handle_error(e, error_context)
            
            self.logger.error(f"联盟验证出错: {e}")
            return False
    
    def get_coalition_analysis(self, coalitions: Set[FrozenSet[str]]) -> Dict[str, Any]:
        """
        分析联盟集合的统计信息
        
        Args:
            coalitions: 联盟集合
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        try:
            # 提取所有涉及的智能体
            all_agents = set()
            for coalition in coalitions:
                all_agents.update(coalition)
            
            # 使用CoalitionManager进行分析
            analysis = self.coalition_manager.analyze_coalition_structure(
                coalitions, list(all_agents)
            )
            
            # 添加额外的分析信息
            analysis.update({
                "analysis_timestamp": datetime.now().isoformat(),
                "total_agents_involved": len(all_agents),
                "agents_list": list(all_agents)
            })
            
            return analysis
            
        except Exception as e:
            if self.error_handler:
                error_context = ErrorContext(
                    operation="get_coalition_analysis",
                    component="CoalitionService",
                    additional_data={"coalitions_count": len(coalitions)}
                )
                self.error_handler.handle_error(e, error_context)
            
            self.logger.error(f"联盟分析出错: {e}")
            return {"error": str(e), "analysis_failed": True}
    
    def prune_coalitions(
        self, 
        coalitions: Set[FrozenSet[str]], 
        criteria: Optional[Dict[str, Any]] = None
    ) -> Set[FrozenSet[str]]:
        """
        根据指定条件剪枝联盟
        
        Args:
            coalitions: 原始联盟集合
            criteria: 剪枝条件
            
        Returns:
            Set[FrozenSet[str]]: 剪枝后的联盟集合
        """
        try:
            if not criteria:
                return coalitions
            
            pruned_coalitions = set()
            
            for coalition in coalitions:
                # 应用剪枝条件
                if self._should_keep_coalition(coalition, criteria):
                    pruned_coalitions.add(coalition)
            
            self.logger.info(f"联盟剪枝完成: {len(coalitions)} -> {len(pruned_coalitions)}")
            return pruned_coalitions
            
        except Exception as e:
            if self.error_handler:
                error_context = ErrorContext(
                    operation="prune_coalitions",
                    component="CoalitionService",
                    additional_data={
                        "original_count": len(coalitions),
                        "criteria": criteria
                    }
                )
                self.error_handler.handle_error(e, error_context)
            
            self.logger.error(f"联盟剪枝出错: {e}")
            return coalitions  # 返回原始集合
    
    def get_coalition_dependencies(self, coalition: FrozenSet[str]) -> Dict[str, List[str]]:
        """
        获取联盟内智能体的依赖关系
        
        Args:
            coalition: 联盟
            
        Returns:
            Dict[str, List[str]]: 依赖关系映射
        """
        try:
            dependencies = {}
            agent_graph = self.coalition_manager.default_agent_graph
            
            for agent in coalition:
                if agent in agent_graph:
                    # 只返回联盟内的依赖关系
                    agent_deps = [dep for dep in agent_graph[agent] if dep in coalition]
                    dependencies[agent] = agent_deps
                else:
                    dependencies[agent] = []
            
            return dependencies
            
        except Exception as e:
            if self.error_handler:
                error_context = ErrorContext(
                    operation="get_coalition_dependencies",
                    component="CoalitionService",
                    additional_data={"coalition": list(coalition)}
                )
                self.error_handler.handle_error(e, error_context)
            
            self.logger.error(f"获取联盟依赖关系出错: {e}")
            return {}
    
    def estimate_coalition_complexity(self, coalition: FrozenSet[str]) -> float:
        """
        估算联盟的复杂度
        
        Args:
            coalition: 联盟
            
        Returns:
            float: 复杂度评分
        """
        try:
            # 基于联盟大小和依赖关系计算复杂度
            size_factor = len(coalition)
            
            # 计算依赖关系复杂度
            dependencies = self.get_coalition_dependencies(coalition)
            dependency_factor = sum(len(deps) for deps in dependencies.values())
            
            # 简单的复杂度计算公式
            complexity = size_factor * 1.0 + dependency_factor * 0.5
            
            return complexity
            
        except Exception as e:
            if self.error_handler:
                error_context = ErrorContext(
                    operation="estimate_coalition_complexity",
                    component="CoalitionService",
                    additional_data={"coalition": list(coalition)}
                )
                self.error_handler.handle_error(e, error_context)
            
            self.logger.error(f"估算联盟复杂度出错: {e}")
            return 0.0
    
    def get_service_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        return self._stats.copy()
    
    def _extract_analyst_agents(self, target_agents: List[str]) -> List[str]:
        """从目标智能体中提取分析智能体"""
        # 默认的核心分析智能体
        core_analyst_agents = ["TAA", "NAA", "FAA"]
        return [agent for agent in target_agents if agent in core_analyst_agents]
    
    def _extract_outlook_agents(self, target_agents: List[str]) -> List[str]:
        """从目标智能体中提取展望智能体"""
        # 默认的核心展望智能体
        core_outlook_agents = ["BOA", "BeOA", "NOA"]
        return [agent for agent in target_agents if agent in core_outlook_agents]
    
    def _extract_trader_agent(self, target_agents: List[str]) -> str:
        """从目标智能体中提取交易智能体"""
        # 默认的交易智能体
        trader_agent = "TRA"
        if trader_agent not in target_agents:
            raise CoalitionGenerationError(f"交易智能体 {trader_agent} 不在目标智能体列表中")
        return trader_agent
    
    def _select_coalitions_intelligently(self, coalitions: List[FrozenSet[str]], 
                                       max_count: int) -> Set[FrozenSet[str]]:
        """智能选择联盟，确保包含完整联盟和足够的子集联盟"""
        if len(coalitions) <= max_count:
            return set(coalitions)
        
        selected = set()
        
        # 按大小排序，优先选择较大的联盟
        coalitions_by_size = sorted(coalitions, key=len, reverse=True)
        
        # 确保包含完整联盟（最大的联盟）
        if coalitions_by_size:
            selected.add(coalitions_by_size[0])
        
        # 添加其他联盟直到达到限制
        for coalition in coalitions_by_size[1:]:
            if len(selected) >= max_count:
                break
            selected.add(coalition)
        
        return selected
    
    def _should_keep_coalition(self, coalition: FrozenSet[str], 
                             criteria: Dict[str, Any]) -> bool:
        """判断是否应该保留联盟"""
        # 大小过滤
        if "min_size" in criteria and len(coalition) < criteria["min_size"]:
            return False
        if "max_size" in criteria and len(coalition) > criteria["max_size"]:
            return False
        
        # 必须包含的智能体
        if "required_agents" in criteria:
            required = set(criteria["required_agents"])
            if not required.issubset(coalition):
                return False
        
        # 不能包含的智能体
        if "excluded_agents" in criteria:
            excluded = set(criteria["excluded_agents"])
            if excluded.intersection(coalition):
                return False
        
        return True
    
    def _publish_event(self, event_type: str, data: Dict[str, Any]) -> None:
        """发布事件"""
        if self.event_bus:
            try:
                self.event_bus.publish(
                    event_type=event_type,
                    data=data,
                    source="CoalitionService"
                )
            except Exception as e:
                self.logger.warning(f"发布事件失败: {event_type}, 错误: {e}")
    
    def _update_stats(self, execution_time: float, success: bool) -> None:
        """更新服务统计信息"""
        self._stats["total_generations"] += 1
        self._stats["total_execution_time"] += execution_time
        self._stats["last_generation_time"] = datetime.now().isoformat()
        
        if success:
            self._stats["successful_generations"] += 1
        else:
            self._stats["failed_generations"] += 1
        
        # 计算平均执行时间
        if self._stats["total_generations"] > 0:
            self._stats["average_execution_time"] = (
                self._stats["total_execution_time"] / self._stats["total_generations"]
            )
        
        # 更新性能指标
        perf_metrics = self._stats["performance_metrics"]
        if execution_time < perf_metrics["min_execution_time"]:
            perf_metrics["min_execution_time"] = execution_time
        if execution_time > perf_metrics["max_execution_time"]:
            perf_metrics["max_execution_time"] = execution_time
    
    def _load_service_config(self) -> Dict[str, Any]:
        """从配置管理器加载服务配置"""
        default_config = {
            "max_coalitions": None,
            "pruning_enabled": True,
            "enable_validation": True,
            "enable_analysis": True,
            "timeout_seconds": 300,
            "performance_monitoring": True,
            "event_publishing": True,
            "analyst_agents": ["TAA", "NAA", "FAA"],
            "trader_agent": "TRA"
        }
        
        if self.config_manager:
            try:
                # 尝试从配置管理器获取联盟服务配置
                service_config = {}
                
                # 加载各项配置
                config_keys = [
                    "coalition.max_coalitions",
                    "coalition.pruning_enabled", 
                    "coalition.enable_validation",
                    "coalition.enable_analysis",
                    "coalition.timeout_seconds",
                    "coalition.performance_monitoring",
                    "coalition.event_publishing"
                ]
                
                for key in config_keys:
                    config_name = key.split('.')[-1]
                    value = self.config_manager.get_config(key)
                    if value is not None:
                        service_config[config_name] = value
                
                # 合并默认配置和用户配置
                default_config.update(service_config)
                
                self.logger.debug(f"已加载服务配置: {service_config}")
                
            except Exception as e:
                self.logger.warning(f"加载服务配置失败，使用默认配置: {e}")
        
        return default_config
    
    def _register_error_recovery_strategies(self) -> None:
        """注册错误恢复策略"""
        if not self.error_handler:
            return
        
        try:
            # 注册联盟生成错误的恢复策略
            def coalition_generation_recovery(error: Exception, context: ErrorContext) -> Any:
                """联盟生成错误恢复策略"""
                self.logger.info("尝试联盟生成错误恢复...")
                
                # 如果是因为智能体配置问题，尝试使用默认配置
                if "不在智能体列表中" in str(error):
                    return {"recovery_action": "use_default_agents", "success": True}
                
                # 如果是超时问题，建议减少联盟数量
                if "timeout" in str(error).lower():
                    return {"recovery_action": "reduce_coalition_count", "success": True}
                
                return None
            
            # 注册验证错误的恢复策略
            def validation_error_recovery(error: Exception, context: ErrorContext) -> Any:
                """联盟验证错误恢复策略"""
                self.logger.info("尝试联盟验证错误恢复...")
                return {"recovery_action": "skip_validation", "success": True}
            
            self.error_handler.register_recovery_strategy(
                CoalitionGenerationError, coalition_generation_recovery
            )
            self.error_handler.register_recovery_strategy(
                CoalitionValidationError, validation_error_recovery
            )
            
            self.logger.debug("错误恢复策略注册完成")
            
        except Exception as e:
            self.logger.warning(f"注册错误恢复策略失败: {e}")
    
    def _subscribe_to_events(self) -> None:
        """订阅相关事件"""
        if not self.event_bus:
            return
        
        try:
            # 订阅系统配置更新事件
            def on_config_updated(event):
                """配置更新事件处理器"""
                self.logger.info("收到配置更新事件，重新加载服务配置")
                self._config = self._load_service_config()
            
            # 订阅性能监控事件
            def on_performance_alert(event):
                """性能告警事件处理器"""
                alert_data = event.data
                self.logger.warning(f"收到性能告警: {alert_data}")
                
                # 如果执行时间过长，记录警告
                if alert_data.get("type") == "slow_execution":
                    self._stats["performance_alerts"] = self._stats.get("performance_alerts", 0) + 1
            
            self.event_bus.subscribe("config_updated", on_config_updated)
            self.event_bus.subscribe("performance_alert", on_performance_alert)
            
            self.logger.debug("事件订阅完成")
            
        except Exception as e:
            self.logger.warning(f"事件订阅失败: {e}")
    
    def _monitor_performance(self, execution_time: float, operation: str) -> None:
        """监控性能并发布告警事件"""
        if not self._config.get("performance_monitoring", True):
            return
        
        # 检查执行时间是否超过阈值
        timeout_threshold = self._config.get("timeout_seconds", 300)
        slow_threshold = timeout_threshold * 0.8  # 80%的超时时间作为慢执行阈值
        
        if execution_time > slow_threshold:
            self._publish_event("performance_alert", {
                "type": "slow_execution",
                "operation": operation,
                "execution_time": execution_time,
                "threshold": slow_threshold,
                "severity": "high" if execution_time > timeout_threshold else "medium"
            })
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取服务健康状态"""
        total_generations = self._stats["total_generations"]
        success_rate = 0.0
        
        if total_generations > 0:
            success_rate = (self._stats["successful_generations"] / total_generations) * 100
        
        # 判断服务健康状态
        health_status = "healthy"
        if success_rate < 50:
            health_status = "critical"
        elif success_rate < 80:
            health_status = "warning"
        
        return {
            "status": health_status,
            "success_rate": success_rate,
            "total_operations": total_generations,
            "average_execution_time": self._stats["average_execution_time"],
            "last_operation": self._stats["last_generation_time"],
            "performance_metrics": self._stats["performance_metrics"],
            "configuration": {
                "pruning_enabled": self._config.get("pruning_enabled", True),
                "max_coalitions": self._config.get("max_coalitions"),
                "timeout_seconds": self._config.get("timeout_seconds", 300)
            }
        }
    
    def reset_stats(self) -> None:
        """重置服务统计信息"""
        self._stats = {
            "total_generations": 0,
            "successful_generations": 0,
            "failed_generations": 0,
            "total_execution_time": 0.0,
            "average_execution_time": 0.0,
            "last_generation_time": None,
            "performance_metrics": {
                "min_execution_time": float('inf'),
                "max_execution_time": 0.0,
                "total_coalitions_generated": 0,
                "total_coalitions_pruned": 0
            }
        }
        
        self.logger.info("服务统计信息已重置")
        
        # 发布统计重置事件
        self._publish_event("stats_reset", {
            "timestamp": datetime.now().isoformat(),
            "component": "CoalitionService"
        })