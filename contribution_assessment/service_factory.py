"""
服务工厂 - 顶层便利接口

提供简化的服务创建接口，封装底层的基础设施服务工厂。
这个模块作为用户友好的入口点，隐藏复杂的依赖注入细节。
增强版：集成DailyStateManager历史查询服务
"""

import logging
from typing import Dict, Any, Optional, TYPE_CHECKING

# 导入基础设施层的服务工厂
from .infrastructure.service_factory import ServiceFactory as InfraServiceFactory

# 导入每日状态管理器
try:
    from state_management.daily_state_manager import DailyStateManager
except ImportError:
    DailyStateManager = None

# 类型检查时的导入，避免循环导入
if TYPE_CHECKING:
    from .refactored_assessor import RefactoredContributionAssessor

# 导入服务组件
from .services.phase_coordinator import PhaseCoordinator


class ServiceFactory:
    """
    顶层服务工厂
    
    提供简化的接口来创建和配置各种服务。
    封装了底层基础设施的复杂性。
    """
    
    def __init__(self):
        """初始化服务工厂"""
        self._infra_factory = InfraServiceFactory()
        self._configured = False
    
    def configure_services(self, config: Dict[str, Any]):
        """
        配置服务

        Args:
            config: 配置字典
        """
        # 如果基础设施工厂还没有配置管理器，创建一个
        if not self._infra_factory._config_manager:
            from .infrastructure.configuration_manager import ConfigurationManager
            config_manager = ConfigurationManager(config)
            self._infra_factory._config_manager = config_manager

        self._infra_factory.configure_services(config)
        self._configured = True
    
    def create_assessor(self,
                       agents: Optional[Dict[str, Any]] = None,
                       logger: Optional[logging.Logger] = None,
                       llm_provider: Optional[Any] = None,
                       enable_opro: bool = False) -> "RefactoredContributionAssessor":
        """
        创建重构版本的评估器
        
        Args:
            agents: 智能体配置
            logger: 日志记录器
            llm_provider: LLM提供者
            enable_opro: 是否启用OPRO优化
            
        Returns:
            RefactoredContributionAssessor实例
        """
        if not self._configured:
            raise RuntimeError("ServiceFactory must be configured before creating services")
        
        # 获取配置
        config = self._infra_factory.get_config()
        
        # 延迟导入避免循环导入
        from .refactored_assessor import RefactoredContributionAssessor

        # 创建评估器
        return RefactoredContributionAssessor(
            config=config,
            agents=agents,
            logger=logger,
            llm_provider=llm_provider,
            enable_opro=enable_opro
        )
    
    def get_health_status(self) -> Dict[str, Any]:
        """
        获取服务健康状态
        
        Returns:
            健康状态字典
        """
        if not self._configured:
            return {
                "status": "not_configured",
                "message": "ServiceFactory has not been configured"
            }
        
        return self._infra_factory.get_health_status()

    def create_phase_coordinator(self,
                               logger: Optional[logging.Logger] = None,
                               enable_opro: bool = False) -> PhaseCoordinator:
        """
        创建阶段协调器

        Args:
            logger: 日志记录器
            enable_opro: 是否启用OPRO优化

        Returns:
            PhaseCoordinator实例
        """
        if not self._configured:
            raise RuntimeError("ServiceFactory must be configured before creating services")

        # 创建真实服务实例（替代Mock服务）
        from .services.coalition_service import CoalitionService
        from .services.simulation_service import SimulationService
        from .services.shapley_service import ShapleyService
        from .services.simplified_opro_service import SimplifiedOPROService
        from .services.state_manager import StateManager
        from .infrastructure.event_bus import EventBus

        # 实例化真实服务
        from .shapley_calculator import ShapleyCalculator
        from .coalition_manager import CoalitionManager

        # 创建基础设施服务
        event_bus = EventBus()  # EventBus不需要logger参数

        # 创建业务服务的依赖
        coalition_manager = CoalitionManager()
        shapley_calculator = ShapleyCalculator()

        # 创建交易模拟器
        from .trading_simulator import TradingSimulator
        config = self._infra_factory.get_config()
        trading_simulator = TradingSimulator(base_config=config, logger=logger)

        # 创建LLM接口用于OPRO服务
        from .llm_interface import LLMInterface
        llm_provider = config.get("llm_integration", {}).get("primary_provider", "zhipuai")
        llm_interface = LLMInterface(provider=llm_provider, logger=logger)

        # 创建DailyStateManager用于历史数据分析
        daily_state_manager = self.get_daily_state_manager()

        # 创建业务服务
        coalition_service = CoalitionService(
            coalition_manager=coalition_manager,
            event_bus=event_bus,
            logger=logger
        )
        simulation_service = SimulationService(
            trading_simulator=trading_simulator,
            event_bus=event_bus,
            config_manager=self._infra_factory.get_config_manager(),
            logger=logger
        )
        shapley_service = ShapleyService(
            shapley_calculator=shapley_calculator,
            logger=logger
        )
        opro_service = SimplifiedOPROService(
            llm_interface=llm_interface,
            logger=logger,
            enabled=enable_opro,
            daily_state_manager=daily_state_manager
        )
        state_manager = StateManager(
            event_bus=event_bus
        )

        # 创建阶段协调器
        return PhaseCoordinator(
            coalition_service=coalition_service,  # type: ignore
            simulation_service=simulation_service,  # type: ignore
            shapley_service=shapley_service,  # type: ignore
            opro_service=opro_service,  # type: ignore
            state_manager=state_manager,  # type: ignore
            event_bus=event_bus,  # type: ignore
            logger=logger
        )

    def create_phase_coordinator_with_mocks(self,
                                          logger: Optional[logging.Logger] = None,
                                          enable_opro: bool = False) -> PhaseCoordinator:
        """
        创建使用Mock服务的阶段协调器（用于测试）

        Args:
            logger: 日志记录器
            enable_opro: 是否启用OPRO优化

        Returns:
            使用Mock服务的PhaseCoordinator实例
        """
        if not self._configured:
            raise RuntimeError("ServiceFactory must be configured before creating services")

        # 导入Mock服务
        from .services.mock_services import (
            MockCoalitionService, MockSimulationService, MockShapleyService,
            MockOPROService, MockStateManager, MockEventBus
        )

        # 创建Mock服务实例
        coalition_service = MockCoalitionService(logger=logger)  # type: ignore
        simulation_service = MockSimulationService(logger=logger)  # type: ignore
        shapley_service = MockShapleyService(logger=logger)  # type: ignore
        opro_service = MockOPROService(logger=logger, enabled=enable_opro)  # type: ignore
        state_manager = MockStateManager(logger=logger)  # type: ignore
        event_bus = MockEventBus(logger=logger)  # type: ignore

        # 创建阶段协调器
        return PhaseCoordinator(
            coalition_service=coalition_service,  # type: ignore
            simulation_service=simulation_service,  # type: ignore
            shapley_service=shapley_service,  # type: ignore
            opro_service=opro_service,  # type: ignore
            state_manager=state_manager,  # type: ignore
            event_bus=event_bus,  # type: ignore
            logger=logger
        )

    def create_weekly_optimization_service(self,
                                         logger: Optional[logging.Logger] = None,
                                         llm_provider: Optional[Any] = None):
        """
        创建周期性优化服务

        Args:
            logger: 日志记录器
            llm_provider: LLM提供者

        Returns:
            WeeklyOptimizationService实例
        """
        if not self._configured:
            raise RuntimeError("ServiceFactory must be configured before creating services")

        # 导入周期性优化服务
        from .services.weekly_optimization_service import WeeklyOptimizationService
        from .llm_interface import LLMInterface

        # 创建PhaseCoordinator
        phase_coordinator = self.create_phase_coordinator(
            logger=logger,
            enable_opro=True  # 周期性优化需要OPRO功能
        )

        # 创建LLM接口
        llm_interface = None
        if llm_provider:
            llm_interface = LLMInterface(provider=llm_provider, logger=logger)

        # 创建周期性优化服务 - 使用类型忽略来处理接口兼容性
        return WeeklyOptimizationService(
            phase_coordinator=phase_coordinator,  # type: ignore
            llm_interface=llm_interface,
            logger=logger
        )

    def create_agent_creation_service(self,
                                    llm_provider: Optional[Any] = None,
                                    logger: Optional[logging.Logger] = None,
                                    enable_opro: bool = False):
        """
        创建智能体创建服务

        Args:
            llm_provider: LLM提供者
            logger: 日志记录器
            enable_opro: 是否启用OPRO优化功能

        Returns:
            AgentCreationService实例
        """
        if not self._configured:
            raise RuntimeError("ServiceFactory must be configured before creating services")

        # 导入智能体创建服务
        from .services.agent_creation_service import AgentCreationService
        from .llm_interface import LLMInterface

        # 创建LLM接口
        llm_interface = None
        if llm_provider:
            if isinstance(llm_provider, str):
                llm_interface = LLMInterface(provider=llm_provider, logger=logger)
            else:
                llm_interface = llm_provider
        else:
            # 尝试从配置获取默认LLM提供者
            config = self._infra_factory.get_config()
            default_provider = config.get("llm_integration", {}).get("primary_provider", "mock")
            llm_interface = LLMInterface(provider=default_provider, logger=logger)

        # 创建智能体创建服务
        return AgentCreationService(
            llm_interface=llm_interface,
            logger=logger,
            opro_enabled=enable_opro
        )

    @classmethod
    def create_assessor_with_config(cls,
                                  config: Dict[str, Any],
                                  agents: Optional[Dict[str, Any]] = None,
                                  logger: Optional[logging.Logger] = None,
                                  llm_provider: Optional[Any] = None,
                                  enable_opro: bool = False) -> "RefactoredContributionAssessor":
        """
        便利方法：一步创建配置好的评估器
        
        Args:
            config: 配置字典
            agents: 智能体配置
            logger: 日志记录器
            llm_provider: LLM提供者
            enable_opro: 是否启用OPRO优化
            
        Returns:
            配置好的RefactoredContributionAssessor实例
        """
        factory = cls()
        factory.configure_services(config)
        return factory.create_assessor(
            agents=agents,
            logger=logger,
            llm_provider=llm_provider,
            enable_opro=enable_opro
        )
    
    def create_daily_state_manager(self, 
                                   storage_path: Optional[str] = None,
                                   logger: Optional[logging.Logger] = None) -> Optional[DailyStateManager]:
        """
        创建每日状态管理器服务
        
        Args:
            storage_path: 数据库存储路径
            logger: 日志记录器
            
        Returns:
            DailyStateManager实例，如果不可用则返回None
        """
        if DailyStateManager is None:
            if logger:
                logger.warning("DailyStateManager不可用，跳过创建")
            return None
        
        try:
            daily_state_manager = DailyStateManager(storage_path=storage_path)
            
            if logger:
                logger.info("DailyStateManager服务创建成功")
            
            return daily_state_manager
            
        except Exception as e:
            if logger:
                logger.error(f"创建DailyStateManager失败: {str(e)}")
            return None
    
    def get_daily_state_manager(self) -> Optional[DailyStateManager]:
        """
        获取单例DailyStateManager实例
        
        Returns:
            DailyStateManager实例，如果不可用则返回None
        """
        # 检查是否已经创建了实例
        if not hasattr(self, '_daily_state_manager'):
            self._daily_state_manager = self.create_daily_state_manager()
        
        return self._daily_state_manager
    
    def register_daily_state_manager_service(self, 
                                            storage_path: Optional[str] = None) -> bool:
        """
        将DailyStateManager注册为服务到依赖注入容器
        
        Args:
            storage_path: 数据库存储路径
            
        Returns:
            bool: 注册是否成功
        """
        if DailyStateManager is None:
            return False
        
        try:
            # 使用基础设施服务工厂注册DailyStateManager
            from .infrastructure.service_factory import ServiceConfiguration
            
            daily_state_config = ServiceConfiguration(
                service_type="state_management.daily_state_manager.DailyStateManager",
                implementation_type="state_management.daily_state_manager.DailyStateManager",
                lifetime="singleton",
                configuration={"storage_path": storage_path} if storage_path else {},
                enabled=True
            )
            
            self._infra_factory.register_service_from_config(daily_state_config)
            
            # 同时注册工厂方法
            def daily_state_manager_factory():
                return DailyStateManager(storage_path=storage_path)
            
            self._infra_factory.register_factory_method(
                "create_daily_state_manager", 
                daily_state_manager_factory
            )
            
            return True
            
        except Exception as e:
            logging.error(f"注册DailyStateManager服务失败: {str(e)}")
            return False
    
    def create_enhanced_opro_service(self, 
                                   llm_interface=None,
                                   logger: Optional[logging.Logger] = None,
                                   enabled: bool = True) -> Any:
        """
        创建增强版SimplifiedOPROService，集成DailyStateManager
        
        Args:
            llm_interface: LLM接口
            logger: 日志记录器
            enabled: 是否启用服务
            
        Returns:
            增强版SimplifiedOPROService实例
        """
        try:
            from .services.simplified_opro_service import SimplifiedOPROService
            
            # 获取或创建DailyStateManager
            daily_state_manager = self.get_daily_state_manager()
            
            # 创建增强版OPRO服务
            enhanced_opro_service = SimplifiedOPROService(
                llm_interface=llm_interface,
                logger=logger,
                enabled=enabled,
                daily_state_manager=daily_state_manager
            )
            
            if logger:
                logger.info("增强版SimplifiedOPROService创建成功 (with historical data support)")
            
            return enhanced_opro_service
            
        except Exception as e:
            if logger:
                logger.error(f"创建增强版SimplifiedOPROService失败: {str(e)}")
            # 回退到基础版本
            try:
                from .services.simplified_opro_service import SimplifiedOPROService
                return SimplifiedOPROService(
                    llm_interface=llm_interface,
                    logger=logger,
                    enabled=enabled
                )
            except Exception as fallback_e:
                if logger:
                    logger.error(f"创建基础版SimplifiedOPROService也失败: {str(fallback_e)}")
                return None
