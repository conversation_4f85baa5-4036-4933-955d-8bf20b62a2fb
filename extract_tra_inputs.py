#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取包含"🤖 TRA 输入"的行内容的脚本
"""

import re
import sys
from pathlib import Path
from typing import List, Dict
import csv
import json


def extract_tra_inputs(file_path: str) -> List[Dict[str, str]]:
    """
    从日志文件中提取包含"🤖 TRA 输入"的行内容
    
    Args:
        file_path: 日志文件路径
        
    Returns:
        包含提取信息的字典列表
    """
    results = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                if '🤖 TRA 输入' in line:
                    # 解析日志行
                    parsed_data = parse_tra_input_line(line.strip(), line_num)
                    if parsed_data:
                        results.append(parsed_data)
    
    except FileNotFoundError:
        print(f"错误: 文件 '{file_path}' 不存在")
        return []
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return []
    
    return results


def parse_tra_input_line(line: str, line_num: int) -> Dict[str, str]:
    """
    解析包含TRA输入的日志行
    
    Args:
        line: 日志行内容
        line_num: 行号
        
    Returns:
        解析后的数据字典
    """
    # 正则表达式匹配日志格式
    # 示例: 2025-07-28 21:18:59,588 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000
    pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}).*?🤖 TRA 输入: 日期=([^,]+), 累计收益=([^,]+), 周收益=(.+)'
    
    match = re.search(pattern, line)
    if match:
        timestamp = match.group(1)
        date = match.group(2)
        cumulative_return = match.group(3)
        weekly_return = match.group(4)
        
        return {
            'line_number': str(line_num),
            'timestamp': timestamp,
            'date': date,
            'cumulative_return': cumulative_return,
            'weekly_return': weekly_return,
            'full_line': line
        }
    
    return None


def save_to_csv(data: List[Dict[str, str]], output_file: str):
    """保存数据到CSV文件"""
    if not data:
        print("没有数据可保存")
        return
    
    fieldnames = ['line_number', 'timestamp', 'date', 'cumulative_return', 'weekly_return', 'full_line']
    
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(data)
    
    print(f"数据已保存到 CSV 文件: {output_file}")


def save_to_json(data: List[Dict[str, str]], output_file: str):
    """保存数据到JSON文件"""
    if not data:
        print("没有数据可保存")
        return
    
    with open(output_file, 'w', encoding='utf-8') as jsonfile:
        json.dump(data, jsonfile, ensure_ascii=False, indent=2)
    
    print(f"数据已保存到 JSON 文件: {output_file}")


def print_summary(data: List[Dict[str, str]]):
    """打印提取结果摘要"""
    if not data:
        print("未找到包含'🤖 TRA 输入'的行")
        return
    
    print(f"\n=== 提取结果摘要 ===")
    print(f"总共找到 {len(data)} 条TRA输入记录")
    print(f"时间范围: {data[0]['date']} 到 {data[-1]['date']}")
    
    # 统计收益情况
    positive_returns = sum(1 for item in data if float(item['cumulative_return']) > 0)
    negative_returns = sum(1 for item in data if float(item['cumulative_return']) < 0)
    zero_returns = sum(1 for item in data if float(item['cumulative_return']) == 0)
    
    print(f"收益分布:")
    print(f"  正收益: {positive_returns} 次")
    print(f"  负收益: {negative_returns} 次")
    print(f"  零收益: {zero_returns} 次")
    
    print(f"\n前5条记录:")
    for i, item in enumerate(data[:5]):
        print(f"  {i+1}. {item['date']} - 累计收益: {item['cumulative_return']}, 周收益: {item['weekly_return']}")


def main():
    """主函数"""
    # 默认文件路径
    default_file = "test_results/NVDA_phase1.md"
    
    # 获取输入文件路径
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    else:
        input_file = default_file
    
    print(f"正在处理文件: {input_file}")
    
    # 提取数据
    extracted_data = extract_tra_inputs(input_file)
    
    if not extracted_data:
        print("未找到任何TRA输入数据")
        return
    
    # 打印摘要
    print_summary(extracted_data)
    
    # 生成输出文件名
    input_path = Path(input_file)
    base_name = input_path.stem
    output_dir = input_path.parent
    
    csv_output = output_dir / f"{base_name}_tra_inputs.csv"
    json_output = output_dir / f"{base_name}_tra_inputs.json"
    
    # 保存到文件
    save_to_csv(extracted_data, str(csv_output))
    save_to_json(extracted_data, str(json_output))
    
    print(f"\n处理完成! 共提取 {len(extracted_data)} 条记录")


if __name__ == "__main__":
    main()
