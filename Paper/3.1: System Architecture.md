# 

## 3.1. System Architecture: MAS as a Directed Acyclic Graph

To systematically manage agent interactions and enable principled contribution analysis, we model our Multi-Agent System (MAS) as a Directed Acyclic Graph (DAG). This architectural choice is motivated by three key observations: (1) many collaborative tasks exhibit natural information flow hierarchies that preclude cycles \cite{jang2023learning, chen2024dag}, (2) acyclic structures enable efficient topological scheduling and prevent information loops that can destabilize learning \cite{wang2024multiagent}, and (3) DAG constraints facilitate rigorous credit assignment through game-theoretic frameworks \cite{li2024shapley, rodriguez2024redefining}. Our approach builds upon recent advances in DAG-based multi-agent coordination while addressing the specific challenges of financial decision-making under uncertainty.

### Formal Mathematical Framework

Formally, we define the system as a graph $\mathcal{G} = (\mathcal{V}, \mathcal{E})$, where $\mathcal{V} = \{a_1, a_2, \ldots, a_N\}$ is the set of $N$ agents, and $\mathcal{E} \subseteq \mathcal{V} \times \mathcal{V}$ is the set of directed edges. An edge $(a_i, a_j) \in \mathcal{E}$ signifies that the output of agent $a_i$ is a required input for agent $a_j$. The acyclicity constraint ensures that there exists a topological ordering $\pi: \mathcal{V} \rightarrow \{1, 2, \ldots, N\}$ such that for all $(a_i, a_j) \in \mathcal{E}$, we have $\pi(a_i) < \pi(a_j)$.

Within this graph, we distinguish critical subsets of nodes:
- **Source nodes**: $\mathcal{V}_{\text{source}} = \{v \in \mathcal{V} : \text{in-degree}(v) = 0\}$, which process external information
- **Sink node**: $a_T \in \mathcal{V}$ such that $\text{out-degree}(a_T) = 0$, which produces the system's final output
- **Intermediate nodes**: $\mathcal{V}_{\text{intermediate}} = \mathcal{V} \setminus (\mathcal{V}_{\text{source}} \cup \{a_T\})$

### Information Flow Formalization

The DAG structure enforces strict information flow constraints through a formal access control mechanism. Let $\mathcal{D}$ represent the universe of available data, and define an information access function $I: \mathcal{V} \rightarrow 2^{\mathcal{D}}$ that maps each agent to its accessible data subset. For any agent $a_i$, its information set $I(a_i)$ includes:

$$I(a_i) = \mathcal{D}_{\text{external}}^{(i)} \cup \bigcup_{(a_j, a_i) \in \mathcal{E}} O(a_j)$$

where $\mathcal{D}_{\text{external}}^{(i)}$ represents external data directly accessible to agent $a_i$, and $O(a_j)$ denotes the output of agent $a_j$. This formalization ensures that information flows strictly according to the DAG topology, preventing unauthorized data access that could compromise the system's hierarchical structure.

### Architectural Benefits and Computational Properties

The DAG constraint provides several computational and organizational advantages:

1. **Efficient Scheduling**: The topological ordering enables parallel execution within layers while maintaining dependencies, achieving optimal scheduling complexity $O(|\mathcal{V}| + |\mathcal{E}|)$.

2. **Bounded Computation**: The acyclic property guarantees termination and prevents infinite loops in iterative processes, ensuring system stability.

3. **Hierarchical Learning**: The structured information flow enables layer-wise optimization and facilitates credit assignment through backward propagation of gradients or game-theoretic value distribution.

### Financial Trading System Instantiation

To ground this abstraction, we instantiate our framework within a multi-agent financial trading system designed for stock market analysis and decision-making, a domain where multi-agent approaches have demonstrated significant promise \cite{liu2024tradingagents, zhang2024multiagent}. The system comprises seven agents organized into three distinct hierarchical layers, as illustrated in Figure 1:

**Analysis Layer** ($\mathcal{V}_{\text{source}}$): This layer consists of three independent specialist agents:
- **News Analyst Agent (NAA)**: Processes news feeds and sentiment data
- **Technical Analyst Agent (TAA)**: Analyzes price history and technical indicators  
- **Fundamental Analyst Agent (FAA)**: Evaluates financial statements and company metrics

Each agent processes raw, heterogeneous market data in parallel to produce focused analytical summaries, forming the source nodes of our DAG.

**Outlook Layer** ($\mathcal{V}_{\text{intermediate}}$): This layer contains three agents that synthesize outputs from the Analysis Layer:
- **Bullish Outlook Agent (BOA)**: Aggregates positive market signals
- **Bearish Outlook Agent (BeOA)**: Consolidates negative market indicators
- **Neutral Outlook Agent (NOA)**: Balances conflicting signals

These agents are explicitly prohibited from accessing raw market data through the information flow constraints defined by $I(\cdot)$, forcing reliance solely on structured analyses from the previous layer.

**Decision Layer** (Sink Node): The **Trader Agent (TRA)** constitutes the final layer, consuming competing outlooks to produce actionable trading decisions. This agent serves as the system's sink node $a_T$.

### Technical Implementation Architecture

This logical DAG is realized through a modular, service-oriented software architecture. The structure is explicitly defined through:

- **Graph Representation**: An adjacency list (`agent_graph`) encoding the DAG structure
- **Execution Scheduling**: A topologically sorted layer list (`agent_layers`) for efficient parallel execution
- **Coordination Service**: A `PhaseCoordinator` that orchestrates operational phases according to the topological ordering
- **Dependency Management**: A `ServiceFactory` implementing dependency injection patterns
- **Access Control**: A data access controller that strictly enforces information flow constraints defined by the function $I(\cdot)$

The integration of Shapley value theory into this DAG-based framework provides a principled approach to contribution assessment that leverages the structured information flow \cite{thompson2023collective, kumar2024unleashing}. This combination enables both efficient execution and fair credit assignment, making it particularly suitable for collaborative decision-making tasks where individual agent contributions must be quantified and optimized.

---

## References (Additional Citations Needed)

- \cite{jang2023learning}: Jang et al. (2023). "Learning Multiple Coordinated Agents under Directed Acyclic Graph Constraints." ICML.
- \cite{chen2024dag}: Chen et al. (2024). "DAG-Plan: Generating Directed Acyclic Dependency Graphs for Dual-Arm Cooperative Planning."
- \cite{wang2024multiagent}: Wang et al. (2024). "Multi-agent Reinforcement Learning-based Adaptive Heterogeneous DAG Scheduling."
- \cite{li2024shapley}: Li et al. (2024). "Shapley Value Based Multi-Agent Reinforcement Learning: Theory, Method and Its Application to Energy Network." arXiv:2402.15324.
- \cite{rodriguez2024redefining}: Rodriguez et al. (2024). "Redefining Contributions: Shapley-Driven Federated Learning." IJCAI.
- \cite{liu2024tradingagents}: Liu et al. (2024). "TradingAgents: Multi-Agents LLM Financial Trading Framework." arXiv:2412.20138.
- \cite{zhang2024multiagent}: Zhang et al. (2024). "A Multi-Agent Reinforcement Learning Framework for Optimizing Financial Trading Strategies Based on TimesNet." Expert Systems with Applications.
- \cite{thompson2023collective}: Thompson et al. (2023). "Collective eXplainable AI: Explaining Cooperative Strategies and Agent Contribution in Multiagent Reinforcement Learning with Shapley Values."
- \cite{kumar2024unleashing}: Kumar et al. (2024). "Unleashing the Power of Multi-Agent Reinforcement Learning for Algorithmic Trading." Computers, Materials & Continua.

---

- 