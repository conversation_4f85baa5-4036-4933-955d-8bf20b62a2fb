# Design Document

## Overview

本设计文档描述了如何将现有的巨大`ContributionAssessor`类重构为低耦合、高聚合的模块化架构。重构将遵循SOLID原则，采用依赖注入、事件驱动和分层架构等设计模式，同时保持所有现有功能的完整性和向后兼容性。

## Architecture

### 整体架构设计

采用分层架构模式，将系统分为以下层次：

```
┌─────────────────────────────────────────┐
│           Facade Layer                  │  ← ContributionAssessor (保持兼容)
├─────────────────────────────────────────┤
│         Coordination Layer              │  ← PhaseCoordinator
├─────────────────────────────────────────┤
│          Service Layer                  │  ← 各种专门服务
├─────────────────────────────────────────┤
│          Domain Layer                   │  ← 数据传输对象和业务逻辑
├─────────────────────────────────────────┤
│       Infrastructure Layer             │  ← 配置、日志、事件总线
└─────────────────────────────────────────┘
```

### 核心设计原则

1. **单一职责原则**：每个服务类只负责一个明确的功能领域
2. **依赖注入**：通过构造函数注入依赖，避免硬编码
3. **接口抽象**：使用抽象基类定义服务接口
4. **事件驱动**：通过事件总线解耦服务间通信
5. **配置驱动**：通过配置文件控制系统行为

## Components and Interfaces

### 1. Facade Layer - ContributionAssessor

保持原有的公共接口，作为外观模式的实现：

```python
class ContributionAssessor:
    """
    重构后的主协调器 - 作为外观模式保持向后兼容性
    """
    def __init__(self, config, agents, logger, llm_provider, enable_opro, opro_config):
        self.phase_coordinator = PhaseCoordinator(...)
        
    def run(self, agents, target_agents, max_coalitions) -> Dict[str, Any]:
        """保持原有接口不变"""
        return self.phase_coordinator.execute_assessment_workflow(...)
        
    # 保持所有现有的公共方法...
```

### 2. Coordination Layer - PhaseCoordinator

负责协调四个执行阶段的流程：

```python
from abc import ABC, abstractmethod

class IPhaseCoordinator(ABC):
    @abstractmethod
    def execute_assessment_workflow(self, request: AssessmentRequest) -> AssessmentResult:
        pass

class PhaseCoordinator(IPhaseCoordinator):
    """
    阶段协调器 - 管理四个执行阶段的顺序和数据流
    """
    def __init__(self, 
                 coalition_service: ICoalitionService,
                 simulation_service: ISimulationService,
                 shapley_service: IShapleyService,
                 opro_service: IOPROService,
                 state_manager: IStateManager,
                 event_bus: IEventBus):
        self.coalition_service = coalition_service
        self.simulation_service = simulation_service
        self.shapley_service = shapley_service
        self.opro_service = opro_service
        self.state_manager = state_manager
        self.event_bus = event_bus
        
    def execute_assessment_workflow(self, request: AssessmentRequest) -> AssessmentResult:
        """执行完整的四阶段评估流程"""
        # 阶段1: 联盟生成
        coalition_result = self.coalition_service.generate_coalitions(request.target_agents)
        
        # 阶段2: 交易模拟
        simulation_result = self.simulation_service.run_simulations(
            coalition_result.valid_coalitions, request.agents, request.max_coalitions
        )
        
        # 阶段3: 周期性Shapley值计算
        shapley_result = self.shapley_service.calculate_periodic_shapley(
            request.target_agents, simulation_result.coalition_daily_returns
        )
        
        # 阶段4: OPRO优化
        opro_result = self.opro_service.optimize_agents(
            request.target_agents, shapley_result
        )
        
        return AssessmentResult(...)
```

### 3. Service Layer - 专门服务

#### 3.1 CoalitionService - 联盟生成服务

```python
class ICoalitionService(ABC):
    @abstractmethod
    def generate_coalitions(self, target_agents: List[str]) -> CoalitionResult:
        pass

class CoalitionService(ICoalitionService):
    """联盟生成服务 - 负责生成和剪枝智能体联盟"""
    def __init__(self, coalition_manager: CoalitionManager, logger: logging.Logger):
        self.coalition_manager = coalition_manager
        self.logger = logger
        
    def generate_coalitions(self, target_agents: List[str]) -> CoalitionResult:
        """生成有效联盟"""
        # 原有的 _run_coalition_generation_phase 逻辑
        pass
```

#### 3.2 SimulationService - 交易模拟服务

```python
class ISimulationService(ABC):
    @abstractmethod
    def run_simulations(self, coalitions: Set[frozenset], 
                       agents: Dict[str, Any], 
                       max_coalitions: Optional[int]) -> SimulationResult:
        pass

class SimulationService(ISimulationService):
    """交易模拟服务 - 负责运行联盟交易模拟"""
    def __init__(self, 
                 trading_simulator: TradingSimulator,
                 concurrent_executor: IConcurrentExecutor,
                 logger: logging.Logger):
        self.trading_simulator = trading_simulator
        self.concurrent_executor = concurrent_executor
        self.logger = logger
        
    def run_simulations(self, coalitions, agents, max_coalitions) -> SimulationResult:
        """运行交易模拟"""
        # 原有的 _run_trading_simulation_phase 逻辑
        pass
```

#### 3.3 ShapleyService - Shapley值计算服务

```python
class IShapleyService(ABC):
    @abstractmethod
    def calculate_periodic_shapley(self, target_agents: List[str], 
                                 coalition_returns: Dict) -> ShapleyResult:
        pass

class ShapleyService(IShapleyService):
    """Shapley值计算服务 - 负责周期性Shapley值计算"""
    def __init__(self, 
                 shapley_calculator: ShapleyCalculator,
                 trading_calendar: ITradingCalendar,
                 logger: logging.Logger):
        self.shapley_calculator = shapley_calculator
        self.trading_calendar = trading_calendar
        self.logger = logger
        
    def calculate_periodic_shapley(self, target_agents, coalition_returns) -> ShapleyResult:
        """计算周期性Shapley值"""
        # 原有的 _run_periodic_shapley_calculation_phase 逻辑
        pass
```

#### 3.4 OPROService - OPRO优化服务

```python
class IOPROService(ABC):
    @abstractmethod
    def optimize_agents(self, target_agents: List[str], 
                       shapley_result: ShapleyResult) -> OPROResult:
        pass

class OPROService(IOPROService):
    """OPRO优化服务 - 负责智能体提示词优化"""
    def __init__(self, 
                 opro_optimizer: Optional[OPROOptimizer],
                 historical_score_manager: Optional[HistoricalScoreManager],
                 enabled: bool,
                 logger: logging.Logger):
        self.opro_optimizer = opro_optimizer
        self.historical_score_manager = historical_score_manager
        self.enabled = enabled
        self.logger = logger
        
    def optimize_agents(self, target_agents, shapley_result) -> OPROResult:
        """优化智能体提示词"""
        # 原有的 _run_opro_optimization_phase 逻辑
        pass
```

### 4. Domain Layer - 数据传输对象

```python
@dataclass
class AssessmentRequest:
    """评估请求数据传输对象"""
    agents: Optional[Dict[str, Any]]
    target_agents: Optional[List[str]]
    max_coalitions: Optional[int]
    config: Dict[str, Any]

@dataclass
class AssessmentResult:
    """评估结果数据传输对象"""
    success: bool
    execution_time: float
    shapley_values: Dict[str, float]
    shapley_analysis: Dict[str, Any]
    periodic_shapley_results: Dict[str, Any]
    opro_optimization_results: Dict[str, Any]
    phase_results: Dict[str, Any]
    summary: Dict[str, Any]
    error: Optional[str] = None

@dataclass
class CoalitionResult:
    """联盟生成结果"""
    success: bool
    valid_coalitions: Set[frozenset]
    pruned_coalitions: Set[frozenset]
    coalition_analysis: Dict[str, Any]
    generation_stats: Dict[str, Any]

@dataclass
class SimulationResult:
    """交易模拟结果"""
    success: bool
    coalition_values: Dict[frozenset, float]
    coalition_daily_returns: Dict[frozenset, List[float]]
    simulation_stats: Dict[str, Any]
    simulator_stats: Dict[str, Any]

@dataclass
class ShapleyResult:
    """Shapley值计算结果"""
    success: bool
    total_weeks: int
    weekly_results: List[Dict[str, Any]]
    periodic_data: List[Dict[str, Any]]
    trading_days_per_week: int
    compilation_mode: str

@dataclass
class OPROResult:
    """OPRO优化结果"""
    success: bool
    enabled: bool
    optimization_result: Optional[Dict[str, Any]]
    updated_agents: List[str]
    execution_time: float
    error: Optional[str] = None
```

### 5. Infrastructure Layer

#### 5.1 StateManager - 状态管理服务

```python
class IStateManager(ABC):
    @abstractmethod
    def update_stats(self, execution_time: float, success: bool) -> None:
        pass
    
    @abstractmethod
    def get_stats(self) -> Dict[str, Any]:
        pass

class StateManager(IStateManager):
    """状态管理服务 - 管理系统运行时状态和统计信息"""
    def __init__(self, event_bus: IEventBus):
        self.event_bus = event_bus
        self._stats = {
            "total_runs": 0,
            "successful_runs": 0,
            "failed_runs": 0,
            "last_run_time": 0.0,
            "average_run_time": 0.0,
            "last_run": None
        }
        self._concurrent_stats = {
            "total_concurrent_tasks": 0,
            "successful_concurrent_tasks": 0,
            "failed_concurrent_tasks": 0,
            "concurrent_execution_time": 0.0
        }
        
    def update_stats(self, execution_time: float, success: bool) -> None:
        """更新执行统计信息"""
        # 原有的 _update_stats 逻辑
        self.event_bus.publish("stats_updated", {"execution_time": execution_time, "success": success})
```

#### 5.2 EventBus - 事件总线

```python
class IEventBus(ABC):
    @abstractmethod
    def publish(self, event_type: str, data: Any) -> None:
        pass
    
    @abstractmethod
    def subscribe(self, event_type: str, callback: Callable) -> None:
        pass

class EventBus(IEventBus):
    """事件总线 - 解耦服务间通信"""
    def __init__(self):
        self._subscribers: Dict[str, List[Callable]] = defaultdict(list)
        
    def publish(self, event_type: str, data: Any) -> None:
        """发布事件"""
        for callback in self._subscribers[event_type]:
            try:
                callback(data)
            except Exception as e:
                # 记录错误但不中断其他订阅者
                pass
                
    def subscribe(self, event_type: str, callback: Callable) -> None:
        """订阅事件"""
        self._subscribers[event_type].append(callback)
```

#### 5.3 ConcurrentExecutor - 并发执行器

```python
class IConcurrentExecutor(ABC):
    @abstractmethod
    def execute_concurrent_simulations(self, tasks: List[Callable]) -> List[Any]:
        pass

class ConcurrentExecutor(IConcurrentExecutor):
    """并发执行器 - 管理并发任务执行"""
    def __init__(self, max_workers: int, logger: logging.Logger):
        self.max_workers = max_workers
        self.logger = logger
        
    def execute_concurrent_simulations(self, tasks: List[Callable]) -> List[Any]:
        """执行并发模拟任务"""
        # 原有的并发执行逻辑
        pass
```

#### 5.4 ConfigurationManager - 配置管理器

```python
class IConfigurationManager(ABC):
    @abstractmethod
    def get_config(self, key: str, default: Any = None) -> Any:
        pass

class ConfigurationManager(IConfigurationManager):
    """配置管理器 - 集中管理系统配置"""
    def __init__(self, config_dict: Dict[str, Any]):
        self.config = config_dict
        
    def get_config(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        return self.config.get(key, default)
```

## Data Models

### 配置数据模型

```python
@dataclass
class AssessmentConfig:
    """评估配置"""
    start_date: str
    end_date: str
    stocks: List[str]
    starting_cash: int
    risk_free_rate: float
    simulation_days: Optional[int]
    enable_concurrent_execution: bool
    max_concurrent_api_calls: int
    trading_days_per_week: int

@dataclass
class OPROConfig:
    """OPRO配置"""
    optimization_frequency: str
    candidates_per_generation: int
    historical_weeks_to_consider: int
    temperature: float
    max_optimization_iterations: int
    convergence_threshold: float
    enable_cache: bool
    parallel_evaluation: bool
    max_workers: int
```

### 服务注册表

```python
class ServiceRegistry:
    """服务注册表 - 管理服务实例的创建和依赖注入"""
    def __init__(self):
        self._services: Dict[str, Any] = {}
        self._factories: Dict[str, Callable] = {}
        
    def register_service(self, service_type: str, instance: Any) -> None:
        """注册服务实例"""
        self._services[service_type] = instance
        
    def register_factory(self, service_type: str, factory: Callable) -> None:
        """注册服务工厂"""
        self._factories[service_type] = factory
        
    def get_service(self, service_type: str) -> Any:
        """获取服务实例"""
        if service_type in self._services:
            return self._services[service_type]
        elif service_type in self._factories:
            instance = self._factories[service_type]()
            self._services[service_type] = instance
            return instance
        else:
            raise ValueError(f"Service {service_type} not registered")
```

## Error Handling

### 异常层次结构

```python
class AssessmentError(Exception):
    """评估系统基础异常"""
    pass

class CoalitionGenerationError(AssessmentError):
    """联盟生成异常"""
    pass

class SimulationError(AssessmentError):
    """交易模拟异常"""
    pass

class ShapleyCalculationError(AssessmentError):
    """Shapley值计算异常"""
    pass

class OPROOptimizationError(AssessmentError):
    """OPRO优化异常"""
    pass
```

### 错误处理策略

```python
class ErrorHandler:
    """错误处理器 - 统一处理和记录错误"""
    def __init__(self, logger: logging.Logger, event_bus: IEventBus):
        self.logger = logger
        self.event_bus = event_bus
        
    def handle_error(self, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理错误并返回标准化的错误响应"""
        error_info = {
            "error_type": type(error).__name__,
            "error_message": str(error),
            "context": context,
            "timestamp": datetime.now().isoformat()
        }
        
        self.logger.error(f"Error in {context.get('phase', 'unknown')}: {error}")
        self.event_bus.publish("error_occurred", error_info)
        
        return {
            "success": False,
            "error": str(error),
            "error_type": type(error).__name__,
            "context": context
        }
```

## Testing Strategy

### 单元测试策略

1. **服务层测试**：每个服务类都有独立的单元测试
2. **模拟依赖**：使用Mock对象隔离外部依赖
3. **接口测试**：验证所有接口的实现符合契约
4. **数据传输对象测试**：验证DTO的序列化和反序列化

### 集成测试策略

1. **阶段协调器测试**：测试四个阶段的完整流程
2. **事件总线测试**：验证事件发布和订阅机制
3. **依赖注入测试**：验证服务注册和获取机制
4. **向后兼容性测试**：确保外观模式正确工作

### 测试工具和框架

```python
# 测试基础设施
class TestServiceRegistry(ServiceRegistry):
    """测试用服务注册表"""
    def register_mock_service(self, service_type: str, mock_instance: Any):
        self.register_service(service_type, mock_instance)

class MockEventBus(IEventBus):
    """模拟事件总线"""
    def __init__(self):
        self.published_events = []
        
    def publish(self, event_type: str, data: Any) -> None:
        self.published_events.append((event_type, data))
        
    def subscribe(self, event_type: str, callback: Callable) -> None:
        pass

# 示例测试用例
class TestCoalitionService(unittest.TestCase):
    def setUp(self):
        self.mock_coalition_manager = Mock()
        self.mock_logger = Mock()
        self.service = CoalitionService(self.mock_coalition_manager, self.mock_logger)
        
    def test_generate_coalitions_success(self):
        # 测试成功生成联盟的情况
        target_agents = ["NAA", "TAA", "FAA", "TRA"]
        expected_result = CoalitionResult(...)
        
        result = self.service.generate_coalitions(target_agents)
        
        self.assertTrue(result.success)
        self.assertEqual(len(result.valid_coalitions), expected_count)
```

### 性能测试

1. **基准测试**：对比重构前后的性能指标
2. **内存使用测试**：验证内存使用优化
3. **并发性能测试**：测试并发执行的效率
4. **负载测试**：测试系统在高负载下的表现

## Migration Strategy

### 渐进式重构策略

1. **阶段1**：创建新的服务接口和基础设施
2. **阶段2**：逐个迁移服务实现
3. **阶段3**：创建协调器和外观模式
4. **阶段4**：完整测试和性能优化
5. **阶段5**：清理旧代码和文档更新

### 向后兼容性保证

1. **保持公共接口**：ContributionAssessor的所有公共方法保持不变
2. **返回值兼容**：确保返回值格式和结构一致
3. **异常兼容**：保持异常类型和消息的一致性
4. **配置兼容**：支持现有的配置格式

### 风险缓解措施

1. **功能开关**：通过配置控制新旧实现的切换
2. **A/B测试**：并行运行新旧实现进行对比
3. **回滚机制**：出现问题时快速回滚到旧实现
4. **监控告警**：实时监控系统性能和错误率