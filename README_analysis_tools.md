# 净值变化数据提取与金融分析工具

本工具集用于从交易日志中提取"💰 第X天净值变化"数据，并进行全面的金融指标分析。

## 工具概览

### 1. `extract_tra_inputs.py` - 完整数据提取工具
**功能**: 提取净值变化数据并计算基础金融指标
**输出文件**:
- `*_net_value_changes.csv` - 原始数据表格
- `*_net_value_changes.json` - 原始数据JSON格式
- `*_financial_metrics.json` - 基础金融指标

**使用方法**:
```bash
python extract_tra_inputs.py [文件路径]
```

### 2. `simple_tra_extract.py` - 快速查看工具
**功能**: 在终端直接显示数据和快速金融指标
**特点**: 无文件输出，快速查看

**使用方法**:
```bash
python simple_tra_extract.py [文件路径]
```

### 3. `advanced_financial_analysis.py` - 高级分析工具
**功能**: 提供详细的金融风险分析报告
**输出文件**:
- `*_advanced_metrics.json` - 详细金融指标

**使用方法**:
```bash
python advanced_financial_analysis.py [文件路径]
```

## 分析指标说明

### 基础收益指标
- **累计收益率**: 整个期间的总收益率
- **年化收益率**: 按252个交易日年化的收益率
- **日均收益率**: 平均每日收益率

### 风险指标
- **日收益波动率**: 日收益率的标准差
- **年化波动率**: 年化的收益率波动性
- **下行偏差**: 只考虑负收益的波动率
- **VaR (95%)**: 95%置信度下的风险价值
- **CVaR (95%)**: 条件风险价值，超过VaR的平均损失

### 风险调整收益指标
- **夏普率**: (年化收益率 - 无风险利率) / 年化波动率
- **索提诺率**: (年化收益率 - 无风险利率) / 下行偏差
- **卡玛率**: 年化收益率 / 最大回撤

### 回撤分析
- **最大回撤**: 从峰值到谷值的最大跌幅比例
- **平均回撤持续期**: 回撤期间的平均持续天数
- **最长回撤持续期**: 最长的回撤持续天数

### 交易统计
- **胜率**: 正收益交易日的比例
- **盈亏比**: 平均盈利 / 平均亏损的绝对值
- **平均盈利**: 盈利交易日的平均收益率
- **平均亏损**: 亏损交易日的平均收益率

### 分布特征
- **偏度**: 收益分布的偏斜程度（负值表示左偏）
- **峰度**: 收益分布的尖锐程度（超额峰度）

## 示例分析结果

基于 `NVDA_phase1.md` 的分析结果：

```
📊 基础统计信息
交易天数:                69 天
累计收益率:               -0.0874 (-8.74%)
年化收益率:               0.2650 (26.50%)
日均收益率:               0.0009 (0.09%)

⚠️  风险指标
日收益波动率:              0.0371 (3.71%)
年化波动率:               0.5891 (58.91%)
下行偏差:                0.4580 (45.80%)
VaR (95%):           -0.0415 (-4.15%)
CVaR (95%):          -0.0840 (-8.40%)

📈 风险调整收益指标
夏普率:                 0.3990
索提诺率:                0.5131
卡玛率:                 1.1444

📉 回撤分析
最大回撤:                0.2316 (23.16%)
平均回撤持续期:             57.0 天
最长回撤持续期:             57 天

🎯 交易统计
胜率:                  0.5217 (52.17%)
盈亏比:                 0.9820
平均盈利:                0.0269 (2.69%)
平均亏损:                -0.0274 (-2.74%)

📊 收益分布特征
偏度:                  -1.0588
峰度:                  5.0436
最大单日收益:              0.0893 (8.93%)
最大单日亏损:              -0.1697 (-16.97%)
```

## 指标解读

### 收益表现
- 虽然累计收益为负(-8.74%)，但年化收益率达到26.50%，说明策略具有盈利潜力
- 日均收益率0.09%，年化后的收益率较为可观

### 风险特征
- 年化波动率58.91%，属于高波动策略
- 夏普率0.3990，风险调整后收益一般
- 索提诺率0.5131 > 夏普率，说明上行波动较大
- 卡玛率1.1444，回撤控制相对较好

### 交易特征
- 胜率52.17%，略高于50%
- 盈亏比0.9820，接近1:1，盈亏基本平衡
- 最大单日亏损-16.97%，风险控制需要加强

### 分布特征
- 偏度-1.0588，收益分布左偏，存在较大亏损的尾部风险
- 峰度5.0436，分布较为尖锐，极端收益出现频率较高

## 依赖库

```bash
pip install numpy pandas
```

## 注意事项

1. 所有分析基于252个交易日年化假设
2. 无风险利率假设为3%
3. 分析结果仅供参考，实际投资需考虑更多因素
4. 建议结合其他风险管理工具使用
