# 修复子集联盟检测问题需求文档

## 介绍

系统在周期性优化的第2阶段中无法找到子集联盟，导致跳过了重要的联盟子集运行步骤。这个问题影响了Shapley值计算的准确性和OPRO优化的效果。需要修复子集联盟检测机制，确保系统能够正确识别和使用可用的联盟子集。

## 需求

### 需求 1

**用户故事:** 作为系统管理员，我希望周期性优化能够正确检测到子集联盟，以便系统能够完整执行四阶段优化流程。

#### 验收标准

1. WHEN 系统进入阶段2（运行所有联盟子集）THEN 系统 SHALL 能够检测到至少一个有效的子集联盟
2. WHEN 没有预存的子集联盟时 THEN 系统 SHALL 自动生成基本的子集联盟
3. WHEN 子集联盟检测失败时 THEN 系统 SHALL 记录详细的调试信息并尝试恢复

### 需求 2

**用户故事:** 作为开发者，我希望子集联盟管理机制是健壮的，以便系统能够在各种情况下都能找到合适的联盟进行分析。

#### 验收标准

1. WHEN 联盟管理器初始化时 THEN 系统 SHALL 确保至少有基本的子集联盟可用
2. IF 默认智能体列表可用 THEN 系统 SHALL 生成所有可能的非空真子集作为子集联盟
3. WHEN 子集联盟为空时 THEN 系统 SHALL 从默认智能体生成最小可行的子集联盟

### 需求 3

**用户故事:** 作为系统用户，我希望能够通过日志清楚地了解子集联盟的状态和处理过程，以便进行问题诊断和系统监控。

#### 验收标准

1. WHEN 系统检测子集联盟时 THEN 系统 SHALL 记录找到的联盟数量和类型
2. WHEN 生成新的子集联盟时 THEN 系统 SHALL 记录生成的联盟详情
3. IF 子集联盟检测或生成失败 THEN 系统 SHALL 记录具体的错误原因和建议的解决方案