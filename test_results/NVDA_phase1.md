2025-07-28 21:18:13,419 - __main__ - INFO - ======================================================================
2025-07-28 21:18:13,419 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-28 21:18:13,419 - __main__ - INFO - ======================================================================
2025-07-28 21:18:13,419 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-28 21:18:13,419 - __main__ - INFO - 开始模拟联盟: {'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'}
2025-07-28 21:18:13,419 - __main__ - INFO - 📅 当前周数: 1
2025-07-28 21:18:13,419 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-28 21:18:13,419 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'})
2025-07-28 21:18:13,420 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-28 21:18:13,549 - __main__ - INFO - 📅 设置当前模拟周数: 1 (来自参数)
2025-07-28 21:18:13,550 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 21:18:37,280 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 21:18:37,281 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 21:18:59,586 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 21:18:59,588 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 21:18:59,588 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000
2025-07-28 21:19:03,709 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 21:19:03,710 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 21:19:03,710 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 21:19:03,710 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 21:19:03,710 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 21:19:03,710 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 21:19:03,710 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 21:19:03,710 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对NVDA进行了全面的多维度分析。基础面分析显示，尽管缺乏具体的新闻分析，但NVDA的财务健康度评分为8.5，显示出良好的财务状况。同时，其估值评估为高估，这可能表明市场对其预期较高。技术面分析显示，NVDA的价格趋势呈上升趋势，技术评分达到0.6，表明市场趋势向上。具体来看，RSI指数为68，接近超买区域，但MACD线和移动平均线均显示出上升趋势。展望层分析中，尽管缺乏具体的看涨或看跌信号，NOA认为市场展望为中性，这表明市场情绪不明朗。综合考虑各层级信息，基础面和技术面均显示出NVDA的上涨潜力，而展望层的中性观点也提醒我们保持谨慎。因此，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-02'}
2025-07-28 21:19:03,710 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-28 21:19:03,710 - __main__ - INFO - 📈 第1天交易动作: {'NVDA': 1.0}
2025-07-28 21:19:03,715 - __main__ - INFO - 💰 第1天净值变化: 收益率=0.0284, 净值=$1028371.92
2025-07-28 21:19:03,717 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 21:19:39,259 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 21:19:39,261 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 21:19:57,562 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 21:19:57,562 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 21:19:57,562 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-03, 累计收益=0.0284, 周收益=0.0284
2025-07-28 21:20:06,837 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 21:20:06,837 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 21:20:06,837 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 21:20:06,837 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 21:20:06,838 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 21:20:06,838 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 21:20:06,838 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 21:20:06,838 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA进行了全面的多维度分析。首先，从基础面来看，FAA显示NVDA财务健康度良好，且在竞争地位上处于领先。NAA分析显示，NVDA在人工智能和芯片领域的领导地位以及与Cerence的合作受到了市场的积极关注，这表明公司基本面稳健。其次，技术面分析显示，TAA指出股价呈现上升趋势，且成交量增加，这进一步支持了技术面的看涨信号。展望层智能体中，BOA提供了较强的看涨信号，认为NVDA在人工智能领域的领导地位将推动股价上涨。然而，BeOA提出了看跌展望，主要基于潜在的市场风险。NOA则认为市场存在不确定性，建议观望。在风险评估方面，我们注意到信号的一致性较高，风险控制指标也显示较低的风险。综合以上分析，**最终决策：买入**。我们相信NVDA的长期增长潜力，且当前的市场信号支持这一决策。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-03'}
2025-07-28 21:20:06,838 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-28 21:20:06,838 - __main__ - INFO - 📈 第2天交易动作: {'NVDA': 1.0}
2025-07-28 21:20:06,840 - __main__ - INFO - 💰 第2天净值变化: 收益率=0.0407, 净值=$1070268.28
2025-07-28 21:20:06,842 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 21:20:26,076 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 21:20:26,076 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 21:20:50,559 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 21:20:50,561 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 21:20:50,561 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-06, 累计收益=0.0703, 周收益=0.0703
2025-07-28 21:21:07,960 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 21:21:07,961 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 21:21:07,961 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 21:21:07,961 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 21:21:07,961 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 21:21:07,961 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 21:21:07,961 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 21:21:07,962 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在2025-01-06的分析中，我们对NVDA进行了全面的多维度分析。首先，从基本面分析来看，FAA给出了财务健康度评分为8.5，表明公司财务状况良好。尽管NAA没有提供具体的新闻和情绪数据，但根据FAA的评估，我们可以推断出公司基本面稳健。其次，技术面分析显示，TAA给出了技术评分为0.8，表明股票处于上升趋势，且支撑位和阻力位分析显示股票在$147.00附近有较强支撑，在$152.00附近有阻力。技术指标如RSI和MACD也显示出看涨趋势。展望层分析中，虽然BOA和BeOA的分析信心度未知，但根据TAA的技术分析结果，我们可以认为看涨信号较强。NOA的分析信心度也为未知，但考虑到技术面的看涨趋势，我们可以认为上涨概率较高。在风险评估方面，各层级信号基本一致，分歧度较低，市场环境不确定性较高，但技术面和基本面均支持看涨。因此，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-06'}
2025-07-28 21:21:07,962 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-28 21:21:07,962 - __main__ - INFO - 📈 第3天交易动作: {'NVDA': 1.0}
2025-07-28 21:21:07,964 - __main__ - INFO - 💰 第3天净值变化: 收益率=0.0225, 净值=$1094303.56
2025-07-28 21:21:07,966 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 21:21:33,859 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 21:21:33,860 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 21:22:04,138 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 21:22:04,139 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 21:22:04,140 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-07, 累计收益=0.0943, 周收益=0.0943
2025-07-28 21:22:15,285 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 21:22:15,286 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 21:22:15,286 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 21:22:15,286 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 21:22:15,286 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 21:22:15,286 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对NVDA的基础面进行了评估。根据FAA的基本面分析，NVDA的财务健康度评分为8.5，显示出良好的财务状况。然而，估值评估显示NVDA目前处于高估状态，这可能对长期投资者构成风险。NAA由于缺乏具体新闻和情绪数据，无法进行情绪分析和新闻摘要，但分析信心度设置为1，表明在没有数据的情况下，分析结果基于逻辑推断。技术面分析（TAA）显示NVDA股票价格出现了下跌趋势，技术评分-0.5表明市场处于看跌状态，但分析信心度为0.7，表示有一定信心但不是完全确定。展望层智能体（BOA、BeOA、NOA）的分析结果也显示出市场的不确定性，其中NOA建议观望。综合考虑各层级信息，基础面和技术面显示出的信号存在分歧，且展望层智能体信号不一致，因此，我们无法确定明确的买入或卖出信号。基于以上分析，**最终决策：保持不变**。', 'action': 'hold', 'trading_actions': {'__HOLD__': 0.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-07'}
2025-07-28 21:22:15,287 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 0.0}
2025-07-28 21:22:15,287 - __main__ - INFO - 📈 第4天交易动作: {'__HOLD__': 0.0}
2025-07-28 21:22:15,290 - __main__ - INFO - 💰 第4天净值变化: 收益率=-0.0084, 净值=$1085115.77
2025-07-28 21:22:15,292 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 21:22:47,021 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 21:22:47,022 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 21:23:10,781 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 21:23:10,782 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 21:23:10,782 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-08, 累计收益=0.0851, 周收益=0.0851
2025-07-28 21:23:23,200 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 21:23:23,202 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 21:23:23,202 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 21:23:23,203 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 21:23:23,203 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 21:23:23,203 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 21:23:23,203 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 21:23:23,203 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA进行了全面的多维度分析。首先，从基础面来看，FAA基本面分析显示NVDA财务健康度良好，估值处于高估状态，但公司竞争地位领先。NAA新闻分析由于缺乏相关新闻，无法进行具体分析，但根据情绪评分和影响评估，市场情绪较为稳定。技术面分析显示，TAA技术分析结果显示NVDA价格趋势中性，存在轻微的看跌迹象，但支撑位和阻力位较为明显，提供了入场和风险控制点。展望层分析中，BOA看涨信号强度较弱，BeOA看跌信号也不强，NOA认为市场处于中性状态。综合分析，各层级信号存在一定分歧，但基础面和技术面显示NVDA整体表现稳健。因此，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-08'}
2025-07-28 21:23:23,203 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-28 21:23:23,204 - __main__ - INFO - 📈 第5天交易动作: {'NVDA': 1.0}
2025-07-28 21:23:23,210 - __main__ - INFO - 💰 第5天净值变化: 收益率=-0.0161, 净值=$1067695.70
2025-07-28 21:23:23,210 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-28 21:23:23,211 - __main__ - INFO - ============================================================
2025-07-28 21:23:23,211 - __main__ - INFO - 第 1 周性能分析 - 联盟: frozenset({'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'})
2025-07-28 21:23:23,211 - __main__ - INFO - 周总收益率: 0.0671
2025-07-28 21:23:23,211 - __main__ - INFO - 周夏普比率: 9.6766
2025-07-28 21:23:23,211 - __main__ - INFO - 交易天数: 5
2025-07-28 21:23:23,211 - __main__ - INFO - ============================================================
2025-07-28 21:55:22,749 - __main__ - INFO - ======================================================================
2025-07-28 21:55:22,750 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-28 21:55:22,750 - __main__ - INFO - ======================================================================
2025-07-28 21:55:22,750 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-28 21:55:22,750 - __main__ - INFO - 开始模拟联盟: {'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'}
2025-07-28 21:55:22,750 - __main__ - INFO - 📅 当前周数: 2
2025-07-28 21:55:22,750 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-28 21:55:22,750 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'})
2025-07-28 21:55:22,752 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-01-08 (第2周)
2025-07-28 21:55:22,752 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-01-08, end_date=2025-01-15
2025-07-28 21:55:22,752 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-28 21:55:22,834 - __main__ - INFO - 📅 设置当前模拟周数: 2 (来自参数)
2025-07-28 21:55:22,836 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 21:56:01,842 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 21:56:01,843 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 21:56:39,101 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 21:56:39,103 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 21:56:39,103 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-08, 累计收益=0.0000, 周收益=0.0000
2025-07-28 21:56:45,567 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 21:56:45,567 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 21:56:45,567 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 21:56:45,567 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 21:56:45,567 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 21:56:45,567 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 21:56:45,567 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 21:56:45,568 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA进行了全面的多维度分析。首先，从基础面来看，FAA基本面分析显示公司财务健康度良好，估值处于合理水平。尽管NAA新闻分析由于缺乏相关新闻，无法提供具体的市场情绪变化，但根据历史数据，我们可以推断市场情绪相对稳定。技术面分析显示，TAA技术分析结果显示NVDA当前趋势可能为中性或轻微看跌，支撑位在$137.56，阻力位在$143.95。展望层分析中，BOA看涨信号强度较弱，而BeOA看跌信号强度也不强，NOA则认为市场展望保持中性。在风险评估方面，各层级信号的一致性程度较高，风险控制方面，市场环境和流动性风险处于可控范围。综合以上分析，**最终决策：买入**。我们建议在当前市场环境下，考虑到NVDA的基本面和技术面表现，以及市场情绪的相对稳定，可以适当买入NVDA。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-08'}
2025-07-28 21:56:45,568 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-28 21:56:45,568 - __main__ - INFO - 📈 第1天交易动作: {'NVDA': 1.0}
2025-07-28 21:56:45,572 - __main__ - INFO - 💰 第1天净值变化: 收益率=0.0209, 净值=$1020859.15
2025-07-28 21:56:45,574 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 21:56:45,576 - __main__ - INFO - TAA检测到无价格数据，建议跳过当前日期: 2025-01-09
2025-07-28 21:57:02,958 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 21:57:02,959 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 21:57:27,664 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 21:57:27,664 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 21:57:27,664 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-09, 累计收益=0.0209, 周收益=0.0209
2025-07-28 21:57:44,878 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 21:57:44,879 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 21:57:44,879 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 21:57:44,880 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 21:57:44,880 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 21:57:44,880 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA进行了全面的分析。首先，从基础面来看，FAA显示NVDA的财务健康度良好，得分为8.5，显示出公司的稳健性。然而，估值评估显示NVDA目前处于高估状态，这可能对股价产生压力。NAA由于缺乏相关新闻，无法提供情绪评分和市场情绪分析，因此我们将其信心度设定为0.5。技术面分析由于技术分析智能体无法进行，因此我们无法提供技术信号。展望层分析中，BOA、BeOA和NOA的分析结果均未知，因此我们无法对其信号强度进行评估。在风险评估方面，由于信号分歧较大，我们建议降低仓位或观望。综合以上分析，**最终决策：保持不变**。', 'action': 'hold', 'trading_actions': {'__HOLD__': 0.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-09'}
2025-07-28 21:57:44,880 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 0.0}
2025-07-28 21:57:44,880 - __main__ - INFO - 📈 第2天交易动作: {'__HOLD__': 0.0}
2025-07-28 21:57:44,883 - __main__ - INFO - 💰 第2天净值变化: 收益率=-0.0136, 净值=$1006953.05
2025-07-28 21:57:44,884 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 21:58:09,679 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 21:58:09,681 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 21:58:46,211 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 21:58:46,214 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 21:58:46,214 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-10, 累计收益=0.0070, 周收益=0.0070
2025-07-28 21:58:50,174 - __main__ - INFO - 🎯 找到最终决策声明: sell
2025-07-28 21:58:50,175 - __main__ - WARNING - 修正决策不一致: 声明sell vs 字段hold
2025-07-28 21:58:50,175 - __main__ - INFO - 一致性修正：hold -> sell
2025-07-28 21:58:50,175 - __main__ - INFO - 🤖 TRA 输出: action=sell, position_size=1.0
2025-07-28 21:58:50,175 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: sell (信心度: 0.00)
2025-07-28 21:58:50,175 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 21:58:50,175 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 21:58:50,175 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对NVIDIA（NVDA）进行了全面的多维度分析。基础面分析显示，尽管NVIDIA在人工智能领域的增长可能会对其股票产生积极影响，但当前其估值被评估为高估，这可能对投资者构成风险。新闻分析表明，市场情绪较为乐观，尤其是关于NVIDIA和TSMC在人工智能和半导体行业中的增长。然而，技术面分析显示，NVDA可能处于看跌趋势，价格有所下降，且技术指标如RSI和MACD可能显示超卖或看跌信号。展望层分析中，看涨信号和看跌信号存在分歧，中性展望提供了市场不确定性的视角。综合以上分析，尽管基础面和新闻分析显示积极因素，但技术面和风险控制因素表明当前市场存在下行风险。因此，**最终决策：卖出**。', 'action': 'sell', 'trading_actions': {'NVDA': -1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-10'}
2025-07-28 21:58:50,175 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': -1.0}
2025-07-28 21:58:50,175 - __main__ - INFO - 📈 第3天交易动作: {'NVDA': -1.0}
2025-07-28 21:58:50,179 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 21:59:11,632 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 21:59:11,633 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 21:59:51,694 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 21:59:51,695 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 21:59:51,695 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-13, 累计收益=0.0070, 周收益=0.0070
2025-07-28 21:59:57,347 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 21:59:57,347 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 21:59:57,347 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 21:59:57,347 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 21:59:57,347 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 21:59:57,347 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA进行了全面的多维度分析。首先，从基本面分析来看，FAA给出了财务健康度评分为8.5，表明公司财务状况良好，且在竞争中处于领先地位。然而，NAA由于缺乏相关新闻数据，无法提供情绪评分、新闻摘要和关键事件列表，因此分析信心度较低。在技术面分析中，TAA显示股票趋势为看跌，支撑位价格为约130.00美元，阻力位价格为约140.00美元。技术评分较低，为-0.5，表明短期内股票可能继续下跌。展望层分析中，BOA和BeOA的信号强度尚不明确，因为NAA的分析信心度较低。NOA认为市场展望保持中性，但TAA显示技术面看跌。综合分析，各层级信号存在分歧，尤其是基础面和技术面方向相反。因此，考虑到风险控制和信号一致性，**最终决策：保持不变**。', 'action': 'hold', 'trading_actions': {'__HOLD__': 0.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-13'}
2025-07-28 21:59:57,347 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 0.0}
2025-07-28 21:59:57,347 - __main__ - INFO - 📈 第4天交易动作: {'__HOLD__': 0.0}
2025-07-28 21:59:57,353 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 22:00:18,499 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 22:00:18,501 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 22:00:30,684 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 22:00:30,685 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 22:00:30,685 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-14, 累计收益=0.0070, 周收益=0.0070
2025-07-28 22:00:42,652 - __main__ - WARNING - 未找到明确的最终决策声明，保持action=hold
2025-07-28 22:00:42,653 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 22:00:42,653 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 22:00:42,653 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 22:00:42,653 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 22:00:42,654 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA进行了全面的多维度分析。首先，从基础面来看，FAA给出了财务健康度评分8.5，表明公司财务状况良好，但估值评估为高估，这可能意味着股票价格存在一定的下行风险。NAA由于缺乏相关新闻数据，无法评估市场情绪和重大事件影响，因此分析信心度较低。技术面分析（TAA）显示，股票价格在短期内呈现下跌趋势，支撑位在$130.05，阻力位在$135.00，技术评分-0.5，表明市场可能处于超卖状态。展望层分析中，BOA和BeOA的信号存在分歧，BOA看涨信号强度较弱，而BeOA看跌信号强度较强，NOA保持中性观点。综合考虑各层级信息，我们发现基础面和技术面存在一定的不确定性，而展望层信号分歧较大。因此，根据综合决策规则，我们得出**最终决策：观望**。', 'action': 'hold', 'trading_actions': {'__HOLD__': 0.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-14'}
2025-07-28 22:00:42,654 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 0.0}
2025-07-28 22:00:42,654 - __main__ - INFO - 📈 第5天交易动作: {'__HOLD__': 0.0}
2025-07-28 22:00:42,656 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-28 22:00:42,657 - __main__ - INFO - ============================================================
2025-07-28 22:00:42,657 - __main__ - INFO - 第 2 周性能分析 - 联盟: frozenset({'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'})
2025-07-28 22:00:42,657 - __main__ - INFO - 周总收益率: 0.0072
2025-07-28 22:00:42,657 - __main__ - INFO - 周夏普比率: 1.9659
2025-07-28 22:00:42,657 - __main__ - INFO - 交易天数: 5
2025-07-28 22:00:42,657 - __main__ - INFO - ============================================================
2025-07-28 22:32:23,591 - __main__ - INFO - ======================================================================
2025-07-28 22:32:23,591 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-28 22:32:23,591 - __main__ - INFO - ======================================================================
2025-07-28 22:32:23,591 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-28 22:32:23,591 - __main__ - INFO - 开始模拟联盟: {'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'}
2025-07-28 22:32:23,591 - __main__ - INFO - 📅 当前周数: 3
2025-07-28 22:32:23,591 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-28 22:32:23,591 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'})
2025-07-28 22:32:23,592 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-01-15 (第3周)
2025-07-28 22:32:23,592 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-01-15, end_date=2025-01-22
2025-07-28 22:32:23,592 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-28 22:32:23,669 - __main__ - INFO - 📅 设置当前模拟周数: 3 (来自参数)
2025-07-28 22:32:23,670 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 22:32:44,295 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 22:32:44,297 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 22:33:09,713 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 22:33:09,715 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 22:33:09,715 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-15, 累计收益=0.0000, 周收益=0.0000
2025-07-28 22:33:26,675 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 22:33:26,676 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 22:33:26,676 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 22:33:26,677 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 22:33:26,677 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 22:33:26,677 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 22:33:26,677 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 22:33:26,677 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA股票进行了全面的多维度分析。首先，从基础面来看，FAA基本面分析显示公司财务健康度良好，估值处于合理区间，同时NAA新闻分析未发现重大负面消息，表明公司基本面稳健。其次，TAA技术分析显示股票价格呈现上升趋势，支撑位和阻力位明确，技术指标如RSI、MACD和移动平均线均显示出多头市场特征，技术评分较高。展望层方面，BOA看涨信号强度较高，而BeOA看跌信号较弱，NOA认为上涨概率较高。在风险评估方面，各层级信号基本一致，分歧度较低，市场环境不确定性不高。综合以上分析，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-15'}
2025-07-28 22:33:26,677 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-28 22:33:26,677 - __main__ - INFO - 📈 第1天交易动作: {'NVDA': 1.0}
2025-07-28 22:33:26,680 - __main__ - INFO - 💰 第1天净值变化: 收益率=0.0489, 净值=$1048947.56
2025-07-28 22:33:26,683 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 22:33:51,190 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 22:33:51,192 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 22:34:13,312 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 22:34:13,312 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 22:34:13,312 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-16, 累计收益=0.0489, 周收益=0.0489
2025-07-28 22:34:31,219 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 22:34:31,220 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 22:34:31,220 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 22:34:31,220 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 22:34:31,220 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 22:34:31,220 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 22:34:31,220 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 22:34:31,221 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '详细的分析过程如下：\n\n### 基础面评估\n- FAA基本面分析显示NVDA财务健康度良好，估值处于高估状态，但其在竞争中的领先地位和长期增长潜力得到认可。\n- NAA新闻分析显示，尽管投资者对NVDA的长期增长潜力持乐观态度，但对其短期表现持中性态度，且无重大负面消息。\n- 综合来看，基础面评估为中性。\n\n### 技术面分析\n- TAA技术分析显示，股票价格在经历了一段时间的上涨后出现下跌，短期动量可能转向。\n- 支撑位和阻力位的分析表明，短期内可能存在下跌风险。\n- 技术指标分析需要具体数值，但根据趋势，可能支持看跌结论。\n\n### 展望层综合\n- BOA看涨信号强度较高，主要基于NVDA在AI和机器人技术方面的合作和进展。\n- BeOA看跌信号强度较低，主要基于市场情绪和宏观经济因素的影响。\n- NOA中性观点认为，市场对NVDA的看法是中性的。\n\n### 风险评估\n- 各层级信号存在分歧，基础面和技术面方向相反。\n- 市场环境不确定性较高。\n\n**最终决策：买入/卖出/保持不变**', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-16'}
2025-07-28 22:34:31,221 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-28 22:34:31,221 - __main__ - INFO - 📈 第2天交易动作: {'NVDA': 1.0}
2025-07-28 22:34:31,225 - __main__ - INFO - 💰 第2天净值变化: 收益率=-0.0076, 净值=$1040956.12
2025-07-28 22:34:31,228 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 22:34:53,833 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 22:34:53,835 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 22:35:24,177 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 22:35:24,179 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 22:35:24,179 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-17, 累计收益=0.0410, 周收益=0.0410
2025-07-28 22:35:31,528 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 22:35:31,529 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 22:35:31,529 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 22:35:31,529 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 22:35:31,529 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 22:35:31,529 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 22:35:31,529 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 22:35:31,529 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对基础面进行了评估。根据FAA的分析，NVDA的财务健康度得分为8.5，显示出良好的财务状况。同时，NAA的分析显示，市场对NVDA的AI安全工具发布持乐观态度，这可能会对其股价产生积极影响。然而，NAA也指出市场对特朗普就职典礼后的加密货币和人工智能领域持乐观态度，这可能对相关股票产生广泛影响，而不仅仅是NVDA。在技术面分析中，TAA显示NVDA的价格趋势可能呈看涨状态，支撑位在$135.00附近，阻力位在$140.00附近。展望层智能体中，BOA显示看涨信号，而BeOA显示看跌信号，NOA则认为市场趋势中性。在风险评估方面，各层级信号基本一致，分歧度较低。综合考虑以上因素，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-17'}
2025-07-28 22:35:31,529 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-28 22:35:31,529 - __main__ - INFO - 📈 第3天交易动作: {'NVDA': 1.0}
2025-07-28 22:35:31,531 - __main__ - INFO - 💰 第3天净值变化: 收益率=0.0053, 净值=$1046521.58
2025-07-28 22:35:31,532 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 22:35:31,532 - __main__ - INFO - TAA检测到无价格数据，建议跳过当前日期: 2025-01-20
2025-07-28 22:35:34,367 - __main__ - ERROR - LLM分析请求失败: Error code: 400, with error text {"contentFilter":[{"level":1,"role":"assistant"}],"error":{"code":"1301","message":"系统检测到输入或生成内容可能包含不安全或敏感内容，请您避免输入易产生敏感内容的提示语，感谢您的配合。"}}
2025-07-28 22:35:34,369 - __main__ - WARNING - 🤖 NAA: 数据验证失败，使用默认响应
2025-07-28 22:35:34,369 - __main__ - ERROR - 智能体 NAA LLM调用失败: the JSON object must be str, bytes or bytearray, not NoneType
2025-07-28 22:35:44,215 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 22:35:44,216 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 22:36:11,237 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 22:36:11,238 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 22:36:11,238 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-20, 累计收益=0.0465, 周收益=0.0465
2025-07-28 22:36:28,044 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 22:36:28,046 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 22:36:28,046 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 22:36:28,046 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 22:36:28,046 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 22:36:28,046 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '```json\n{\n  "analysis": "在本次分析中，我们首先对基础面进行了评估。根据FAA的分析，公司财务健康度得分为8.5，显示出良好的财务状况。然而，估值评估显示公司目前处于高估状态。NAA由于LLM不可用，使用了默认分析，结果显示市场情绪较为稳定，没有出现重大负面消息。因此，基础面分析显示基本面稳健，但估值较高。\n\n技术面分析由于TAA无法进行，我们无法提供具体的技术信号。然而，根据FAA的分析，公司处于领先地位，长期趋势可能看涨。\n\n展望层分析中，BOA和BeOA的分析由于LLM不可用，我们无法提供具体的信号强度分析。NOA的分析显示市场存在不确定性，建议观望。\n\n在风险评估方面，由于技术面分析无法进行，我们无法准确评估技术风险。然而，基础面和展望层分析显示信号存在分歧，因此我们降低仓位或观望。\n\n综合以上分析，**最终决策：保持不变**。由于技术面分析无法进行，且基础面和展望层分析存在分歧，我们选择保持当前持仓，等待更多信息以做出更准确的决策。",\n  "action": "hold"\n}\n```', 'action': 'hold', 'trading_actions': {'__HOLD__': 0.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-20'}
2025-07-28 22:36:28,046 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 0.0}
2025-07-28 22:36:28,046 - __main__ - INFO - 📈 第4天交易动作: {'__HOLD__': 0.0}
2025-07-28 22:36:28,052 - __main__ - INFO - 💰 第4天净值变化: 收益率=-0.0322, 净值=$1012843.38
2025-07-28 22:36:28,055 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 22:36:32,015 - __main__ - ERROR - LLM分析请求失败: Error code: 400, with error text {"contentFilter":[{"level":1,"role":"assistant"}],"error":{"code":"1301","message":"系统检测到输入或生成内容可能包含不安全或敏感内容，请您避免输入易产生敏感内容的提示语，感谢您的配合。"}}
2025-07-28 22:36:32,016 - __main__ - WARNING - 🤖 NAA: 数据验证失败，使用默认响应
2025-07-28 22:36:32,016 - __main__ - ERROR - 智能体 NAA LLM调用失败: the JSON object must be str, bytes or bytearray, not NoneType
2025-07-28 22:36:50,265 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 22:36:50,267 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 22:37:09,509 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 22:37:09,510 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 22:37:09,510 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-21, 累计收益=0.0128, 周收益=0.0128
2025-07-28 22:37:19,711 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 22:37:19,711 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 22:37:19,711 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 22:37:19,711 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 22:37:19,711 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 22:37:19,711 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 22:37:19,711 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 22:37:19,711 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '详细的分析过程，包括展望层智能体信号分析、决策理由、风险评估等，最后必须明确声明：**最终决策：买入/卖出/保持不变**\n\n分析过程如下：\n\n1. **基础面评估**：根据FAA和NAA的分析，NVDA的财务健康度良好，但估值可能处于高估状态。同时，NAA显示近期无重大负面消息，但市场情绪较为波动。\n\n2. **技术面分析**：TAA显示NVDA在2025-01-21可能处于中性或看跌状态，价格波动较大，且整体呈现下降趋势。支撑位在$135.00附近，阻力位在$150.00附近。\n\n3. **展望层综合**：BOA显示看涨信号，理由是尽管短期内有波动和下降趋势，但长期基本面分析显示积极信号。BeOA显示看跌信号，理由是技术分析结果以及财务健康度分析。NOA认为市场展望中性，市场对NVDA的展望尚未出现明确方向。\n\n4. **风险评估**：各层级信号存在分歧，技术面显示中性偏看跌，但基本面分析显示稳健。市场环境不确定性较高，流动性风险需要考虑。\n\n**最终决策：买入/卖出/保持不变**\n\n基于以上分析，考虑到技术面的中性偏看跌趋势和基础面的稳健，以及市场环境的不确定性，**最终决策：保持不变**。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-21'}
2025-07-28 22:37:19,711 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-28 22:37:19,712 - __main__ - INFO - 📈 第5天交易动作: {'NVDA': 1.0}
2025-07-28 22:37:19,714 - __main__ - INFO - 💰 第5天净值变化: 收益率=-0.0418, 净值=$970531.57
2025-07-28 22:37:19,714 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-28 22:37:19,715 - __main__ - INFO - ============================================================
2025-07-28 22:37:19,715 - __main__ - INFO - 第 3 周性能分析 - 联盟: frozenset({'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'})
2025-07-28 22:37:19,715 - __main__ - INFO - 周总收益率: -0.0273
2025-07-28 22:37:19,715 - __main__ - INFO - 周夏普比率: -2.7476
2025-07-28 22:37:19,715 - __main__ - INFO - 交易天数: 5
2025-07-28 22:37:19,715 - __main__ - INFO - ============================================================
2025-07-28 23:08:58,527 - __main__ - INFO - ======================================================================
2025-07-28 23:08:58,527 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-28 23:08:58,527 - __main__ - INFO - ======================================================================
2025-07-28 23:08:58,527 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-28 23:08:58,527 - __main__ - INFO - 开始模拟联盟: {'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'}
2025-07-28 23:08:58,527 - __main__ - INFO - 📅 当前周数: 4
2025-07-28 23:08:58,527 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-28 23:08:58,527 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'})
2025-07-28 23:08:58,528 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-01-22 (第4周)
2025-07-28 23:08:58,528 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-01-22, end_date=2025-01-29
2025-07-28 23:08:58,528 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-28 23:08:58,615 - __main__ - INFO - 📅 设置当前模拟周数: 4 (来自参数)
2025-07-28 23:08:58,617 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 23:09:28,772 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 23:09:28,773 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 23:09:57,004 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 23:09:57,004 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 23:09:57,004 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-22, 累计收益=0.0000, 周收益=0.0000
2025-07-28 23:10:02,127 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 23:10:02,127 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 23:10:02,127 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 23:10:02,127 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 23:10:02,127 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 23:10:02,127 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 23:10:02,127 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 23:10:02,127 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA进行了全面的多维度分析。首先，从基础面来看，FAA基本面分析显示NVDA财务健康度良好，估值处于合理区间。NAA新闻分析表明，NVDA受益于美国总统特朗普宣布的5000亿美元AI投资计划，市场情绪积极。然而，我们也注意到FAA指出NVDA可能存在高估的风险。技术面分析中，TAA显示NVDA股价呈现上升趋势，动量增强，但需关注RSI接近70的回调风险。展望层分析中，BOA看涨信号强于BeOA看跌信号，但NOA认为市场存在不确定性。综合考虑，各层级信号基本一致，风险控制方面，我们注意到市场环境不确定性较高，但信号分歧度较低。因此，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-22'}
2025-07-28 23:10:02,127 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-28 23:10:02,127 - __main__ - INFO - 📈 第1天交易动作: {'NVDA': 1.0}
2025-07-28 23:10:02,129 - __main__ - INFO - 💰 第1天净值变化: 收益率=0.0066, 净值=$1006616.67
2025-07-28 23:10:02,130 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 23:10:21,716 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 23:10:21,717 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 23:10:49,019 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 23:10:49,020 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 23:10:49,020 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-23, 累计收益=0.0066, 周收益=0.0066
2025-07-28 23:11:02,097 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 23:11:02,098 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 23:11:02,098 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 23:11:02,099 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 23:11:02,099 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 23:11:02,099 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 23:11:02,099 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 23:11:02,099 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对基础面进行了评估。根据FAA和NAA的分析结果，英伟达（NVIDIA）显示出良好的财务健康状况和领先的市场地位，尽管其估值目前处于高估状态。NAA指出，英伟达的供应商SK Hynix的强劲业绩和AI市场的增长可能会对英伟达产生积极影响，而苹果公司市值的下降可能会增加英伟达的市场份额。在技术面分析中，TAA显示英伟达处于上升趋势，技术指标如RSI和MACD均支持这一观点。展望层智能体中，BOA认为上涨概率较高，而BeOA和NOA则提醒存在一些不确定性因素。综合考虑各层级信号，我们得出结论：英伟达的技术面和基础面均显示出积极的信号，而展望层智能体也支持看涨的观点。因此，我们评估信号的一致性较高，风险控制措施得当，最终决策为买入。**最终决策：买入**', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-23'}
2025-07-28 23:11:02,099 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-28 23:11:02,099 - __main__ - INFO - 📈 第2天交易动作: {'NVDA': 1.0}
2025-07-28 23:11:02,101 - __main__ - INFO - 💰 第2天净值变化: 收益率=-0.0115, 净值=$995000.74
2025-07-28 23:11:02,102 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 23:11:46,964 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 23:11:46,965 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 23:12:59,029 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 23:12:59,030 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 23:12:59,030 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-24, 累计收益=-0.0050, 周收益=-0.0050
2025-07-28 23:13:13,975 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 23:13:13,976 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 23:13:13,976 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 23:13:13,976 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 23:13:13,977 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 23:13:13,977 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 23:13:13,977 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 23:13:13,977 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '详细的分析过程，包括展望层智能体信号分析、决策理由、风险评估等，最后必须明确声明：**最终决策：买入/卖出/保持不变**\n\n### 基础面评估\n\n- FAA基本面分析显示NVDA财务健康度良好，估值处于合理区间，竞争地位领先。\n- NAA新闻分析显示，虽然半导体行业下滑预测可能对行业产生负面影响，但AI领域的快速发展可能对NVDA产生积极影响。\n- 综合来看，基础面评估显示NVDA基本面稳健，无重大负面消息。\n\n### 技术面分析\n\n- TAA技术分析显示，股票价格呈现上升趋势，但近期出现回调。支撑位在$140.00附近，阻力位在$150.00附近。\n- 技术指标如RSI接近中性，MACD看涨，移动平均线显示上升趋势。\n- 技术面分析显示NVDA存在上升趋势，但需关注回调风险。\n\n### 展望层综合\n\n- BOA看涨信号强度较高，认为上涨概率较高。\n- BeOA看跌信号强度较低，认为下跌风险较小。\n- NOA中性观点认为市场存在不确定性，但上涨概率较高。\n\n### 风险评估\n\n- 各层级信号基本一致，分歧度较低。\n- 市场环境不确定性较高，需关注流动性风险。\n\n### 最终决策声明\n\n**最终决策：买入**\n\n根据以上分析，NVDA基本面稳健，技术面显示上升趋势，展望层信号偏向看涨，且风险可控。因此，建议买入NVDA。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-24'}
2025-07-28 23:13:13,977 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-28 23:13:13,977 - __main__ - INFO - 📈 第3天交易动作: {'NVDA': 1.0}
2025-07-28 23:13:13,981 - __main__ - INFO - 💰 第3天净值变化: 收益率=0.0215, 净值=$1016394.65
2025-07-28 23:13:13,983 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 23:13:50,580 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 23:13:50,581 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 23:14:22,285 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 23:14:22,285 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 23:14:22,285 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-27, 累计收益=0.0164, 周收益=0.0164
2025-07-28 23:14:45,050 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 23:14:45,050 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 23:14:45,050 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 23:14:45,050 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 23:14:45,050 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 23:14:45,050 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 23:14:45,050 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 23:14:45,051 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '详细的分析过程，包括展望层智能体信号分析、决策理由、风险评估等，最后必须明确声明：**最终决策：买入/卖出/保持不变**\n\n### 基础面评估\n\n#### FAA基本面分析\nNVDA的财务健康度评分为8.5，显示出良好的财务状况。然而，估值评估显示高估，这可能表明市场对其预期过高。\n\n#### NAA新闻分析\n新闻分析显示，NVDA受到中国DeepSeek AI崛起的影响，短期内出现波动。尽管如此，长期来看，AI市场的增长潜力仍然巨大。\n\n### 技术面分析\nTAA技术分析显示，NVDA股票在2025-01-27经历了下跌，趋势可能转向看跌。支撑位在$116.7，阻力位在$128.4。\n\n### 展望层综合\nBOA看涨信号强度较弱，而BeOA看跌信号强度较强。NOA认为市场对NVDA的展望呈现中性。\n\n### 风险评估\n各层级信号存在分歧，特别是技术面和展望层的信号。市场环境不确定性较高，流动性风险也需要考虑。\n\n### 最终决策\n基于以上分析，NVDA的财务状况良好，但市场估值较高且技术面和展望层信号存在分歧。考虑到市场环境的不确定性，**最终决策：卖出**。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-27'}
2025-07-28 23:14:45,051 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-28 23:14:45,051 - __main__ - INFO - 📈 第4天交易动作: {'NVDA': 1.0}
2025-07-28 23:14:45,054 - __main__ - INFO - 💰 第4天净值变化: 收益率=0.0027, 净值=$1019188.35
2025-07-28 23:14:45,055 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 23:15:06,770 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 23:15:06,770 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 23:15:33,551 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 23:15:33,552 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 23:15:33,552 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-28, 累计收益=0.0192, 周收益=0.0192
2025-07-28 23:15:54,687 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 23:15:54,688 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 23:15:54,688 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 23:15:54,688 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 23:15:54,688 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 23:15:54,688 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '```json\n{\n  "analysis": "在本次分析中，我们对NVDA股票进行了全面的多维度分析。首先，从基础面来看，FAA基本面分析显示NVDA财务健康度良好，估值处于合理区间。NAA新闻分析指出，尽管DeepSeek的AI模型在苹果应用商店的表现超过了OpenAI的ChatGPT，引发了对AI芯片需求的担忧，但市场情绪逐渐稳定，Nvidia的股价也有所回升。这表明基础面整体稳健，无重大负面消息。\n\n技术面分析方面，TAA技术分析显示NVDA股票呈现上升趋势，支撑位在约$116.25，阻力位在约$129.0。技术指标如RSI、MACD和移动平均线均显示出多头市场的信号，技术评分较高，分析信心度也较高。\n\n展望层分析中，BOA看涨信号强度较高，认为上涨概率和目标空间较大。BeOA看跌信号强度较低，主要担忧DeepSeek的AI模型可能降低对AI芯片的需求。NOA中性展望指出市场情绪复杂，但整体上涨概率较高。\n\n在风险评估方面，各层级信号基本一致，分歧度较低。考虑到市场环境和流动性风险，我们决定采取谨慎的态度。\n\n综上所述，**最终决策：买入**。NVDA股票在基础面、技术面和展望层均显示出积极的信号，且风险控制指标也较为理想，因此建议买入NVDA股票。",\n  "action": "buy"\n}\n```', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-28'}
2025-07-28 23:15:54,689 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-28 23:15:54,689 - __main__ - INFO - 📈 第5天交易动作: {'NVDA': 1.0}
2025-07-28 23:15:54,692 - __main__ - INFO - 💰 第5天净值变化: 收益率=0.0118, 净值=$1031171.89
2025-07-28 23:15:54,692 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-28 23:15:54,692 - __main__ - INFO - ============================================================
2025-07-28 23:15:54,692 - __main__ - INFO - 第 4 周性能分析 - 联盟: frozenset({'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'})
2025-07-28 23:15:54,692 - __main__ - INFO - 周总收益率: 0.0311
2025-07-28 23:15:54,692 - __main__ - INFO - 周夏普比率: 8.9557
2025-07-28 23:15:54,692 - __main__ - INFO - 交易天数: 5
2025-07-28 23:15:54,692 - __main__ - INFO - ============================================================
2025-07-28 23:48:44,572 - __main__ - INFO - ======================================================================
2025-07-28 23:48:44,572 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-28 23:48:44,572 - __main__ - INFO - ======================================================================
2025-07-28 23:48:44,572 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-28 23:48:44,572 - __main__ - INFO - 开始模拟联盟: {'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'}
2025-07-28 23:48:44,573 - __main__ - INFO - 📅 当前周数: 5
2025-07-28 23:48:44,573 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-28 23:48:44,573 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'})
2025-07-28 23:48:44,574 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-01-29 (第5周)
2025-07-28 23:48:44,574 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-01-29, end_date=2025-02-05
2025-07-28 23:48:44,575 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-28 23:48:44,652 - __main__ - INFO - 📅 设置当前模拟周数: 5 (来自参数)
2025-07-28 23:48:44,653 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 23:49:15,650 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 23:49:15,650 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 23:49:56,436 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 23:49:56,436 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 23:49:56,436 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-29, 累计收益=0.0000, 周收益=0.0000
2025-07-28 23:50:09,190 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 23:50:09,190 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 23:50:09,191 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 23:50:09,191 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 23:50:09,191 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 23:50:09,191 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对NVDA进行了全面的基础面评估。FAA基本面分析显示，NVDA的财务健康度良好，竞争地位领先，但估值被评估为高估。NAA新闻分析表明，尽管DeepSeek的AI模型对市场造成了一定的冲击，但NVDA和其他科技巨头仍在积极应对，同时NVDA的供应商ASML的业绩超预期，进一步提升了市场对NVDA的信心。技术面分析（TAA）显示，NVDA的股价在短期内显示出下跌趋势，技术指标如RSI和MACD可能显示超卖和卖出信号。展望层智能体（BOA、BeOA、NOA）的分析结果呈现分歧，BOA看涨信号强度较高，BeOA看跌信号强度较低，NOA认为市场展望中性。综合以上分析，我们评估各层级信号的一致性程度较低，存在分歧。因此，根据我们的综合决策规则，**最终决策：保持不变**。', 'action': 'hold', 'trading_actions': {'__HOLD__': 0.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-29'}
2025-07-28 23:50:09,191 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 0.0}
2025-07-28 23:50:09,191 - __main__ - INFO - 📈 第1天交易动作: {'__HOLD__': 0.0}
2025-07-28 23:50:09,198 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 23:50:35,928 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 23:50:35,930 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 23:51:04,801 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 23:51:04,802 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 23:51:04,802 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-30, 累计收益=0.0000, 周收益=0.0000
2025-07-28 23:51:20,356 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 23:51:20,357 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 23:51:20,357 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 23:51:20,357 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 23:51:20,357 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 23:51:20,357 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '```json\n{\n  "analysis": "在本次分析中，我们对NVDA进行了全面的多维度分析。首先，从基础面来看，FAA显示NVDA财务健康度良好，且在竞争中处于领先地位。然而，NAA指出深度搜索（DeepSeek）的崛起可能对NVDA构成挑战，同时Oracle联合创始人Larry Ellison的净资产大幅减少可能反映了投资者对DeepSeek的担忧。这表明基础面存在一定的不确定性。\n\n技术面分析显示，TAA认为NVDA当前处于震荡走势，没有明显的趋势。虽然NVDA在2025-01-30的收盘价有所上涨，但上涨幅度不大。RSI、MACD和移动平均线等技术指标也显示出中性或看涨的信号。\n\n展望层分析中，BOA认为市场将呈现看涨趋势，而BeOA则认为市场情绪较为悲观。NOA则认为市场情绪稳定，没有明显的看涨或看跌趋势。这表明展望层存在分歧。\n\n在风险评估方面，我们发现各层级信号存在一定程度的分歧，尤其是基础面和展望层。因此，我们决定降低仓位或观望。\n\n**最终决策：买入**。尽管存在一定的不确定性，但NVDA的技术面和基础面分析显示出一定的看涨信号。我们建议在控制风险的前提下，谨慎买入NVDA。"\n\n  "action": "buy"\n}\n```', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-30'}
2025-07-28 23:51:20,358 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-28 23:51:20,358 - __main__ - INFO - 📈 第2天交易动作: {'NVDA': 1.0}
2025-07-28 23:51:20,361 - __main__ - INFO - 💰 第2天净值变化: 收益率=0.0348, 净值=$1034792.53
2025-07-28 23:51:20,364 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 23:51:54,702 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 23:51:54,702 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 23:52:19,359 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 23:52:19,360 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 23:52:19,360 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-31, 累计收益=0.0348, 周收益=0.0348
2025-07-28 23:52:37,364 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 23:52:37,364 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 23:52:37,364 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 23:52:37,365 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 23:52:37,365 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 23:52:37,365 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 23:52:37,365 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 23:52:37,365 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对基础面进行了评估。根据FAA的分析，NVDA的财务健康度良好，且在竞争中处于领先地位。然而，NAA的分析显示，近期市场新闻对AI领域的关注引发了一定的波动，尤其是DeepSeek的AI模型在技术上超越了ChatGPT，对市场产生了影响。尽管如此，NAA也指出SoftBank可能领导OpenAI的融资轮，这可能对AI相关股票产生积极影响。在技术面分析中，TAA显示NVDA短期内可能处于看跌趋势，但长期趋势仍需观察。展望层智能体中，BOA认为市场对AI领域的投资前景持乐观态度，而BeOA则对市场情绪表示担忧。NOA认为市场情绪复杂，建议观望。在风险评估中，我们注意到各层级信号存在一定分歧，因此建议谨慎操作。综合以上分析，**最终决策：买入**。我们相信NVDA的长期增长潜力，且当前市场波动可能提供买入机会。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-31'}
2025-07-28 23:52:37,365 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-28 23:52:37,365 - __main__ - INFO - 📈 第3天交易动作: {'NVDA': 1.0}
2025-07-28 23:52:37,373 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 23:52:57,073 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 23:52:57,075 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 23:53:32,453 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 23:53:32,455 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 23:53:32,455 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-03, 累计收益=0.0342, 周收益=0.0342
2025-07-28 23:53:50,076 - __main__ - INFO - 🎯 找到最终决策声明: sell
2025-07-28 23:53:50,077 - __main__ - WARNING - 修正决策不一致: 声明sell vs 字段hold
2025-07-28 23:53:50,077 - __main__ - INFO - 一致性修正：hold -> sell
2025-07-28 23:53:50,077 - __main__ - INFO - 🤖 TRA 输出: action=sell, position_size=1.0
2025-07-28 23:53:50,077 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: sell (信心度: 0.00)
2025-07-28 23:53:50,077 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 23:53:50,077 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 23:53:50,077 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA进行了全面的多维度分析。首先，从基本面分析来看，FAA给出了财务健康度评分为8.5，表明公司财务状况良好，且在竞争中处于领先地位。然而，NAA由于缺乏相关新闻数据，无法提供情绪评分和新闻摘要，因此我们无法对市场情绪和重大事件影响进行评估。在技术面分析中，TAA显示NVDA在2025-02-03呈现看跌趋势，支撑位价格为$113.01，阻力位价格为$118.57。尽管RSI值低于30，表明股票可能处于超卖状态，但MACD线和移动平均线均指向下跌趋势。展望层分析中，BOA给出了看涨展望，但考虑到技术面的看跌趋势，其信号强度可能不足以支持看涨观点。BeOA和NOA的分析结果由于缺乏数据而无法提供明确的看跌或中性观点。在风险评估方面，由于信号存在分歧，我们建议降低仓位或观望。综合以上分析，**最终决策：卖出**，以规避潜在的风险。', 'action': 'sell', 'trading_actions': {'NVDA': -1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-03'}
2025-07-28 23:53:50,077 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': -1.0}
2025-07-28 23:53:50,077 - __main__ - INFO - 📈 第4天交易动作: {'NVDA': -1.0}
2025-07-28 23:53:50,084 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 23:54:15,961 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 23:54:15,963 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 23:54:49,686 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 23:54:49,687 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 23:54:49,687 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-04, 累计收益=0.0342, 周收益=0.0342
2025-07-28 23:55:06,729 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 23:55:06,729 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 23:55:06,729 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 23:55:06,729 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 23:55:06,730 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 23:55:06,730 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 23:55:06,730 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 23:55:06,730 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对基础面进行了评估。根据FAA的分析，NVDA的财务健康度评分为8.5，显示出良好的财务状况。同时，NAA由于缺乏相关新闻数据，无法提供情绪评分和新闻摘要，但分析信心度设定为0.5，表明在无新闻数据的情况下，我们需要保持谨慎。技术面分析（TAA）显示，NVDA的价格呈现上升趋势，技术评分设定为0.5，分析信心度为0.8。展望层分析中，BOA显示看涨信号，而BeOA由于缺乏情绪评分，无法提供明确的看跌信号。NOA建议观望，认为市场存在不确定性。在风险评估方面，由于信号存在分歧，我们降低仓位或观望。综合以上分析，**最终决策：买入**。我们选择买入NVDA，是基于其良好的财务状况和上升趋势，同时考虑到信号分歧，我们采取了谨慎的态度。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-04'}
2025-07-28 23:55:06,730 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-28 23:55:06,730 - __main__ - INFO - 📈 第5天交易动作: {'NVDA': 1.0}
2025-07-28 23:55:06,734 - __main__ - INFO - 💰 第5天净值变化: 收益率=-0.0255, 净值=$1007865.61
2025-07-28 23:55:06,734 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-28 23:55:06,734 - __main__ - INFO - ============================================================
2025-07-28 23:55:06,735 - __main__ - INFO - 第 5 周性能分析 - 联盟: frozenset({'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'})
2025-07-28 23:55:06,735 - __main__ - INFO - 周总收益率: 0.0088
2025-07-28 23:55:06,735 - __main__ - INFO - 周夏普比率: 1.3818
2025-07-28 23:55:06,735 - __main__ - INFO - 交易天数: 5
2025-07-28 23:55:06,735 - __main__ - INFO - ============================================================
2025-07-29 00:27:06,032 - __main__ - INFO - ======================================================================
2025-07-29 00:27:06,032 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-29 00:27:06,032 - __main__ - INFO - ======================================================================
2025-07-29 00:27:06,032 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-29 00:27:06,032 - __main__ - INFO - 开始模拟联盟: {'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'}
2025-07-29 00:27:06,032 - __main__ - INFO - 📅 当前周数: 6
2025-07-29 00:27:06,032 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-29 00:27:06,032 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'})
2025-07-29 00:27:06,033 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-02-05 (第6周)
2025-07-29 00:27:06,034 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-02-05, end_date=2025-02-12
2025-07-29 00:27:06,034 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-29 00:27:06,115 - __main__ - INFO - 📅 设置当前模拟周数: 6 (来自参数)
2025-07-29 00:27:06,116 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 00:27:32,947 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 00:27:32,948 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 00:28:04,489 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 00:28:04,489 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 00:28:04,489 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-05, 累计收益=0.0000, 周收益=0.0000
2025-07-29 00:28:08,680 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 00:28:08,680 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 00:28:08,681 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 00:28:08,681 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 00:28:08,681 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 00:28:08,681 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 00:28:08,681 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 00:28:08,681 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA进行了全面的多维度分析。首先，从基础面来看，FAA显示NVDA的财务健康度良好，估值合理，且在竞争中处于领先地位。NAA分析显示，尽管缺乏具体新闻数据，但市场情绪评分和新闻摘要均未显示重大负面消息，因此基础面评估为正面。技术面分析中，TAA指出NVDA显示出一定的看涨趋势，动量指标支持这一观点，且支撑位和阻力位分析也显示出潜在的上行空间。展望层分析中，BOA认为NVDA具有看涨潜力，而BeOA则对市场的不确定性表示担忧，NOA则保持中性观点。在风险评估方面，各层级信号基本一致，分歧度较低，因此风险可控。综合以上分析，**最终决策：买入**NVDA。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-05'}
2025-07-29 00:28:08,681 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 00:28:08,681 - __main__ - INFO - 📈 第1天交易动作: {'NVDA': 1.0}
2025-07-29 00:28:08,684 - __main__ - INFO - 💰 第1天净值变化: 收益率=-0.0269, 净值=$973056.70
2025-07-29 00:28:08,686 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 00:28:30,626 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 00:28:30,627 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 00:28:48,968 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 00:28:48,968 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 00:28:48,968 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-06, 累计收益=-0.0269, 周收益=-0.0269
2025-07-29 00:28:56,659 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 00:28:56,659 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 00:28:56,659 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 00:28:56,659 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 00:28:56,660 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 00:28:56,660 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 00:28:56,660 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 00:28:56,660 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA进行了全面的多维度分析。首先，从基础面来看，FAA基本面分析显示NVDA财务健康，估值合理，且在NAA新闻分析中未发现重大负面消息，这为买入提供了基础。其次，TAA技术分析显示NVDA价格趋势看涨，动量强劲，且存在明显的支撑位和阻力位，技术评分设定为0.5，信心度较高。展望层智能体中，BOA看涨信号强度较高，而BeOA看跌信号较弱，NOA持中性观点。在风险评估方面，各层级信号基本一致，分歧度较低，市场环境不确定性不高。综合以上分析，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-06'}
2025-07-29 00:28:56,660 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 00:28:56,660 - __main__ - INFO - 📈 第2天交易动作: {'NVDA': 1.0}
2025-07-29 00:28:56,662 - __main__ - INFO - 💰 第2天净值变化: 收益率=0.0314, 净值=$1003602.05
2025-07-29 00:28:56,663 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 00:29:17,412 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 00:29:17,413 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 00:29:47,369 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 00:29:47,370 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 00:29:47,371 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-07, 累计收益=0.0036, 周收益=0.0036
2025-07-29 00:30:04,674 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 00:30:04,675 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 00:30:04,675 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 00:30:04,675 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 00:30:04,675 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 00:30:04,675 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 00:30:04,676 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 00:30:04,676 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '详细的分析过程，包括展望层智能体信号分析、决策理由、风险评估等，最后必须明确声明：**最终决策：买入/卖出/保持不变**\n\n### 基础面评估\n\n#### FAA基本面分析\nNVDA的财务健康度得分为8.5，表明公司财务状况良好。估值合理性分析显示，NVDA的估值处于合理区间。NAA新闻分析显示，大部分新闻情绪偏向乐观，尤其是关于AI芯片和科技股的新闻，但也有一些中性情绪的新闻，如亏损报告和诉讼新闻。整体而言，NVDA的基本面表现良好，但需关注潜在的负面消息。\n\n#### 技术面分析\nTAA技术分析显示，NVDA的价格显示出上升趋势，动量指标支持这一趋势。支撑位位于$125.0，阻力位位于$130.37。技术指标如RSI和MACD需要具体数值分析，但根据趋势，可能表明市场过热和上升趋势。\n\n#### 展望层综合\nBOA看涨信号强度较高，认为NVDA的市场展望为看涨。BeOA看跌信号强度较低，认为NVDA的股价存在下跌风险，但风险较低。NOA认为市场对NVDA的展望呈现中性，尽管有积极的新闻和情绪评分，但诉讼和亏损报告等负面因素也值得关注。\n\n#### 风险评估\n各层级信号基本一致，分歧度较低。市场环境不确定性较高，但NVDA的基本面和技术面表现良好。\n\n**最终决策：买入**\n\nNVDA的基本面稳健，技术面显示上升趋势，展望层信号偏向看涨。综合考虑各层级信息，建议买入NVDA。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-07'}
2025-07-29 00:30:04,676 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 00:30:04,676 - __main__ - INFO - 📈 第3天交易动作: {'NVDA': 1.0}
2025-07-29 00:30:04,677 - __main__ - INFO - 💰 第3天净值变化: 收益率=-0.0141, 净值=$989409.98
2025-07-29 00:30:04,679 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 00:30:25,962 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 00:30:25,963 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 00:30:45,739 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 00:30:45,740 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 00:30:45,740 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-10, 累计收益=-0.0106, 周收益=-0.0106
2025-07-29 00:30:57,353 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 00:30:57,354 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 00:30:57,354 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 00:30:57,354 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 00:30:57,354 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 00:30:57,354 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 00:30:57,354 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 00:30:57,354 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对NVDA进行了全面的多维度分析。基础面分析显示，NVDA的财务健康度良好，估值处于低估状态，显示出公司的竞争优势和长期增长潜力。新闻分析由于缺乏相关新闻，无法提供具体的市场情绪变化和重大事件影响，但根据历史数据，NVDA通常在无负面新闻时表现稳定。技术面分析显示，NVDA股价呈现上升趋势，动量向上，关键支撑位和阻力位明确，技术指标如RSI和MACD均显示出多头市场特征。展望层分析中，看涨信号强于看跌信号，中性观点认为上涨概率较高。综合各层级分析，NVDA的基础面稳健，技术面看涨，展望层信号支持上涨，因此，**最终决策：买入**。在风险控制方面，由于缺乏新闻数据，我们保持谨慎态度，但技术面的积极信号和展望层的一致性使得我们倾向于买入。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-10'}
2025-07-29 00:30:57,354 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 00:30:57,354 - __main__ - INFO - 📈 第4天交易动作: {'NVDA': 1.0}
2025-07-29 00:30:57,356 - __main__ - INFO - 💰 第4天净值变化: 收益率=-0.0225, 净值=$967149.34
2025-07-29 00:30:57,357 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 00:31:22,899 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 00:31:22,899 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 00:31:45,320 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 00:31:45,321 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 00:31:45,321 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-11, 累计收益=-0.0329, 周收益=-0.0329
2025-07-29 00:32:02,739 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 00:32:02,740 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 00:32:02,740 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 00:32:02,740 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 00:32:02,740 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 00:32:02,740 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 00:32:02,740 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 00:32:02,740 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA进行了全面的多维度分析。首先，从基础面来看，FAA基本面分析显示NVDA的财务健康度良好，估值合理，且在竞争中处于领先地位。NAA新闻分析由于缺乏相关新闻，无法提供具体的市场情绪变化和重大事件影响，但分析信心度设定为0.5，表明在无新闻数据的情况下，我们需要保持谨慎。技术面分析显示，TAA技术分析指出NVDA显示出上升趋势，但动能有所减弱，技术评分设定为0.5。展望层分析中，BOA看涨信号强度较高，认为市场展望为看涨，而BeOA看跌信号强度较低，认为市场动能减弱，存在回调风险。NOA中性展望指出，由于缺乏市场情绪数据，建议保持中立。在风险评估方面，由于信号存在一定分歧，我们降低了仓位或观望的可能性。综合以上分析，**最终决策：买入**。我们相信NVDA的基本面和技术面显示出上升趋势，且展望层分析支持看涨观点，尽管存在一定风险，但整体决策倾向于买入。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-11'}
2025-07-29 00:32:02,740 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 00:32:02,740 - __main__ - INFO - 📈 第5天交易动作: {'NVDA': 1.0}
2025-07-29 00:32:02,742 - __main__ - INFO - 💰 第5天净值变化: 收益率=-0.0168, 净值=$950940.13
2025-07-29 00:32:02,742 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-29 00:32:02,742 - __main__ - INFO - ============================================================
2025-07-29 00:32:02,742 - __main__ - INFO - 第 6 周性能分析 - 联盟: frozenset({'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'})
2025-07-29 00:32:02,742 - __main__ - INFO - 周总收益率: -0.0490
2025-07-29 00:32:02,742 - __main__ - INFO - 周夏普比率: -7.4370
2025-07-29 00:32:02,742 - __main__ - INFO - 交易天数: 5
2025-07-29 00:32:02,742 - __main__ - INFO - ============================================================
2025-07-29 01:04:13,401 - __main__ - INFO - ======================================================================
2025-07-29 01:04:13,401 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-29 01:04:13,401 - __main__ - INFO - ======================================================================
2025-07-29 01:04:13,401 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-29 01:04:13,401 - __main__ - INFO - 开始模拟联盟: {'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'}
2025-07-29 01:04:13,402 - __main__ - INFO - 📅 当前周数: 7
2025-07-29 01:04:13,402 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-29 01:04:13,402 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'})
2025-07-29 01:04:13,403 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-02-12 (第7周)
2025-07-29 01:04:13,403 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-02-12, end_date=2025-02-19
2025-07-29 01:04:13,403 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-29 01:04:13,478 - __main__ - INFO - 📅 设置当前模拟周数: 7 (来自参数)
2025-07-29 01:04:13,479 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 01:04:51,831 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 01:04:51,833 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 01:05:15,218 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 01:05:15,219 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 01:05:15,219 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-12, 累计收益=0.0000, 周收益=0.0000
2025-07-29 01:05:33,731 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 01:05:33,731 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 01:05:33,731 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 01:05:33,731 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 01:05:33,731 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 01:05:33,731 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 01:05:33,731 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 01:05:33,731 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对NVDA的基础面进行了评估。根据NAA的输出，尽管有关于NVDAAI业务和行业地位的积极报道，但同时也存在DeepSeek项目可能对NVDA生态系统构成威胁的风险。然而，整体情绪评分较高，显示出市场对NVDA的乐观态度。FAA的分析显示NVDA的财务健康度良好，估值合理，且在竞争中处于领先地位。技术面分析（TAA）显示NVDA可能处于看跌状态，但考虑到支撑位和阻力位，以及动量指标有所减弱，技术评分设定为-0.5。展望层分析中，BOA认为尽管TAA显示看跌，但结合其他分析结果，NVDA的上涨概率较高。BeOA则对NVDA持中性态度，而NOA认为市场对NVDA的情绪较为乐观，但存在不确定性。在风险评估方面，我们注意到信号存在一定分歧，因此降低仓位或观望是合理的。综合以上分析，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-12'}
2025-07-29 01:05:33,732 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 01:05:33,732 - __main__ - INFO - 📈 第1天交易动作: {'NVDA': 1.0}
2025-07-29 01:05:33,735 - __main__ - INFO - 💰 第1天净值变化: 收益率=-0.0122, 净值=$987803.03
2025-07-29 01:05:33,737 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 01:05:48,221 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 01:05:48,222 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 01:06:12,743 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 01:06:12,744 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 01:06:12,744 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-13, 累计收益=-0.0122, 周收益=-0.0122
2025-07-29 01:06:23,140 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 01:06:23,140 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 01:06:23,141 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 01:06:23,141 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 01:06:23,141 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 01:06:23,141 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 01:06:23,141 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 01:06:23,141 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '详细的分析过程，包括展望层智能体信号分析、决策理由、风险评估等，最后必须明确声明：**最终决策：买入/卖出/保持不变**\n\n分析过程：\n\n1. **基础面评估**：根据FAA和NAA的分析结果，NVDA的基本面稳健，财务健康度良好，估值合理。NAA显示，人工智能和半导体行业的发展对NVDA等公司产生积极影响，同时没有出现重大负面消息。因此，基础面评估为积极。\n\n2. **技术面分析**：TAA显示NVDA处于上升趋势，支撑位价格为约130.00美元，阻力位价格为约140.00美元。技术指标如RSI、MACD和移动平均线均显示出多头市场信号。因此，技术面分析为看涨。\n\n3. **展望层综合**：BOA显示市场展望为看涨，BeOA提出了一些看跌的风险因素，但NOA认为上涨概率较高。因此，展望层综合分析倾向于看涨。\n\n4. **风险评估**：各层级信号基本一致，分歧度较低。市场环境不确定性较高，但NVDA的基本面和技术面均显示出积极的信号。\n\n最终决策：买入\n\n决策理由：NVDA的基本面稳健，技术面显示上升趋势，展望层分析倾向于看涨，且风险控制措施得当。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-13'}
2025-07-29 01:06:23,141 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 01:06:23,141 - __main__ - INFO - 📈 第2天交易动作: {'NVDA': 1.0}
2025-07-29 01:06:23,144 - __main__ - INFO - 💰 第2天净值变化: 收益率=-0.0114, 净值=$976590.91
2025-07-29 01:06:23,146 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 01:06:50,280 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 01:06:50,280 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 01:07:14,936 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 01:07:14,937 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 01:07:14,937 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-14, 累计收益=-0.0234, 周收益=-0.0234
2025-07-29 01:07:19,692 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 01:07:19,693 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 01:07:19,693 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 01:07:19,693 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 01:07:19,693 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 01:07:19,693 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 01:07:19,693 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 01:07:19,693 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对NVDA的基础面进行了评估。根据FAA和NAA的分析结果，NVDA在AI和数据中心芯片市场的地位稳固，且在自动驾驶技术方面的投资可能对其股价产生长期影响。尽管Arm进入芯片销售市场可能对NVDA构成竞争，但整体而言，NVDA的财务健康度良好，估值合理。在技术面分析中，TAA显示NVDA显示出上升趋势，动量强劲，且支撑位和阻力位明确。展望层智能体中，BOA信号强于BeOA，NOA认为上涨概率较高。然而，我们也注意到市场存在一定的不确定性，因此风险评估时考虑了信号的一致性和潜在风险。综合以上分析，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-14'}
2025-07-29 01:07:19,693 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 01:07:19,693 - __main__ - INFO - 📈 第3天交易动作: {'NVDA': 1.0}
2025-07-29 01:07:19,697 - __main__ - INFO - 💰 第3天净值变化: 收益率=0.0137, 净值=$990000.00
2025-07-29 01:07:19,699 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 01:07:19,702 - __main__ - INFO - TAA检测到无价格数据，建议跳过当前日期: 2025-02-17
2025-07-29 01:07:34,912 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 01:07:34,912 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 01:08:04,827 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 01:08:04,827 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 01:08:04,828 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-17, 累计收益=-0.0100, 周收益=-0.0100
2025-07-29 01:08:22,570 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 01:08:22,570 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 01:08:22,571 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 01:08:22,571 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 01:08:22,571 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 01:08:22,571 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 01:08:22,571 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 01:08:22,571 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在基础面评估中，FAA显示NVIDIA的财务健康度良好，且估值处于低估状态，显示出公司的竞争优势。NAA分析显示，尽管市场对NVIDIA股价存在一定质疑，但整体情绪偏向中性偏悲观，主要受xAI的Grok 3推出和Meta Platforms、Google对Broadcom投资者好消息的影响。技术面分析由于TAA无法提供具体信号，我们跳过此部分。展望层分析中，BOA看涨信号基于xAI的Grok 3推出和AI技术对市场的潜在影响，而BeOA显示市场情绪偏向中性偏悲观，NOA则认为市场情绪积极但非极度乐观。综合考虑，尽管存在一定的不确定性，但基础面和技术面的信号较为一致，且展望层中看涨信号强于看跌信号。因此，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-17'}
2025-07-29 01:08:22,571 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 01:08:22,571 - __main__ - INFO - 📈 第4天交易动作: {'NVDA': 1.0}
2025-07-29 01:08:22,573 - __main__ - INFO - 💰 第4天净值变化: 收益率=0.0308, 净值=$1020454.55
2025-07-29 01:08:22,574 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 01:09:05,818 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 01:09:05,820 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 01:09:39,380 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 01:09:39,382 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 01:09:39,382 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-18, 累计收益=0.0205, 周收益=0.0205
2025-07-29 01:09:49,131 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 01:09:49,132 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 01:09:49,132 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 01:09:49,132 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 01:09:49,132 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 01:09:49,132 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 01:09:49,132 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 01:09:49,132 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA进行了全面的多维度分析。首先，从基础面来看，FAA显示NVDA的财务健康度良好，估值合理，且在竞争地位上处于领先。NAA分析指出，尽管NVDA面临来自AMD等竞争对手的挑战，但投资者对NVDA的投资动态显示出市场对其在AI领域的关注。其次，技术面分析显示NVDA呈现出上升趋势，动量强劲，且支撑位和阻力位明确。TAA分析认为NVDA显示出看涨趋势。展望层分析中，BOA看涨信号强于BeOA看跌信号，NOA也认为上涨概率较高。然而，我们也注意到市场环境存在一定的不确定性。在风险评估方面，各层级信号基本一致，分歧度较低。综合以上分析，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-18'}
2025-07-29 01:09:49,132 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 01:09:49,132 - __main__ - INFO - 📈 第5天交易动作: {'NVDA': 1.0}
2025-07-29 01:09:49,135 - __main__ - INFO - 💰 第5天净值变化: 收益率=0.0369, 净值=$1058106.06
2025-07-29 01:09:49,135 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-29 01:09:49,136 - __main__ - INFO - ============================================================
2025-07-29 01:09:49,136 - __main__ - INFO - 第 7 周性能分析 - 联盟: frozenset({'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'})
2025-07-29 01:09:49,136 - __main__ - INFO - 周总收益率: 0.0578
2025-07-29 01:09:49,136 - __main__ - INFO - 周夏普比率: 8.8895
2025-07-29 01:09:49,136 - __main__ - INFO - 交易天数: 5
2025-07-29 01:09:49,136 - __main__ - INFO - ============================================================
2025-07-29 01:42:39,102 - __main__ - INFO - ======================================================================
2025-07-29 01:42:39,102 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-29 01:42:39,102 - __main__ - INFO - ======================================================================
2025-07-29 01:42:39,102 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-29 01:42:39,103 - __main__ - INFO - 开始模拟联盟: {'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'}
2025-07-29 01:42:39,103 - __main__ - INFO - 📅 当前周数: 8
2025-07-29 01:42:39,103 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-29 01:42:39,103 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'})
2025-07-29 01:42:39,104 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-02-19 (第8周)
2025-07-29 01:42:39,104 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-02-19, end_date=2025-02-26
2025-07-29 01:42:39,104 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-29 01:42:39,189 - __main__ - INFO - 📅 设置当前模拟周数: 8 (来自参数)
2025-07-29 01:42:39,190 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 01:43:03,464 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 01:43:03,466 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 01:43:39,458 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 01:43:39,459 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 01:43:39,460 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-19, 累计收益=0.0000, 周收益=0.0000
2025-07-29 01:43:44,633 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 01:43:44,633 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 01:43:44,633 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 01:43:44,633 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 01:43:44,633 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 01:43:44,633 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 01:43:44,633 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 01:43:44,633 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA进行了全面的多维度分析。首先，从基础面来看，FAA显示NVDA的财务健康度良好，估值合理，显示出公司的稳健性。NAA分析显示，尽管近期有关于供应链和关税的负面新闻，但整体市场情绪偏向中性，且无重大负面消息。技术面分析中，TAA显示NVDA呈现出一定的下跌趋势，但成交量的增加表明市场活跃。展望层分析中，BOA看涨信号强于BeOA看跌信号，NOA认为市场上涨概率较高。然而，风险评估显示，各层级信号存在一定分歧，尤其是基础面和技术面方向相反。因此，综合考虑，**最终决策：买入**。我们建议在买入时注意风险控制，并密切关注市场动态。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-19'}
2025-07-29 01:43:44,633 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 01:43:44,633 - __main__ - INFO - 📈 第1天交易动作: {'NVDA': 1.0}
2025-07-29 01:43:44,635 - __main__ - INFO - 💰 第1天净值变化: 收益率=0.0039, 净值=$1003937.85
2025-07-29 01:43:44,636 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 01:45:10,367 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 01:45:10,367 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 01:45:48,379 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 01:45:48,379 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 01:45:48,379 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-20, 累计收益=0.0039, 周收益=0.0039
2025-07-29 01:45:55,692 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 01:45:55,692 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 01:45:55,692 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 01:45:55,692 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 01:45:55,692 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 01:45:55,692 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 01:45:55,692 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 01:45:55,692 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对NVDA的基础面进行了评估。根据FAA基本面分析，NVDA的财务健康度评分为8.5，显示出良好的财务状况。同时，NAA新闻分析显示，NVDA推出的AI系统Evo 2以及分析师对其股价的乐观预测都对公司股价产生积极影响。然而，我们也注意到一些潜在的负面影响，如马丁·斯克雷利对微软量子计算的怀疑以及英伟达在AI股票中的出售行为。在技术面分析中，TAA技术分析显示NVDA股价呈现上升趋势，支撑位价格为136.79美元，阻力位价格为141.36美元，技术评分0.5，显示出看涨趋势。展望层分析中，BOA看涨信号强度较高，而BeOA看跌信号较弱，NOA则认为市场展望中性。在风险评估方面，各层级信号基本一致，分歧度较低。综合以上分析，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-20'}
2025-07-29 01:45:55,692 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 01:45:55,692 - __main__ - INFO - 📈 第2天交易动作: {'NVDA': 1.0}
2025-07-29 01:45:55,694 - __main__ - INFO - 💰 第2天净值变化: 收益率=-0.0021, 净值=$1001861.53
2025-07-29 01:45:55,695 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 01:46:20,775 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 01:46:20,775 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 01:46:50,700 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 01:46:50,702 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 01:46:50,702 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-21, 累计收益=0.0019, 周收益=0.0019
2025-07-29 01:47:05,940 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 01:47:05,941 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 01:47:05,941 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 01:47:05,941 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 01:47:05,941 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 01:47:05,941 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 01:47:05,941 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 01:47:05,941 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA进行了全面的多维度分析。首先，从基础面来看，FAA基本面分析显示NVDA的财务健康度良好，估值合理，显示出公司的稳健性。NAA新闻分析表明，NVDA近期发布的AI平台Signs以及CEO对DeepSeek事件的分析，对公司形象和市场情绪产生了积极影响。然而，技术面分析显示，TAA技术分析结果显示NVDA当前处于看跌趋势，支撑位约为$130.00，阻力位约为$140.00。展望层智能体中，BOA看涨信号强度较高，主要基于NVDA在AI领域的创新和行业前景。BeOA看跌信号强度较低，主要基于市场对AI芯片股票的普遍看好。NOA中性展望认为，市场对NVDA的展望呈现出一定的复杂性。综合各层级信号，我们发现基础面和技术面存在一定分歧，但整体上NVDA的基本面和技术面均显示出一定的看涨潜力。因此，我们评估信号的一致性程度较高，风险控制方面，市场环境和流动性风险需密切关注。**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-21'}
2025-07-29 01:47:05,941 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 01:47:05,941 - __main__ - INFO - 📈 第3天交易动作: {'NVDA': 1.0}
2025-07-29 01:47:05,945 - __main__ - INFO - 💰 第3天净值变化: 收益率=-0.0209, 净值=$980955.11
2025-07-29 01:47:05,947 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 01:47:33,794 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 01:47:33,795 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 01:47:59,128 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 01:47:59,129 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 01:47:59,129 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-24, 累计收益=-0.0190, 周收益=-0.0190
2025-07-29 01:48:16,344 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 01:48:16,345 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 01:48:16,345 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 01:48:16,345 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 01:48:16,345 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 01:48:16,345 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 01:48:16,345 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 01:48:16,345 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '经过对NVDA的综合分析，以下为各层级智能体的输出结果和决策理由。\n\n### 基础面评估\n- FAA基本面分析显示NVDA财务健康度良好，估值合理，竞争力领先。\n- NAA新闻分析显示，NVDA在AI领域的强劲需求推动业绩预期，西班牙国防部选择Skydio X10D无人机可能带来新的增长机会，但加密货币市场的负面情绪可能对相关股票产生负面影响。\n\n### 技术面分析\n- TAA技术分析显示NVDA股价呈现下降趋势，但存在一定的反弹可能性，RSI值低于50，MACD线在信号线下方，5日和20日移动平均线均向下。\n\n### 展望层综合\n- BOA看涨信号强度较高，主要基于NVDA在AI领域的强劲需求和西班牙国防部选择Skydio X10D无人机。\n- BeOA看跌信号强度较低，主要基于技术面和情绪面的影响。\n- NOA认为市场目前呈现出一定的中性趋势。\n\n### 风险评估\n- 各层级信号存在一定分歧，但整体上NVDA基本面和技术面均显示一定支撑。\n\n**最终决策：买入**。NVDA在AI领域的强劲需求和西班牙国防部选择Skydio X10D无人机等因素提供了看涨支撑，尽管技术面显示一定回调风险，但整体基本面和技术面均显示一定支撑。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-24'}
2025-07-29 01:48:16,346 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 01:48:16,346 - __main__ - INFO - 📈 第4天交易动作: {'NVDA': 1.0}
2025-07-29 01:48:16,349 - __main__ - INFO - 💰 第4天净值变化: 收益率=0.0035, 净值=$984391.78
2025-07-29 01:48:16,351 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 01:48:41,077 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 01:48:41,078 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 01:49:16,949 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 01:49:16,949 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 01:49:16,949 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-25, 累计收益=-0.0156, 周收益=-0.0156
2025-07-29 01:49:20,574 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 01:49:20,575 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 01:49:20,575 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 01:49:20,575 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 01:49:20,575 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 01:49:20,575 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 01:49:20,575 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 01:49:20,575 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对NVDA的基础面进行了评估。根据FAA和NAA的分析结果，NVDA的财务健康度良好，估值合理，且公司AI芯片需求增长，与DeepSeek的竞争以及即将发布的季度财报可能对股价产生积极影响。然而，我们也注意到DeepSeek的竞争和整体市场情绪可能对股价产生负面影响。在技术面分析中，TAA显示NVDA在短期内显示出看跌趋势，但市场活跃度增加。展望层智能体中，BOA看涨信号强于BeOA，但NOA认为市场展望中性。在风险评估中，我们注意到信号存在一定分歧，但整体风险可控。综合以上分析，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-25'}
2025-07-29 01:49:20,575 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 01:49:20,575 - __main__ - INFO - 📈 第5天交易动作: {'NVDA': 1.0}
2025-07-29 01:49:20,579 - __main__ - INFO - 💰 第5天净值变化: 收益率=-0.0233, 净值=$961480.63
2025-07-29 01:49:20,579 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-29 01:49:20,580 - __main__ - INFO - ============================================================
2025-07-29 01:49:20,580 - __main__ - INFO - 第 8 周性能分析 - 联盟: frozenset({'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'})
2025-07-29 01:49:20,580 - __main__ - INFO - 周总收益率: -0.0388
2025-07-29 01:49:20,580 - __main__ - INFO - 周夏普比率: -10.4453
2025-07-29 01:49:20,580 - __main__ - INFO - 交易天数: 5
2025-07-29 01:49:20,580 - __main__ - INFO - ============================================================
2025-07-29 02:20:56,763 - __main__ - INFO - ======================================================================
2025-07-29 02:20:56,763 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-29 02:20:56,763 - __main__ - INFO - ======================================================================
2025-07-29 02:20:56,763 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-29 02:20:56,763 - __main__ - INFO - 开始模拟联盟: {'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'}
2025-07-29 02:20:56,763 - __main__ - INFO - 📅 当前周数: 9
2025-07-29 02:20:56,763 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-29 02:20:56,763 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'})
2025-07-29 02:20:56,764 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-02-26 (第9周)
2025-07-29 02:20:56,764 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-02-26, end_date=2025-03-05
2025-07-29 02:20:56,764 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-29 02:20:56,837 - __main__ - INFO - 📅 设置当前模拟周数: 9 (来自参数)
2025-07-29 02:20:56,839 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 02:21:17,553 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 02:21:17,553 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 02:21:41,491 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 02:21:41,492 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 02:21:41,492 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-26, 累计收益=0.0000, 周收益=0.0000
2025-07-29 02:21:58,473 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 02:21:58,473 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 02:21:58,473 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 02:21:58,473 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 02:21:58,473 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 02:21:58,473 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 02:21:58,473 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 02:21:58,473 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA进行了全面的多维度分析。首先，从基础面来看，FAA基本面分析显示NVDA财务健康，估值合理，其竞争地位领先。NAA新闻分析表明，NVDA新一代RTX 50系列笔记本电脑的发布和第四季度财报的强劲表现受到市场积极评价，情绪评分较高。技术面分析中，TAA技术分析显示NVDA股价呈现上升趋势，支撑位和阻力位明确，技术指标如RSI、MACD和移动平均线均显示出多头市场特征。展望层分析中，BOA看涨信号强度较高，主要基于NVDA新产品发布和强劲财报，而BeOA看跌信号较弱，NOA则认为市场展望中性。综合各层级分析，信号一致性较高，风险控制方面，目前市场环境较为稳定，流动性风险可控。因此，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-26'}
2025-07-29 02:21:58,473 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 02:21:58,473 - __main__ - INFO - 📈 第1天交易动作: {'NVDA': 1.0}
2025-07-29 02:21:58,475 - __main__ - INFO - 💰 第1天净值变化: 收益率=-0.0233, 净值=$976725.58
2025-07-29 02:21:58,477 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 02:22:36,570 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 02:22:36,572 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 02:22:58,283 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 02:22:58,284 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 02:22:58,285 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-27, 累计收益=-0.0233, 周收益=-0.0233
2025-07-29 02:23:06,528 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-29 02:23:06,528 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-29 02:23:06,528 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-29 02:23:06,528 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 02:23:06,528 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 02:23:06,528 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对NVDA的基础面进行了评估。根据FAA和NAA的分析结果，NVDA的财务健康度良好，估值合理，且近期发布的强劲财报和AI技术的应用对其股价产生积极影响。然而，AMD股价下跌和特朗普的关税计划可能对其产生负面影响。在技术面分析中，TAA显示NVDA的股价在2025-02-27经历了较大的下跌，趋势方向为看跌。展望层智能体中，BOA认为市场展望为看涨，而BeOA则认为市场展望为看跌，NOA则持中性观点。在风险评估方面，我们发现各层级信号存在一定分歧，特别是在基础面和技术面之间。因此，我们决定保持观望态度，等待市场进一步明确方向。**最终决策：保持不变**', 'action': 'hold', 'trading_actions': {'__HOLD__': 0.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-27'}
2025-07-29 02:23:06,528 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 0.0}
2025-07-29 02:23:06,528 - __main__ - INFO - 📈 第2天交易动作: {'__HOLD__': 0.0}
2025-07-29 02:23:06,531 - __main__ - INFO - 💰 第2天净值变化: 收益率=0.0299, 净值=$1005964.07
2025-07-29 02:23:06,532 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 02:23:29,426 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 02:23:29,426 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 02:24:19,822 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 02:24:19,823 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 02:24:19,823 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-28, 累计收益=0.0060, 周收益=0.0060
2025-07-29 02:24:29,671 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 02:24:29,672 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 02:24:29,672 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 02:24:29,672 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 02:24:29,672 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 02:24:29,672 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 02:24:29,672 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 02:24:29,672 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA（英伟达）进行了全面的多维度分析。首先，从基础面来看，FAA基本面分析显示NVDA财务健康度良好，估值合理，显示出公司的稳健性。NAA新闻分析揭示了市场对NVDA业绩的担忧，以及美国对中国芯片出口管制的潜在影响，但整体情绪评分较低，表明市场对NVDA的长期增长前景仍持谨慎乐观态度。技术面分析中，TAA技术分析显示NVDA在2025-02-28显示出一定的上升趋势，尽管动量有所减弱，但支撑位和阻力位分析表明短期内仍有上涨空间。展望层分析中，BOA看涨信号强度较高，而BeOA看跌信号较弱，NOA则认为市场展望中性。综合各层级信号，我们认为NVDA的上涨概率较高，风险控制方面，各层级信号基本一致，分歧度较低。因此，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-28'}
2025-07-29 02:24:29,672 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 02:24:29,672 - __main__ - INFO - 📈 第3天交易动作: {'NVDA': 1.0}
2025-07-29 02:24:29,676 - __main__ - INFO - 💰 第3天净值变化: 收益率=0.0445, 净值=$1050767.33
2025-07-29 02:24:29,678 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 02:24:51,040 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 02:24:51,040 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 02:25:14,299 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 02:25:14,300 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 02:25:14,300 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-03, 累计收益=0.0508, 周收益=0.0508
2025-07-29 02:25:30,739 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-29 02:25:30,740 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-29 02:25:30,741 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-29 02:25:30,741 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 02:25:30,741 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 02:25:30,741 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA进行了全面的多维度分析。首先，从基础面来看，FAA显示NVDA的财务健康度良好，估值合理，且在竞争地位上处于领先。然而，NAA由于缺乏相关新闻数据，无法提供情绪评分和新闻摘要，但分析信心度设定为0.5，表明在无新闻数据的情况下，我们需要保持谨慎。技术面分析显示，TAA认为NVDA可能处于熊市，支撑位位于$112.28，阻力位位于$123.51，技术评分设定为-0.5，分析信心度为0.8。展望层分析中，BOA倾向于看涨，但具体分析内容未提供，BeOA和NOA的分析内容也未提供。综合以上分析，由于基础面和技术面存在分歧，且缺乏足够的新闻数据支持，我们无法得出明确的买入或卖出结论。因此，**最终决策：保持不变**。', 'action': 'hold', 'trading_actions': {'__HOLD__': 0.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-03'}
2025-07-29 02:25:30,741 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 0.0}
2025-07-29 02:25:30,741 - __main__ - INFO - 📈 第4天交易动作: {'__HOLD__': 0.0}
2025-07-29 02:25:30,744 - __main__ - INFO - 💰 第4天净值变化: 收益率=0.0343, 净值=$1086842.68
2025-07-29 02:25:30,745 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 02:25:53,188 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 02:25:53,190 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 02:26:13,877 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 02:26:13,877 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 02:26:13,878 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-04, 累计收益=0.0868, 周收益=0.0868
2025-07-29 02:26:22,389 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 02:26:22,389 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 02:26:22,389 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 02:26:22,389 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 02:26:22,389 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 02:26:22,389 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 02:26:22,389 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 02:26:22,389 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA进行了全面的多维度分析。首先，从基础面来看，FAA基本面分析显示NVDA的财务健康度良好，估值合理，且在竞争中处于领先地位。NAA新闻分析由于缺乏相关新闻，无法提供具体的市场情绪变化和重大事件影响，但根据现有信息，我们未发现任何重大负面消息。其次，技术面分析显示，NVDA的价格波动较大，但近期有反弹趋势，支撑位在$110.11，阻力位在$120.00。技术指标如RSI接近50，MACD显示看涨趋势，移动平均线交叉向上，支持短期看涨。展望层分析中，BOA看涨信号强于BeOA看跌信号，NOA认为上涨概率较高。然而，由于市场波动较大，分析信心度有所下降。综合考虑各层级信息，我们认为NVDA目前处于一个相对稳健的状态，但存在一定的不确定性。因此，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-04'}
2025-07-29 02:26:22,389 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 02:26:22,389 - __main__ - INFO - 📈 第5天交易动作: {'NVDA': 1.0}
2025-07-29 02:26:22,392 - __main__ - INFO - 💰 第5天净值变化: 收益率=-0.0622, 净值=$1019274.13
2025-07-29 02:26:22,392 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-29 02:26:22,392 - __main__ - INFO - ============================================================
2025-07-29 02:26:22,392 - __main__ - INFO - 第 9 周性能分析 - 联盟: frozenset({'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'})
2025-07-29 02:26:22,392 - __main__ - INFO - 周总收益率: 0.0234
2025-07-29 02:26:22,392 - __main__ - INFO - 周夏普比率: 1.7835
2025-07-29 02:26:22,392 - __main__ - INFO - 交易天数: 5
2025-07-29 02:26:22,392 - __main__ - INFO - ============================================================
2025-07-29 02:58:53,868 - __main__ - INFO - ======================================================================
2025-07-29 02:58:53,868 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-29 02:58:53,868 - __main__ - INFO - ======================================================================
2025-07-29 02:58:53,868 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-29 02:58:53,868 - __main__ - INFO - 开始模拟联盟: {'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'}
2025-07-29 02:58:53,868 - __main__ - INFO - 📅 当前周数: 10
2025-07-29 02:58:53,868 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-29 02:58:53,868 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'})
2025-07-29 02:58:53,869 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-03-05 (第10周)
2025-07-29 02:58:53,869 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-03-05, end_date=2025-03-12
2025-07-29 02:58:53,869 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-29 02:58:53,945 - __main__ - INFO - 📅 设置当前模拟周数: 10 (来自参数)
2025-07-29 02:58:53,946 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 02:59:05,265 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 02:59:05,266 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 02:59:34,279 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 02:59:34,279 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 02:59:34,279 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-05, 累计收益=0.0000, 周收益=0.0000
2025-07-29 02:59:43,352 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 02:59:43,352 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 02:59:43,353 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 02:59:43,353 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 02:59:43,353 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 02:59:43,353 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 02:59:43,353 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 02:59:43,353 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA股票进行了全面的多维度分析。首先，从基础面来看，FAA基本面分析显示NVDA财务健康度良好，估值合理，且在竞争地位上处于领先。NAA新闻分析由于缺乏相关新闻，情绪评分设为0，但根据市场情绪的普遍稳定，我们假设市场情绪对NVDA的影响较小。其次，TAA技术分析显示NVDA股票处于上升趋势，支撑位价格约为$115.00，阻力位价格约为$120.00，技术评分0.5，表明市场趋势看涨。展望层智能体中，BOA看涨信号强度较高，认为NVDA上涨概率和目标空间较大。BeOA看跌信号较弱，NOA则认为市场不确定性和观望理由存在。在风险评估方面，各层级信号基本一致，分歧度较低，市场环境和流动性风险在可接受范围内。综合以上分析，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-05'}
2025-07-29 02:59:43,353 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 02:59:43,353 - __main__ - INFO - 📈 第1天交易动作: {'NVDA': 1.0}
2025-07-29 02:59:43,358 - __main__ - INFO - 💰 第1天净值变化: 收益率=-0.0622, 净值=$937830.42
2025-07-29 02:59:43,360 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 03:00:11,575 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 03:00:11,575 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 03:00:30,265 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 03:00:30,266 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 03:00:30,266 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-06, 累计收益=-0.0622, 周收益=-0.0622
2025-07-29 03:00:47,784 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 03:00:47,785 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 03:00:47,785 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 03:00:47,785 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 03:00:47,786 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 03:00:47,786 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 03:00:47,786 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 03:00:47,786 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA进行了全面的多维度分析。首先，从基础面来看，FAA基本面分析显示NVDA财务健康度良好，估值合理，且在NAA新闻分析中未发现重大负面消息，表明公司基本面稳健。其次，TAA技术分析显示NVDA短期内可能存在下跌趋势，但RSI接近30，暗示市场可能存在超卖情况，而MACD线与信号线交叉向下，表明短期内可能存在下跌趋势。最后，展望层智能体中，BOA看涨信号强度较高，而BeOA看跌信号强度较低，NOA认为上涨概率较高。综合分析，虽然技术面显示短期看跌，但基本面和展望层智能体均支持看涨观点。因此，我们得出**最终决策：买入**。在风险控制方面，我们注意到信号存在一定分歧，因此将降低仓位以应对潜在风险。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-06'}
2025-07-29 03:00:47,786 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 03:00:47,786 - __main__ - INFO - 📈 第2天交易动作: {'NVDA': 1.0}
2025-07-29 03:00:47,789 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 03:00:59,478 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 03:00:59,478 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 03:01:23,387 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 03:01:23,388 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 03:01:23,388 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-07, 累计收益=-0.0624, 周收益=-0.0624
2025-07-29 03:01:42,128 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 03:01:42,129 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 03:01:42,129 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 03:01:42,129 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 03:01:42,129 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 03:01:42,129 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 03:01:42,129 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 03:01:42,130 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA进行了全面的多维度分析。首先，从基础面来看，FAA显示NVDA的财务健康度良好，估值合理，且在NAA的分析中，尽管有关于NVDA股价下跌的报道，但大部分新闻情绪偏向乐观，显示出AI和半导体行业的积极发展。这表明NVDA的基本面较为稳健。其次，TAA的技术分析显示NVDA目前处于下跌趋势，但RSI接近30，暗示可能存在超卖情况，MACD线与信号线交叉向下，表明短期内可能存在下跌趋势。然而，这些技术指标也显示出市场可能接近超卖状态。展望层智能体中，BOA看涨信号强度较高，而BeOA看跌信号强度较低，NOA则认为市场展望中性。在风险评估方面，各层级信号存在一定分歧，但整体来看，NVDA的基本面和技术面存在一定矛盾。考虑到这些因素，**最终决策：买入**。我们建议在NVDA价格接近支撑位$110.00时买入，并设置止损点以控制风险。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-07'}
2025-07-29 03:01:42,130 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 03:01:42,130 - __main__ - INFO - 📈 第3天交易动作: {'NVDA': 1.0}
2025-07-29 03:01:42,133 - __main__ - INFO - 💰 第3天净值变化: 收益率=-0.0300, 净值=$909522.85
2025-07-29 03:01:42,136 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 03:01:51,821 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 03:01:51,822 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 03:02:12,594 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 03:02:12,595 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 03:02:12,595 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-10, 累计收益=-0.0905, 周收益=-0.0905
2025-07-29 03:02:30,272 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 03:02:30,272 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 03:02:30,272 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 03:02:30,272 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 03:02:30,272 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 03:02:30,272 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 03:02:30,272 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 03:02:30,272 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '详细的分析过程，包括展望层智能体信号分析、决策理由、风险评估等，最后必须明确声明：**最终决策：买入/卖出/保持不变**\n\n分析过程：\n\n1. **基础面评估**：根据FAA的基本面分析，NVDA的财务健康度良好，估值合理，显示出公司具备较强的竞争力。NAA由于缺乏相关新闻，无法提供具体的市场情绪变化，但根据历史数据和当前市场环境，可以推测市场情绪可能偏向谨慎。综合来看，基础面评估结果为正面。\n\n2. **技术面分析**：TAA的技术分析显示，NVDA股票近期呈现下降趋势，但RSI值在30以下，表明股票可能存在反弹机会。MACD线和移动平均线均显示短期内股票趋势向下，但技术评分较低，表明存在一定的不确定性。\n\n3. **展望层综合**：BOA的看涨信号强度较高，认为尽管市场情绪偏向看跌，但NVDA存在潜在反弹机会。BeOA的看跌信号强度较低，NOA则认为市场存在不确定性，建议观望。\n\n4. **风险评估**：各层级信号存在一定分歧，但基础面和技术面评估结果较为一致，风险控制措施需加强。\n\n最终决策：**买入**。虽然存在一定的不确定性，但NVDA的财务健康和技术分析显示出潜在反弹机会，综合考虑后，决定买入。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-10'}
2025-07-29 03:02:30,272 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 03:02:30,272 - __main__ - INFO - 📈 第4天交易动作: {'NVDA': 1.0}
2025-07-29 03:02:30,275 - __main__ - INFO - 💰 第4天净值变化: 收益率=-0.0197, 净值=$891588.03
2025-07-29 03:02:30,277 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 03:02:50,792 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 03:02:50,792 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 03:03:16,352 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 03:03:16,354 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 03:03:16,354 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-11, 累计收益=-0.1084, 周收益=-0.1084
2025-07-29 03:03:31,132 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 03:03:31,132 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 03:03:31,132 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 03:03:31,132 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 03:03:31,132 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 03:03:31,132 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 03:03:31,132 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 03:03:31,133 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA进行了全面的多维度分析。首先，从基础面来看，FAA基本面分析显示NVDA财务健康度良好，估值合理，且在竞争中处于领先地位。NAA新闻分析由于缺乏相关新闻数据，无法提供具体的市场情绪和事件影响评估，但根据现有信息，未发现重大负面消息。其次，技术面分析显示TAA技术指标支持NVDA的看涨趋势，RSI值接近70，MACD线与信号线在0轴上方交叉，移动平均线均呈上升趋势。展望层分析中，BOA看涨信号强度较高，而BeOA看跌信号较弱，NOA认为上涨概率较高。在风险评估方面，各层级信号基本一致，分歧度较低，市场环境不确定性不高。综合以上分析，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-11'}
2025-07-29 03:03:31,133 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 03:03:31,133 - __main__ - INFO - 📈 第5天交易动作: {'NVDA': 1.0}
2025-07-29 03:03:31,136 - __main__ - INFO - 💰 第5天净值变化: 收益率=-0.0110, 净值=$881750.65
2025-07-29 03:03:31,137 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-29 03:03:31,137 - __main__ - INFO - ============================================================
2025-07-29 03:03:31,137 - __main__ - INFO - 第 10 周性能分析 - 联盟: frozenset({'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'})
2025-07-29 03:03:31,137 - __main__ - INFO - 周总收益率: -0.1231
2025-07-29 03:03:31,137 - __main__ - INFO - 周夏普比率: -18.5143
2025-07-29 03:03:31,137 - __main__ - INFO - 交易天数: 5
2025-07-29 03:03:31,137 - __main__ - INFO - ============================================================
2025-07-29 03:35:22,185 - __main__ - INFO - ======================================================================
2025-07-29 03:35:22,185 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-29 03:35:22,185 - __main__ - INFO - ======================================================================
2025-07-29 03:35:22,185 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-29 03:35:22,185 - __main__ - INFO - 开始模拟联盟: {'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'}
2025-07-29 03:35:22,185 - __main__ - INFO - 📅 当前周数: 11
2025-07-29 03:35:22,185 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-29 03:35:22,185 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'})
2025-07-29 03:35:22,186 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-03-12 (第11周)
2025-07-29 03:35:22,186 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-03-12, end_date=2025-03-19
2025-07-29 03:35:22,186 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-29 03:35:22,262 - __main__ - INFO - 📅 设置当前模拟周数: 11 (来自参数)
2025-07-29 03:35:22,264 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 03:35:42,956 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 03:35:42,957 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 03:36:06,406 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 03:36:06,406 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 03:36:06,407 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-12, 累计收益=0.0000, 周收益=0.0000
2025-07-29 03:36:19,650 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 03:36:19,650 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 03:36:19,650 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 03:36:19,650 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 03:36:19,650 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 03:36:19,650 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 03:36:19,650 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 03:36:19,651 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA进行了全面的多维度分析。首先，从基础面来看，FAA基本面分析显示NVDA的财务健康度良好，估值合理，显示出公司的稳健发展。同时，NAA新闻分析由于缺乏相关新闻，无法评估市场情绪，但根据当前的市场环境，未出现重大负面消息，因此可以认为基础面较为稳定。技术面分析由TAA提供，显示NVDA当前处于上升趋势，支撑位价格为$112.88，阻力位价格为$118.27，技术指标如RSI和MACD均显示出看涨信号。展望层分析中，BOA看涨信号强度较高，BeOA看跌信号较弱，NOA认为上涨概率较高。在风险评估方面，各层级信号基本一致，分歧度较低，市场环境不确定性不高。综合以上分析，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-12'}
2025-07-29 03:36:19,651 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 03:36:19,651 - __main__ - INFO - 📈 第1天交易动作: {'NVDA': 1.0}
2025-07-29 03:36:19,653 - __main__ - INFO - 💰 第1天净值变化: 收益率=-0.0110, 净值=$988966.45
2025-07-29 03:36:19,654 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 03:36:40,413 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 03:36:40,415 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 03:37:04,339 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 03:37:04,340 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 03:37:04,340 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-13, 累计收益=-0.0110, 周收益=-0.0110
2025-07-29 03:37:10,585 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 03:37:10,586 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 03:37:10,586 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 03:37:10,586 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 03:37:10,586 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 03:37:10,586 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 03:37:10,587 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 03:37:10,587 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA股票进行了全面的多维度分析。首先，从基础面评估来看，FAA基本面分析显示NVDA财务健康度良好，估值合理，竞争地位领先。NAA新闻分析由于缺乏相关新闻数据，无法提供具体的市场情绪变化和重大事件影响。其次，TAA技术分析显示NVDA股票价格呈现微弱上升趋势，但需要更多数据来确认趋势方向。展望层智能体中，BOA看涨信号强度较高，认为NVDA股票显示出微弱的上升趋势，且公司基本面稳健。BeOA看跌信号强度较低，认为NVDA股票的微弱上升趋势可能受到市场波动的影响。NOA中性展望认为市场存在不确定性，建议观望。在风险评估方面，各层级信号基本一致，分歧度较低。综合以上分析，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-13'}
2025-07-29 03:37:10,587 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 03:37:10,587 - __main__ - INFO - 📈 第2天交易动作: {'NVDA': 1.0}
2025-07-29 03:37:10,590 - __main__ - INFO - 💰 第2天净值变化: 收益率=0.0340, 净值=$1022592.51
2025-07-29 03:37:10,592 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 03:37:20,126 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 03:37:20,128 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 03:37:43,512 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 03:37:43,513 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 03:37:43,513 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-14, 累计收益=0.0226, 周收益=0.0226
2025-07-29 03:37:56,908 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 03:37:56,908 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 03:37:56,908 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 03:37:56,908 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 03:37:56,908 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 03:37:56,908 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 03:37:56,908 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 03:37:56,908 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA进行了全面的多维度分析。首先，从基础面来看，FAA基本面分析显示NVDA财务健康度良好，估值合理，且在NAA新闻分析中，尽管纳斯达克指数进入修正区域，但NVDA作为AI领域的领导者，其股价下跌但分析师仍看好其未来，表明市场情绪相对稳定。其次，TAA技术分析显示NVDA处于上升趋势，支撑位价格约为$118.00，阻力位价格约为$125.00，技术指标如RSI和MACD均支持看涨趋势。展望层智能体中，BOA看涨信号强度较高，而BeOA看跌信号较弱，NOA认为上涨概率较高。综合分析，各层级信号基本一致，风险控制方面，市场环境和流动性风险在可接受范围内。因此，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-14'}
2025-07-29 03:37:56,909 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 03:37:56,909 - __main__ - INFO - 📈 第3天交易动作: {'NVDA': 1.0}
2025-07-29 03:37:56,913 - __main__ - INFO - 💰 第3天净值变化: 收益率=-0.0196, 净值=$1002551.98
2025-07-29 03:37:56,916 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 03:38:18,304 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 03:38:18,306 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 03:38:45,697 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 03:38:45,697 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 03:38:45,697 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-17, 累计收益=0.0026, 周收益=0.0026
2025-07-29 03:39:00,393 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 03:39:00,393 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 03:39:00,393 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 03:39:00,393 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 03:39:00,393 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 03:39:00,393 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 03:39:00,393 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 03:39:00,393 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对NVDA的基础面进行了评估。根据FAA和NAA的分析结果，NVDA的财务健康度评分为8.5，显示出良好的财务状况。同时，NAA指出英伟达面临来自中国的AI新竞争对手，但整体市场情绪较为中性，略偏向看跌。在技术面分析中，TAA显示NVDA股票显示出上升趋势，支撑位价格约为$118.00，阻力位价格约为$123.00。技术指标如RSI、MACD和移动平均线均显示出看涨信号。展望层智能体中，BOA显示市场展望为看涨，BeOA显示市场情绪较为中性，NOA认为市场情绪较为积极。综合分析，各层级信号基本一致，风险控制方面，信号分歧度较低，因此，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-17'}
2025-07-29 03:39:00,394 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 03:39:00,394 - __main__ - INFO - 📈 第4天交易动作: {'NVDA': 1.0}
2025-07-29 03:39:00,396 - __main__ - INFO - 💰 第4天净值变化: 收益率=0.0310, 净值=$1033626.06
2025-07-29 03:39:00,398 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 03:39:21,858 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 03:39:21,859 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 03:39:46,155 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 03:39:46,155 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 03:39:46,155 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-18, 累计收益=0.0336, 周收益=0.0336
2025-07-29 03:40:00,771 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-29 03:40:00,771 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-29 03:40:00,771 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-29 03:40:00,771 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 03:40:00,771 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 03:40:00,771 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '```json\n{\n  "analysis": "在本次分析中，我们对NVDA进行了全面的多维度分析。首先，从基础面来看，FAA基本面分析显示NVDA的财务健康度良好，估值合理，且在AI和自动驾驶技术领域的进展得到了市场的积极评价。NAA新闻分析也表明，NVDA在AI和自动驾驶技术领域的合作和产品发布对市场情绪产生了正面影响。然而，我们也注意到市场整体估值和宏观经济因素可能对NVDA股价产生负面影响，因此需要密切关注这些因素。\n\n技术面分析显示，TAA技术分析指出NVDA股票目前处于下跌趋势，但RSI值低于50，表明市场可能存在超卖状态，存在反弹机会。MACD线与信号线交叉向下，以及移动平均线均向下，进一步支持了市场趋势偏弱的判断。\n\n展望层分析中，BOA看涨信号强度较高，主要基于NVDA在AI和自动驾驶技术领域的积极进展。BeOA看跌信号强度较低，主要基于NVDA股票的下跌趋势和看跌因素。NOA中性展望认为市场目前呈现出中性展望，NVDA的正面新闻和AI/自动驾驶技术的进展为市场提供了上涨概率，但也存在不确定性。\n\n综合以上分析，我们认为NVDA的股票在当前市场环境下存在一定的风险，但同时也存在反弹机会。因此，我们建议采取观望态度，等待市场进一步明确方向。\n\n**最终决策：保持不变**\n\n",\n  "action": "hold"\n}\n```', 'action': 'hold', 'trading_actions': {'__HOLD__': 0.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-18'}
2025-07-29 03:40:00,771 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 0.0}
2025-07-29 03:40:00,771 - __main__ - INFO - 📈 第5天交易动作: {'__HOLD__': 0.0}
2025-07-29 03:40:00,773 - __main__ - INFO - 💰 第5天净值变化: 收益率=0.0227, 净值=$1057044.21
2025-07-29 03:40:00,773 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-29 03:40:00,773 - __main__ - INFO - ============================================================
2025-07-29 03:40:00,773 - __main__ - INFO - 第 11 周性能分析 - 联盟: frozenset({'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'})
2025-07-29 03:40:00,773 - __main__ - INFO - 周总收益率: 0.0570
2025-07-29 03:40:00,773 - __main__ - INFO - 周夏普比率: 8.0631
2025-07-29 03:40:00,773 - __main__ - INFO - 交易天数: 5
2025-07-29 03:40:00,773 - __main__ - INFO - ============================================================
2025-07-29 04:11:56,448 - __main__ - INFO - ======================================================================
2025-07-29 04:11:56,448 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-29 04:11:56,448 - __main__ - INFO - ======================================================================
2025-07-29 04:11:56,448 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-29 04:11:56,448 - __main__ - INFO - 开始模拟联盟: {'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'}
2025-07-29 04:11:56,448 - __main__ - INFO - 📅 当前周数: 12
2025-07-29 04:11:56,448 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-29 04:11:56,448 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'})
2025-07-29 04:11:56,449 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-03-19 (第12周)
2025-07-29 04:11:56,449 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-03-19, end_date=2025-03-26
2025-07-29 04:11:56,449 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-29 04:11:56,530 - __main__ - INFO - 📅 设置当前模拟周数: 12 (来自参数)
2025-07-29 04:11:56,531 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 04:12:23,830 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 04:12:23,831 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 04:12:57,543 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 04:12:57,544 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 04:12:57,544 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-19, 累计收益=0.0000, 周收益=0.0000
2025-07-29 04:13:08,539 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 04:13:08,540 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 04:13:08,540 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 04:13:08,540 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 04:13:08,540 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 04:13:08,540 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 04:13:08,540 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 04:13:08,540 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '详细的分析过程，包括展望层智能体信号分析、决策理由、风险评估等，最后必须明确声明：**最终决策：买入/卖出/保持不变**\n\n### 基础面评估\n\n#### FAA基本面分析\nNVDA的财务健康度评分为8.5，显示出良好的财务状况。估值合理性分析认为NVDA的估值处于合理水平。NAA新闻分析显示，NVDA在AI和量子计算领域的进展受到市场关注，但股价在GTC会议后有所下跌，市场情绪波动。\n\n#### NAA新闻分析\n情绪评分0.6，大部分新闻对NVDA持积极态度，但部分新闻提到了市场情绪的波动和NVDA股价的下跌。新闻摘要显示NVDA发布了新的AI产品，并与多家公司建立了合作关系，但股价在GTC会议后有所下跌。\n\n#### 综合评估\n基础面分析显示NVDA基本面稳健，但市场情绪波动较大，需要谨慎对待。\n\n### 技术面分析\nTAA技术分析显示，NVDA的价格呈现上升趋势，动量指标支持这一趋势。支撑位价格为$115.43，阻力位价格为$120.45。技术评分0.5，表明市场趋势看涨。\n\n### 展望层综合\nBOA看涨信号强度较高，认为NVDA的市场展望为看涨。BeOA看跌信号强度较低，认为NVDA的下跌风险和幅度预期较小。NOA认为市场不确定性和观望理由存在，但整体倾向于看涨。\n\n### 风险评估\n各层级信号基本一致，分歧度较低。市场环境不确定性较高，需要关注市场变化。\n\n### 最终决策声明\n综合以上分析，**最终决策：买入**。NVDA的基本面稳健，技术面看涨，展望层信号倾向于看涨，但需关注市场风险。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-19'}
2025-07-29 04:13:08,540 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 04:13:08,540 - __main__ - INFO - 📈 第1天交易动作: {'NVDA': 1.0}
2025-07-29 04:13:08,543 - __main__ - INFO - 💰 第1天净值变化: 收益率=0.0443, 净值=$1044308.74
2025-07-29 04:13:08,544 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 04:13:38,990 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 04:13:38,991 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 04:14:06,750 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 04:14:06,752 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 04:14:06,752 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-20, 累计收益=0.0443, 周收益=0.0443
2025-07-29 04:14:25,660 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 04:14:25,660 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 04:14:25,660 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 04:14:25,660 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 04:14:25,660 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 04:14:25,660 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 04:14:25,660 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 04:14:25,660 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '详细的分析过程，包括展望层智能体信号分析、决策理由、风险评估等，最后必须明确声明：**最终决策：买入/卖出/保持不变**\n\n### 基础面评估\n\n- **FAA基本面分析**：根据FAA的输出，NVDA的财务健康评分较高，估值合理，竞争地位领先，长期趋势向好。这些因素表明NVDA的基本面稳健。\n- **NAA新闻分析**：NAA的分析显示，市场情绪稳定，对股价影响较小。NVDA的新闻以中性为主，部分新闻带有略微乐观的情绪，没有出现重大负面消息。\n\n### 技术面分析\n\n- **TAA技术分析**：TAA的分析结果显示，NVDA股票显示出上升趋势，技术指标如RSI、MACD和移动平均线均显示出看涨信号。\n\n### 展望层综合\n\n- **BOA看涨信号强度**：BOA的分析表明，NVDA股票显示出积极的趋势，技术分析和财务健康评分均支持看涨。\n- **BeOA看跌信号强度**：BeOA的分析显示，市场情绪稳定，对股价影响较小，没有明显的看跌信号。\n- **NOA中性观点**：NOA的分析认为，市场情绪稳定，对股价的影响较小，没有明确的上涨或下跌概率。\n\n### 风险评估\n\n- 信号一致性：各层级信号基本一致，均显示出看涨趋势。\n- 潜在风险：市场环境不确定性较高，需要关注市场动态和风险控制。\n\n### 最终决策\n\n**最终决策：买入**。NVDA的基本面稳健，技术面显示出上升趋势，展望层信号也支持看涨。尽管市场环境存在不确定性，但整体信号一致，因此建议买入NVDA。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-20'}
2025-07-29 04:14:25,660 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 04:14:25,660 - __main__ - INFO - 📈 第2天交易动作: {'NVDA': 1.0}
2025-07-29 04:14:25,664 - __main__ - INFO - 💰 第2天净值变化: 收益率=0.0010, 净值=$1045373.86
2025-07-29 04:14:25,668 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 04:15:11,569 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 04:15:11,571 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 04:15:45,587 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 04:15:45,587 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 04:15:45,587 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-21, 累计收益=0.0454, 周收益=0.0454
2025-07-29 04:15:58,184 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 04:15:58,185 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 04:15:58,185 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 04:15:58,185 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 04:15:58,185 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 04:15:58,185 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 04:15:58,185 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 04:15:58,185 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对NVDA的基础面进行了评估。根据FAA和NAA的分析结果，NVDA的财务健康度良好，估值合理，且在新闻层面没有出现重大负面消息，这表明其基本面稳健。接着，我们进行了技术面分析，TAA显示NVDA股票价格呈现上升趋势，且技术指标如RSI、MACD和移动平均线均显示出看涨信号，这进一步支持了技术面的看涨判断。展望层智能体BOA提供了看涨信号，认为市场整体趋势看涨，特别是在半导体、人工智能和量子计算领域，这与NVDA的业务领域高度相关。然而，BeOA和NOA的观点较为中性，表明市场存在一定的不确定性和观望情绪。在风险评估方面，各层级信号基本一致，分歧度较低，市场环境相对稳定。综合考虑以上因素，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-21'}
2025-07-29 04:15:58,185 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 04:15:58,185 - __main__ - INFO - 📈 第3天交易动作: {'NVDA': 1.0}
2025-07-29 04:15:58,190 - __main__ - INFO - 💰 第3天净值变化: 收益率=-0.0312, 净值=$1012710.36
2025-07-29 04:15:58,192 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 04:16:30,330 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 04:16:30,330 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 04:17:01,213 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 04:17:01,214 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 04:17:01,214 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-24, 累计收益=0.0127, 周收益=0.0127
2025-07-29 04:17:10,015 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 04:17:10,015 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 04:17:10,015 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 04:17:10,016 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 04:17:10,016 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 04:17:10,016 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 04:17:10,016 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 04:17:10,016 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA进行了全面的多维度分析。首先，从基础面来看，FAA显示NVDA的财务健康度良好，估值合理，显示出公司的稳健性。NAA的分析表明，尽管存在一些负面新闻，如芯片出口管制和股价波动，但整体市场情绪较为乐观，尤其是在AI芯片业务的增长潜力上。技术面分析（TAA）显示，NVDA股票呈现出上升趋势，关键支撑位和阻力位明确，技术指标如RSI和MACD均支持看涨趋势。展望层分析（BOA）显示，NVDA的股价有望保持稳定并呈现上升趋势，而BeOA则提醒我们关注潜在的下跌风险。NOA认为市场对NVDA的情绪较为乐观，但存在一定的不确定性。在风险评估方面，我们注意到各层级信号基本一致，分歧度较低，市场环境不确定性较高。综合以上分析，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-24'}
2025-07-29 04:17:10,016 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 04:17:10,016 - __main__ - INFO - 📈 第4天交易动作: {'NVDA': 1.0}
2025-07-29 04:17:10,018 - __main__ - INFO - 💰 第4天净值变化: 收益率=-0.1697, 净值=$840871.97
2025-07-29 04:17:10,019 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 04:17:44,873 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 04:17:44,875 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 04:18:09,992 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 04:18:09,993 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 04:18:09,993 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-25, 累计收益=-0.1591, 周收益=-0.1591
2025-07-29 04:18:24,505 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 04:18:24,505 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 04:18:24,505 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 04:18:24,505 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 04:18:24,505 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 04:18:24,505 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 04:18:24,505 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 04:18:24,505 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '经过对NVDA的多维度综合分析，得出以下结论：\n\n1. **基础面评估**：FAA显示NVDA财务健康度良好，估值合理，显示出强劲的竞争力。NAA分析显示，尽管近期有关于业绩季和股价下跌的报道，但整体新闻情绪偏向中性，且包含一些乐观情绪，如关于人工智能和半导体行业的正面预测。因此，基础面评估为正面。\n\n2. **技术面分析**：TAA分析显示NVDA股价呈现上升趋势，且技术指标如RSI、MACD和移动平均线均显示出看涨信号。这表明技术面分析也支持买入决策。\n\n3. **展望层综合**：BOA看涨信号强于BeOA看跌信号，NOA认为上涨概率较高。这表明展望层分析也倾向于看涨NVDA。\n\n4. **风险评估**：各层级信号基本一致，分歧度较低。市场环境方面，虽然存在一些不确定性，但整体风险可控。\n\n**最终决策：买入**。NVDA在基础面、技术面和展望层分析中均显示出积极的信号，且风险可控，因此建议买入。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-25'}
2025-07-29 04:18:24,506 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 04:18:24,506 - __main__ - INFO - 📈 第5天交易动作: {'NVDA': 1.0}
2025-07-29 04:18:24,508 - __main__ - INFO - 💰 第5天净值变化: 收益率=0.0893, 净值=$915927.00
2025-07-29 04:18:24,508 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-29 04:18:24,508 - __main__ - INFO - ============================================================
2025-07-29 04:18:24,508 - __main__ - INFO - 第 12 周性能分析 - 联盟: frozenset({'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'})
2025-07-29 04:18:24,508 - __main__ - INFO - 周总收益率: -0.0663
2025-07-29 04:18:24,508 - __main__ - INFO - 周夏普比率: -2.4047
2025-07-29 04:18:24,508 - __main__ - INFO - 交易天数: 5
2025-07-29 04:18:24,508 - __main__ - INFO - ============================================================
2025-07-29 04:50:15,744 - __main__ - INFO - ======================================================================
2025-07-29 04:50:15,744 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-29 04:50:15,744 - __main__ - INFO - ======================================================================
2025-07-29 04:50:15,744 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-29 04:50:15,744 - __main__ - INFO - 开始模拟联盟: {'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'}
2025-07-29 04:50:15,744 - __main__ - INFO - 📅 当前周数: 13
2025-07-29 04:50:15,744 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-29 04:50:15,744 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'})
2025-07-29 04:50:15,745 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-03-26 (第13周)
2025-07-29 04:50:15,745 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-03-26, end_date=2025-04-02
2025-07-29 04:50:15,745 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-29 04:50:15,818 - __main__ - INFO - 📅 设置当前模拟周数: 13 (来自参数)
2025-07-29 04:50:15,819 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 04:50:46,820 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 04:50:46,822 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 04:51:03,411 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 04:51:03,411 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 04:51:03,411 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-26, 累计收益=0.0000, 周收益=0.0000
2025-07-29 04:51:19,529 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 04:51:19,530 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 04:51:19,530 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 04:51:19,530 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 04:51:19,530 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 04:51:19,530 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 04:51:19,530 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 04:51:19,530 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA进行了全面的多维度分析。首先，从基础面来看，FAA显示NVDA财务健康度良好，估值合理，且在竞争地位上处于领先。NAA分析指出，尽管英伟达面临来自亚马逊的竞争，但其在人工智能领域的强大实力仍然显著，且美国对中国实体实施出口限制可能对英伟达等公司的业务产生负面影响。然而，这些负面影响并未在NAA的分析中得到突出强调。技术面分析（TAA）显示NVDA可能处于下跌趋势，支撑位价格为$112.71，阻力位价格为$118.84，技术评分较低。展望层分析中，BOA认为尽管TAA显示下跌趋势，但结合其他分析层智能体，NVDA仍有上涨潜力。BeOA则认为市场情绪较为悲观，NVDA存在下跌风险。NOA建议观望，认为市场不确定性较高。综合以上分析，尽管存在技术面和展望层的分歧，但NVDA的基础面和技术面分析显示其仍有投资价值。因此，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-26'}
2025-07-29 04:51:19,530 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 04:51:19,531 - __main__ - INFO - 📈 第1天交易动作: {'NVDA': 1.0}
2025-07-29 04:51:19,535 - __main__ - INFO - 💰 第1天净值变化: 收益率=0.0893, 净值=$1089258.57
2025-07-29 04:51:19,538 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 04:54:19,544 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 04:54:19,545 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 04:54:43,462 - __main__ - ERROR - LLM分析请求失败: Error code: 400, with error text {"contentFilter":[{"level":1,"role":"assistant"}],"error":{"code":"1301","message":"系统检测到输入或生成内容可能包含不安全或敏感内容，请您避免输入易产生敏感内容的提示语，感谢您的配合。"}}
2025-07-29 04:54:43,462 - __main__ - WARNING - 🤖 NAA: 数据验证失败，使用默认响应
2025-07-29 04:54:43,462 - __main__ - ERROR - 智能体 NAA LLM调用失败: the JSON object must be str, bytes or bytearray, not NoneType
2025-07-29 04:55:05,706 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 04:55:05,706 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 04:55:05,706 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-27, 累计收益=0.0893, 周收益=0.0893
2025-07-29 04:55:11,190 - __main__ - WARNING - 未找到明确的最终决策声明，保持action=hold
2025-07-29 04:55:11,190 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-29 04:55:11,190 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-29 04:55:11,190 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 04:55:11,190 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 04:55:11,190 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA股票进行了全面的多维度分析。首先，从基础面来看，FAA基本面分析显示NVDA的财务健康度良好，估值合理，且在竞争中处于领先地位。然而，NAA新闻分析由于调用超时，未能提供具体信息。尽管如此，我们可以根据历史数据和当前市场情绪进行推断，目前市场对NVDA的负面消息较少。技术面分析由TAA提供，显示NVDA股票短期内可能处于看跌趋势，支撑位在$110.66，阻力位在$114.45。展望层智能体中，BOA看涨信号基于市场展望和经验教训，但具体分析内容未提供。BeOA看跌信号主要基于TAA的分析结果和FAA的未提供信心度。NOA中性观点认为市场对NVDA的展望不确定。综合分析，虽然技术面显示看跌，但基础面和展望层智能体提供了一定的支持。考虑到风险控制，我们评估了信号的一致性，发现存在一定分歧。因此，我们决定保持观望态度，等待市场进一步明确方向。', 'action': 'hold', 'trading_actions': {'__HOLD__': 0.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-27'}
2025-07-29 04:55:11,190 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 0.0}
2025-07-29 04:55:11,190 - __main__ - INFO - 📈 第2天交易动作: {'__HOLD__': 0.0}
2025-07-29 04:55:11,194 - __main__ - INFO - 💰 第2天净值变化: 收益率=-0.0410, 净值=$1044587.06
2025-07-29 04:55:11,197 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 04:55:31,045 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 04:55:31,045 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 04:56:00,807 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 04:56:00,808 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 04:56:00,808 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-28, 累计收益=0.0446, 周收益=0.0446
2025-07-29 04:56:18,409 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-29 04:56:18,409 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-29 04:56:18,409 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-29 04:56:18,410 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 04:56:18,410 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 04:56:18,410 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA进行了全面的多维度分析。首先，从基础面来看，FAA显示NVDA的财务健康度良好，估值合理，且在竞争地位上处于领先。NAA的分析表明，大部分新闻对NVDA持积极态度，尤其是其产品和技术的发展，以及行业内的认可和投资。然而，TAA的技术分析显示，当前市场趋势为看跌，技术指标如RSI和MACD均显示市场处于中性偏弱状态。展望层智能体中，BOA保持看涨，认为NVDA的积极新闻和财务健康评分支持其上涨，而BeOA则看跌，认为TAA的技术分析显示的市场趋势不容忽视。NOA持中性观点，认为市场存在不确定性。综合以上分析，尽管基础面和新闻分析支持NVDA，但技术面的看跌信号和展望层智能体的分歧使得我们无法确定明确的交易方向。因此，考虑到风险控制和信号的一致性，我们决定保持观望态度。**最终决策：保持不变**', 'action': 'hold', 'trading_actions': {'__HOLD__': 0.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-28'}
2025-07-29 04:56:18,410 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 0.0}
2025-07-29 04:56:18,410 - __main__ - INFO - 📈 第3天交易动作: {'__HOLD__': 0.0}
2025-07-29 04:56:18,412 - __main__ - INFO - 💰 第3天净值变化: 收益率=0.0077, 净值=$1052609.36
2025-07-29 04:56:18,413 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 04:56:49,279 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 04:56:49,279 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 04:57:13,938 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 04:57:13,939 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 04:57:13,939 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-31, 累计收益=0.0526, 周收益=0.0526
2025-07-29 04:57:33,252 - __main__ - INFO - 🎯 找到最终决策声明: sell
2025-07-29 04:57:33,253 - __main__ - INFO - 🤖 TRA 输出: action=sell, position_size=1.0
2025-07-29 04:57:33,253 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: sell (信心度: 0.00)
2025-07-29 04:57:33,253 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 04:57:33,253 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 04:57:33,253 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '```json\n{\n  "analysis": "在本次分析中，我们对NVDA进行了全面的多维度分析。首先，从基础面来看，FAA基本面分析显示NVDA的财务健康度良好，估值合理，且在NAA新闻分析中，大部分新闻对NVDA持积极态度，尤其是苹果在AI基础设施方面的动作可能对NVDA有利，以及Cathie Wood对Mimik公司的支持显示出对NVDA的信心。然而，NVDA的IPO表现不佳，股价下跌，这也是一个需要注意的风险因素。\n\n技术面分析中，TAA技术分析显示NVDA短期内可能存在下跌趋势，支撑位价格约为$105.00，阻力位价格约为$115.00。技术评分较低，分析信心度为0.7。\n\n展望层综合分析中，BOA看涨信号强度较高，但BeOA看跌信号也不容忽视，NOA则认为市场不确定性和观望理由较强。这表明市场对NVDA的未来走势存在分歧。\n\n在风险评估方面，各层级信号存在一定分歧，特别是基础面和技术面方向相反。因此，我们需要谨慎对待这些信号。\n\n综合以上分析，**最终决策：卖出**。尽管NVDA在基础面上表现良好，但技术面的看跌趋势和市场的分歧使得我们倾向于卖出NVDA，以规避潜在的风险。\n\n",\n  "action": "sell"\n}\n```', 'action': 'sell', 'trading_actions': {'NVDA': -1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-31'}
2025-07-29 04:57:33,253 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': -1.0}
2025-07-29 04:57:33,253 - __main__ - INFO - 📈 第4天交易动作: {'NVDA': -1.0}
2025-07-29 04:57:33,257 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 04:57:54,392 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 04:57:54,393 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 04:58:31,068 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 04:58:31,070 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 04:58:31,070 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-01, 累计收益=0.0526, 周收益=0.0526
2025-07-29 04:58:34,947 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 04:58:34,947 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 04:58:34,947 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 04:58:34,947 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 04:58:34,947 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 04:58:34,947 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 04:58:34,947 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 04:58:34,947 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA进行了全面的多维度分析。首先，从基础面来看，FAA基本面分析显示NVDA财务健康度良好，估值合理，且在NAA新闻分析中未发现重大负面消息，表明公司基本面稳健。其次，TAA技术分析显示NVDA股价呈现上升趋势，且关键支撑位和阻力位明确，技术指标如RSI和MACD均显示出看涨信号。展望层方面，BOA看涨信号强于BeOA看跌信号，NOA认为市场趋势难以明确判断，但倾向于中性。在风险评估方面，各层级信号基本一致，分歧度较低，市场环境不确定性较高。综合以上分析，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-01'}
2025-07-29 04:58:34,948 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 04:58:34,948 - __main__ - INFO - 📈 第5天交易动作: {'NVDA': 1.0}
2025-07-29 04:58:34,952 - __main__ - INFO - 💰 第5天净值变化: 收益率=-0.0284, 净值=$1022715.15
2025-07-29 04:58:34,952 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-29 04:58:34,952 - __main__ - INFO - ============================================================
2025-07-29 04:58:34,952 - __main__ - INFO - 第 13 周性能分析 - 联盟: frozenset({'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'})
2025-07-29 04:58:34,952 - __main__ - INFO - 周总收益率: 0.0275
2025-07-29 04:58:34,952 - __main__ - INFO - 周夏普比率: 1.8920
2025-07-29 04:58:34,952 - __main__ - INFO - 交易天数: 5
2025-07-29 04:58:34,952 - __main__ - INFO - ============================================================
2025-07-29 05:30:24,275 - __main__ - INFO - ======================================================================
2025-07-29 05:30:24,275 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-29 05:30:24,275 - __main__ - INFO - ======================================================================
2025-07-29 05:30:24,275 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-29 05:30:24,275 - __main__ - INFO - 开始模拟联盟: {'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'}
2025-07-29 05:30:24,275 - __main__ - INFO - 📅 当前周数: 14
2025-07-29 05:30:24,275 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-29 05:30:24,275 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'})
2025-07-29 05:30:24,276 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-04-02 (第14周)
2025-07-29 05:30:24,276 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-04-02, end_date=2025-04-09
2025-07-29 05:30:24,276 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-29 05:30:24,346 - __main__ - INFO - 📅 设置当前模拟周数: 14 (来自参数)
2025-07-29 05:30:24,347 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 05:30:54,396 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 05:30:54,398 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 05:31:44,060 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 05:31:44,062 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 05:31:44,062 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-02, 累计收益=0.0000, 周收益=0.0000
2025-07-29 05:32:00,734 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 05:32:00,735 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 05:32:00,735 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 05:32:00,735 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 05:32:00,735 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 05:32:00,735 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 05:32:00,735 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 05:32:00,735 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA股票进行了全面的多维度分析。首先，从基础面评估来看，FAA基本面分析显示NVDA财务健康度良好，估值合理，且在NAA新闻分析中未发现重大负面消息，表明公司基本面稳健。其次，TAA技术分析显示NVDA股票呈现上升趋势，关键支撑位和阻力位明确，技术指标如RSI和MACD均支持看涨趋势。展望层智能体中，BOA看涨信号强于BeOA看跌信号，NOA也认为上涨概率较高。然而，由于NAA新闻分析信心度较低，我们对基础面的评估保持谨慎。在风险评估方面，各层级信号基本一致，但信心度存在差异。综合以上分析，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-02'}
2025-07-29 05:32:00,735 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 05:32:00,735 - __main__ - INFO - 📈 第1天交易动作: {'NVDA': 1.0}
2025-07-29 05:32:00,739 - __main__ - INFO - 💰 第1天净值变化: 收益率=0.0171, 净值=$1017058.12
2025-07-29 05:32:00,742 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 05:32:15,343 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 05:32:15,343 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 05:32:53,203 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 05:32:53,204 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 05:32:53,204 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-03, 累计收益=0.0171, 周收益=0.0171
2025-07-29 05:32:57,993 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 05:32:57,993 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 05:32:57,993 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 05:32:57,993 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 05:32:57,993 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 05:32:57,993 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 05:32:57,993 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 05:32:57,993 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA进行了全面的多维度分析。首先，从基础面来看，FAA显示NVDA的财务健康度良好，估值合理，且在NAA的新闻分析中，没有发现对NVDA产生重大负面影响的新闻。这表明NVDA的基本面较为稳健。其次，TAA的技术分析显示NVDA的股价呈现下跌趋势，但RSI指标显示市场可能存在反弹机会，MACD和移动平均线均显示出下跌趋势。这表明技术面存在一定的看跌信号，但也存在潜在的反弹机会。展望层智能体中，BOA的看涨信号强度高于BeOA的看跌信号，但NOA认为市场存在不确定性。综合考虑，各层级信号存在一定分歧。在风险控制方面，由于信号存在分歧，我们建议降低仓位或观望。最终决策：买入。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-03'}
2025-07-29 05:32:57,994 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 05:32:57,994 - __main__ - INFO - 📈 第2天交易动作: {'NVDA': 1.0}
2025-07-29 05:32:57,996 - __main__ - INFO - 💰 第2天净值变化: 收益率=0.0521, 净值=$1070032.57
2025-07-29 05:32:57,998 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 05:33:26,046 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 05:33:26,047 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 05:33:47,140 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 05:33:47,140 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 05:33:47,140 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-04, 累计收益=0.0700, 周收益=0.0700
2025-07-29 05:34:04,222 - __main__ - INFO - 🎯 找到最终决策声明: sell
2025-07-29 05:34:04,224 - __main__ - WARNING - 修正决策不一致: 声明sell vs 字段hold
2025-07-29 05:34:04,224 - __main__ - INFO - 一致性修正：hold -> sell
2025-07-29 05:34:04,224 - __main__ - INFO - 🤖 TRA 输出: action=sell, position_size=1.0
2025-07-29 05:34:04,224 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: sell (信心度: 0.00)
2025-07-29 05:34:04,224 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 05:34:04,224 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 05:34:04,224 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对NVDA的基础面进行了评估。根据FAA和NAA的分析结果，NVDA的财务健康度良好，估值合理，但特朗普的关税政策引发了市场担忧，导致情绪略微偏向悲观。技术面分析显示，NVDA的价格趋势呈现下跌趋势，动量较弱，技术指标如RSI、MACD和移动平均线均显示出看跌信号。展望层智能体中，BeOA的看跌信号明显强于BOA，NOA认为市场展望为中性。综合考虑各层级信号，我们发现基础面和技术面均显示出一定的下行风险，而展望层信号则较为分散。因此，根据我们的综合决策规则，我们得出以下结论：**最终决策：卖出**。', 'action': 'sell', 'trading_actions': {'NVDA': -1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-04'}
2025-07-29 05:34:04,224 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': -1.0}
2025-07-29 05:34:04,224 - __main__ - INFO - 📈 第3天交易动作: {'NVDA': -1.0}
2025-07-29 05:34:04,227 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 05:34:45,378 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 05:34:45,379 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 05:35:11,743 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 05:35:11,744 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 05:35:11,744 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-07, 累计收益=0.0700, 周收益=0.0700
2025-07-29 05:35:29,949 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 05:35:29,950 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 05:35:29,950 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 05:35:29,950 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 05:35:29,950 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 05:35:29,950 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 05:35:29,950 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 05:35:29,950 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA股票进行了全面的多维度分析。首先，从基础面来看，FAA基本面分析显示NVDA的财务健康度良好，估值合理，且在竞争中处于领先地位。然而，NAA新闻分析由于缺乏相关新闻，无法提供情绪评分和新闻摘要，因此我们无法对市场情绪进行评估。在技术面分析中，TAA显示NVDA股票短期内呈现看跌趋势，但RSI指标显示股票可能存在超卖情况，存在反弹机会。展望层智能体中，BOA看涨信号强度较高，而BeOA看跌信号强度较低，NOA则认为市场存在不确定性。在风险评估方面，由于信号存在分歧，我们建议保持谨慎态度。综合以上分析，**最终决策：买入**。我们相信NVDA股票在长期内具有增长潜力，且短期内存在反弹机会。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-07'}
2025-07-29 05:35:29,950 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 05:35:29,950 - __main__ - INFO - 📈 第4天交易动作: {'NVDA': 1.0}
2025-07-29 05:35:29,953 - __main__ - INFO - 💰 第4天净值变化: 收益率=0.0090, 净值=$1079678.50
2025-07-29 05:35:29,955 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 05:35:54,631 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 05:35:54,631 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 05:36:33,847 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 05:36:33,849 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 05:36:33,849 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-08, 累计收益=0.0797, 周收益=0.0797
2025-07-29 05:36:58,636 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 05:36:58,637 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 05:36:58,638 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 05:36:58,638 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 05:36:58,638 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 05:36:58,638 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '```json\n{\n  "analysis": "在本次分析中，我们对NVDA股票进行了全面的多维度分析。首先，从基础面来看，FAA基本面分析显示NVDA公司财务健康，估值合理，且在竞争中处于领先地位。NAA新闻分析由于缺乏相关新闻，无法提供具体的市场情绪和市场事件影响。然而，FAA的分析结果为我们提供了信心，认为NVDA的基本面稳健。\n\n技术面分析由TAA提供，显示NVDA股票近期呈现下跌趋势，技术评分较低。支撑位和阻力位的设定基于历史价格和当前趋势，但考虑到近期价格波动较大，这些水平可能随时发生变化。技术指标如RSI、MACD和移动平均线均指向看跌趋势，但分析信心度较高。\n\n展望层分析中，BOA看涨信号强度较高，认为尽管TAA显示下跌趋势，但结合其他分析，NVDA股票仍有上涨潜力。BeOA看跌信号强度较低，而NOA则认为市场存在不确定性。整体来看，展望层分析支持NVDA股票的看涨前景。\n\n在风险评估方面，我们注意到各层级信号存在一定分歧，尤其是在技术面和展望层之间。因此，我们建议降低仓位或保持观望，以应对潜在的市场风险。\n\n综合以上分析，**最终决策：买入**。我们基于NVDA稳健的基本面和看涨的展望层分析，认为当前是买入的良机。同时，我们将保持警惕，密切关注市场动态，以应对可能的风险。\n\n",\n  "action": "buy"\n}\n```', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-08'}
2025-07-29 05:36:58,638 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 05:36:58,638 - __main__ - INFO - 📈 第5天交易动作: {'NVDA': 1.0}
2025-07-29 05:36:58,642 - __main__ - INFO - 💰 第5天净值变化: 收益率=0.0287, 净值=$1110695.14
2025-07-29 05:36:58,643 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-29 05:36:58,643 - __main__ - INFO - ============================================================
2025-07-29 05:36:58,643 - __main__ - INFO - 第 14 周性能分析 - 联盟: frozenset({'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'})
2025-07-29 05:36:58,643 - __main__ - INFO - 周总收益率: 0.1069
2025-07-29 05:36:58,643 - __main__ - INFO - 周夏普比率: 18.7510
2025-07-29 05:36:58,643 - __main__ - INFO - 交易天数: 5
2025-07-29 05:36:58,643 - __main__ - INFO - ============================================================
2025-07-29 06:08:55,896 - __main__ - INFO - ======================================================================
2025-07-29 06:08:55,896 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-29 06:08:55,897 - __main__ - INFO - ======================================================================
2025-07-29 06:08:55,897 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-29 06:08:55,897 - __main__ - INFO - 开始模拟联盟: {'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'}
2025-07-29 06:08:55,897 - __main__ - INFO - 📅 当前周数: 15
2025-07-29 06:08:55,897 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-29 06:08:55,897 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'})
2025-07-29 06:08:55,899 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-04-09 (第15周)
2025-07-29 06:08:55,899 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-04-09, end_date=2025-04-16
2025-07-29 06:08:55,899 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-29 06:08:55,984 - __main__ - INFO - 📅 设置当前模拟周数: 15 (来自参数)
2025-07-29 06:08:55,985 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 06:09:22,250 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 06:09:22,251 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 06:09:47,540 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 06:09:47,541 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 06:09:47,541 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-09, 累计收益=0.0000, 周收益=0.0000
2025-07-29 06:10:02,831 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 06:10:02,831 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 06:10:02,831 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 06:10:02,831 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 06:10:02,831 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 06:10:02,831 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 06:10:02,831 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 06:10:02,831 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA进行了全面的多维度分析。首先，从基础面来看，FAA基本面分析显示NVDA财务健康度良好，估值合理，且在NAA新闻分析中未发现重大负面消息，表明公司基本面稳健。其次，TAA技术分析显示NVDA处于上升趋势，且技术指标如RSI、MACD和移动平均线均显示出看涨信号，支撑位和阻力位明确。展望层方面，BOA看涨信号强于BeOA，NOA认为上涨概率较高，但市场不确定性仍存在。在风险评估方面，各层级信号基本一致，分歧度较低，市场环境相对稳定。综合以上分析，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-09'}
2025-07-29 06:10:02,831 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 06:10:02,831 - __main__ - INFO - 📈 第1天交易动作: {'NVDA': 1.0}
2025-07-29 06:10:02,836 - __main__ - INFO - 💰 第1天净值变化: 收益率=-0.0058, 净值=$994235.23
2025-07-29 06:10:02,839 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 06:10:30,283 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 06:10:30,283 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 06:11:01,664 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 06:11:01,665 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 06:11:01,665 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-10, 累计收益=-0.0058, 周收益=-0.0058
2025-07-29 06:11:10,386 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 06:11:10,386 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 06:11:10,386 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 06:11:10,387 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 06:11:10,387 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 06:11:10,387 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 06:11:10,387 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 06:11:10,387 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们对NVDA进行了全面的多维度分析。首先，从基础面来看，FAA给出了财务健康度和估值合理性的正面评估，同时NAA没有发现重大负面新闻，这表明公司基本面稳健。其次，技术面分析显示TAA智能体认为股票价格呈现上升趋势，并且提供了关键的支撑位和阻力位，这为交易提供了明确的入场和风险控制点。展望层方面，BOA给出了看涨信号，而BeOA则持谨慎态度，NOA认为市场存在不确定性。在风险评估方面，我们注意到信号的一致性程度较高，但市场环境的不确定性仍然存在。综合以上分析，我们认为NVDA目前处于一个买入的良机。**最终决策：买入**', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-10'}
2025-07-29 06:11:10,387 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 06:11:10,387 - __main__ - INFO - 📈 第2天交易动作: {'NVDA': 1.0}
2025-07-29 06:11:10,393 - __main__ - INFO - 💰 第2天净值变化: 收益率=-0.0125, 净值=$981807.29
2025-07-29 06:11:10,395 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 06:11:39,422 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 06:11:39,424 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 06:12:04,899 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 06:12:04,900 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 06:12:04,900 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-11, 累计收益=-0.0182, 周收益=-0.0182
2025-07-29 06:12:15,467 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 06:12:15,468 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 06:12:15,468 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 06:12:15,468 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 06:12:15,469 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 06:12:15,469 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 06:12:15,469 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 06:12:15,469 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在基础面评估中，FAA显示NVDA财务健康度良好，估值合理，同时NAA分析显示市场对NVDA的看法较为积极，尽管存在一些负面新闻，但整体情绪稳定。技术面分析中，TAA显示NVDA股价呈现上升趋势，技术指标如RSI和MACD均支持看涨趋势。展望层分析中，BOA看涨信号强于BeOA，NOA认为市场上涨概率较高。综合各层级分析，信号一致性较高，风险控制方面，市场环境相对稳定，流动性风险可控。因此，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-11'}
2025-07-29 06:12:15,470 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 06:12:15,470 - __main__ - INFO - 📈 第3天交易动作: {'NVDA': 1.0}
2025-07-29 06:12:15,472 - __main__ - INFO - 💰 第3天净值变化: 收益率=0.0316, 净值=$1012877.14
2025-07-29 06:12:15,474 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 06:12:38,746 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 06:12:38,748 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 06:13:15,268 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 06:13:15,269 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 06:13:15,269 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-14, 累计收益=0.0129, 周收益=0.0129
2025-07-29 06:13:30,852 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 06:13:30,852 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 06:13:30,852 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 06:13:30,853 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 06:13:30,853 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 06:13:30,853 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 06:13:30,853 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 06:13:30,853 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '经过对NVDA股票的多维度综合分析，得出以下结论：\n\n1. **基础面评估**：FAA基本面分析显示NVDA财务健康度良好，估值合理，且在NAA新闻分析中，NVDA作为半导体和人工智能领域的领先企业，受到广泛关注，其宣布在德克萨斯州大规模生产AI超级计算机，并计划在未来四年内投资5000亿美元建设AI基础设施，这些积极因素对市场情绪产生了积极影响。因此，基础面评估结果为正面。\n\n2. **技术面分析**：TAA技术分析显示NVDA股票趋势看涨，支撑位价格为$109.07，阻力位价格为$114.29，技术指标如RSI、MACD和移动平均线均显示市场趋势看涨。因此，技术面评估结果为看涨。\n\n3. **展望层综合**：BOA看涨信号强度较高，BeOA看跌信号强度较弱，NOA认为上涨概率较高。因此，展望层综合评估结果为看涨。\n\n4. **风险评估**：各层级信号基本一致，分歧度较低，市场环境相对稳定，流动性风险可控。\n\n**最终决策：买入**\n\n', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-14'}
2025-07-29 06:13:30,853 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 06:13:30,853 - __main__ - INFO - 📈 第4天交易动作: {'NVDA': 1.0}
2025-07-29 06:13:30,857 - __main__ - INFO - 💰 第4天净值变化: 收益率=0.0263, 净值=$1039529.83
2025-07-29 06:13:30,860 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 06:13:56,402 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 06:13:56,403 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 06:14:42,184 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 06:14:42,185 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 06:14:42,185 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-15, 累计收益=0.0395, 周收益=0.0395
2025-07-29 06:15:03,087 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 06:15:03,087 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 06:15:03,087 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 06:15:03,087 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 06:15:03,087 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 06:15:03,087 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 06:15:03,087 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 06:15:03,087 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '详细的分析过程，包括展望层智能体信号分析、决策理由、风险评估等，最后必须明确声明：**最终决策：买入/卖出/保持不变**\n\n### 基础面评估\n\n- FAA基本面分析显示NVDA财务健康度良好，估值合理，公司处于行业领先地位。\n- NAA新闻分析显示，美国总统特朗普对技术产品的关税暂停，对NVDA等科技股有利，但美国商务部对芯片和电子产品进口的调查可能引发新的关税，存在一定风险。\n- 综合来看，基础面显示NVDA基本面稳健，但存在潜在风险。\n\n### 技术面分析\n\n- TAA技术分析显示NVDA价格呈现上升趋势，支撑位价格为$110.5，阻力位价格为$115.74，技术指标RSI、MACD和移动平均线均显示出市场趋势看涨。\n- 技术面分析支持NVDA的看涨趋势。\n\n### 展望层综合\n\n- BOA看涨信号强度较高，认为市场上涨概率较高。\n- BeOA看跌信号强度较低，认为下跌风险较小。\n- NOA认为市场情绪较为积极，但存在一定的不确定性。\n- 展望层分析支持NVDA的看涨趋势。\n\n### 风险评估\n\n- 各层级信号基本一致，分歧度较低。\n- 市场环境不确定性较高，但NVDA基本面和技术面均支持看涨。\n\n### 最终决策\n\n**最终决策：买入**\n\nNVDA基本面稳健，技术面看涨，展望层分析支持看涨趋势，且风险可控。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-15'}
2025-07-29 06:15:03,087 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 06:15:03,087 - __main__ - INFO - 📈 第5天交易动作: {'NVDA': 1.0}
2025-07-29 06:15:03,089 - __main__ - INFO - 💰 第5天净值变化: 收益率=0.0040, 净值=$1043647.53
2025-07-29 06:15:03,089 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-29 06:15:03,089 - __main__ - INFO - ============================================================
2025-07-29 06:15:03,089 - __main__ - INFO - 第 15 周性能分析 - 联盟: frozenset({'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'})
2025-07-29 06:15:03,089 - __main__ - INFO - 周总收益率: 0.0437
2025-07-29 06:15:03,089 - __main__ - INFO - 周夏普比率: 7.8827
2025-07-29 06:15:03,089 - __main__ - INFO - 交易天数: 5
2025-07-29 06:15:03,089 - __main__ - INFO - ============================================================
2025-07-29 06:47:20,866 - __main__ - INFO - ======================================================================
2025-07-29 06:47:20,866 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-29 06:47:20,866 - __main__ - INFO - ======================================================================
2025-07-29 06:47:20,866 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-29 06:47:20,866 - __main__ - INFO - 开始模拟联盟: {'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'}
2025-07-29 06:47:20,866 - __main__ - INFO - 📅 当前周数: 16
2025-07-29 06:47:20,866 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-29 06:47:20,866 - __main__ - INFO - 开始联盟交易模拟: frozenset({'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'})
2025-07-29 06:47:20,867 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-04-16 (第16周)
2025-07-29 06:47:20,867 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-04-16, end_date=2025-04-23
2025-07-29 06:47:20,867 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-29 06:47:20,938 - __main__ - INFO - 📅 设置当前模拟周数: 16 (来自参数)
2025-07-29 06:47:20,939 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 06:47:42,422 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 06:47:42,424 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 06:48:20,478 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 06:48:20,480 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 06:48:20,480 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-16, 累计收益=0.0000, 周收益=0.0000
2025-07-29 06:48:39,586 - __main__ - INFO - 🎯 找到最终决策声明: sell
2025-07-29 06:48:39,587 - __main__ - WARNING - 修正决策不一致: 声明sell vs 字段hold
2025-07-29 06:48:39,587 - __main__ - INFO - 一致性修正：hold -> sell
2025-07-29 06:48:39,587 - __main__ - INFO - 🤖 TRA 输出: action=sell, position_size=1.0
2025-07-29 06:48:39,587 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: sell (信心度: 0.00)
2025-07-29 06:48:39,587 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 06:48:39,587 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 06:48:39,587 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '经过对NVDA的多维度综合分析，以下是我的决策过程和理由：\n\n1. **基础面评估**：FAA显示NVDA财务健康度良好，估值合理，但NAA指出NVDA面临芯片出口限制和行业压力，这可能对公司的基本面产生负面影响。因此，基础面评估结果为中性。\n\n2. **技术面分析**：TAA显示NVDA股票价格呈现下降趋势，动量较弱，技术指标如RSI、MACD和移动平均线均显示看跌趋势。这表明技术面分析结果为看跌。\n\n3. **展望层综合**：BOA提出看涨展望，但BeOA和NOA均提出看跌展望，市场情绪较为复杂。因此，展望层综合分析结果为看跌。\n\n4. **风险评估**：各层级信号存在分歧，基础面和技术面方向相反，市场环境不确定性较高。因此，风险评估结果为风险较高。\n\n基于以上分析，**最终决策：卖出**。NVDA面临的基本面和技术面风险较高，且市场情绪复杂，建议卖出以规避潜在风险。', 'action': 'sell', 'trading_actions': {'NVDA': -1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-16'}
2025-07-29 06:48:39,587 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': -1.0}
2025-07-29 06:48:39,587 - __main__ - INFO - 📈 第1天交易动作: {'NVDA': -1.0}
2025-07-29 06:48:39,591 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 06:49:14,790 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 06:49:14,792 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 06:49:44,229 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 06:49:44,229 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 06:49:44,229 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-17, 累计收益=0.0000, 周收益=0.0000
2025-07-29 06:50:06,236 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 06:50:06,237 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 06:50:06,238 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 06:50:06,238 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 06:50:06,238 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 06:50:06,238 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 06:50:06,238 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 06:50:06,238 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '详细的分析过程，包括展望层智能体信号分析、决策理由、风险评估等，最后必须明确声明：**最终决策：买入/卖出/保持不变**\n\n分析过程如下：\n\n1. **基础面评估**：根据FAA和NAA的分析结果，NVDA的财务健康度良好，估值合理。尽管存在一些负面新闻，如美国对中国AI芯片的出口限制和股价下跌，但也有积极因素，如TSMC第一季度利润超出预期和NVDA与Nissan合作开发自动驾驶技术。总体来看，基础面评估为中性。\n\n2. **技术面分析**：TAA显示NVDA短期内呈现看跌趋势，但RSI值在30以下，表明可能存在反弹机会。MACD线在信号线下方，暗示短期内可能继续下跌。5日和10日移动平均线均向下，支持看跌趋势。\n\n3. **展望层综合**：BOA看涨信号强度较弱，而BeOA看跌信号强度较强。NOA认为市场情绪较为中性，但建议观望。\n\n4. **风险评估**：各层级信号存在分歧，风险控制尤为重要。基础面和技术面方向相反，市场环境不确定性较高。\n\n**最终决策**：考虑到基础面和技术面的分歧，以及市场环境的不确定性，建议保持观望。\n\n', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-17'}
2025-07-29 06:50:06,238 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 06:50:06,238 - __main__ - INFO - 📈 第2天交易动作: {'NVDA': 1.0}
2025-07-29 06:50:06,241 - __main__ - INFO - 💰 第2天净值变化: 收益率=0.0063, 净值=$1006320.48
2025-07-29 06:50:06,244 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 06:50:06,245 - __main__ - INFO - TAA检测到无价格数据，建议跳过当前日期: 2025-04-18
2025-07-29 06:50:42,841 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 06:50:42,843 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 06:51:16,859 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 06:51:16,860 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 06:51:16,860 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-18, 累计收益=0.0063, 周收益=0.0063
2025-07-29 06:51:22,687 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 06:51:22,688 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 06:51:22,688 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 06:51:22,688 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 06:51:22,688 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 06:51:22,688 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '```json\n{\n  "analysis": "在本次分析中，我们对NVDA进行了全面的多维度分析。首先，从基础面来看，FAA基本面分析显示NVDA的财务健康度良好，估值合理，显示出公司具有较强的竞争力。NAA新闻分析显示，大部分新闻对NVDA持积极态度，尤其是关于AI领域的扩张和公司股票评级提升的消息，但也有一些中性或略微悲观的消息，如关于贸易战和芯片出口限制的报道。这表明基础面整体稳健，但存在一定的风险。\n\n技术面分析方面，由于TAA技术分析无法进行，我们无法直接评估趋势方向、支撑阻力、动量指标等。然而，根据市场情绪和新闻分析，我们可以推测技术面可能呈现上升趋势。\n\n展望层综合分析中，BOA看涨信号强度较高，尽管存在一些看跌因素，但整体市场对NVDA的展望为看涨。NOA中性观点表明市场存在一定的不确定性，但整体上涨概率较高。\n\n在风险评估方面，我们注意到贸易战和芯片出口限制可能对NVDA的战略位置构成威胁，这增加了风险。然而，由于NVDA在AI领域的领先地位和财务健康度，我们认为这些风险是可控的。\n\n综合以上分析，我们认为NVDA的买入条件基本满足。因此，**最终决策：买入**。\n\n",\n  "action": "buy"\n}\n```', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-18'}
2025-07-29 06:51:22,688 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 06:51:22,688 - __main__ - INFO - 📈 第3天交易动作: {'NVDA': 1.0}
2025-07-29 06:51:22,692 - __main__ - INFO - 💰 第3天净值变化: 收益率=-0.0405, 净值=$965524.67
2025-07-29 06:51:22,694 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 06:52:10,165 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 06:52:10,166 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 06:52:41,064 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 06:52:41,064 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 06:52:41,065 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-21, 累计收益=-0.0345, 周收益=-0.0345
2025-07-29 06:52:58,252 - __main__ - INFO - 🎯 找到最终决策声明: sell
2025-07-29 06:52:58,253 - __main__ - WARNING - 修正决策不一致: 声明sell vs 字段hold
2025-07-29 06:52:58,253 - __main__ - INFO - 一致性修正：hold -> sell
2025-07-29 06:52:58,253 - __main__ - INFO - 🤖 TRA 输出: action=sell, position_size=1.0
2025-07-29 06:52:58,254 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: sell (信心度: 0.00)
2025-07-29 06:52:58,254 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 06:52:58,254 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 06:52:58,254 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先对NVIDIA（NVDA）进行了全面的多维度分析。从基础面来看，FAA基本面分析显示NVDA财务健康，估值合理，竞争地位领先。NAA新闻分析显示，尽管有关于华为准备新AI芯片的新闻可能对NVDA构成竞争压力，但整体市场情绪偏向中性。技术面分析中，TAA显示NVDA股票趋势向下，但RSI指标显示股票可能存在超卖情况。展望层分析中，BOA看涨信号强度较弱，BeOA看跌信号强度也不强，NOA认为市场整体呈中性趋势。综合分析，各层级信号存在一定分歧，但基础面和技术面显示NVDA股票存在一定风险。因此，**最终决策：卖出**。', 'action': 'sell', 'trading_actions': {'NVDA': -1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-21'}
2025-07-29 06:52:58,254 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': -1.0}
2025-07-29 06:52:58,254 - __main__ - INFO - 📈 第4天交易动作: {'NVDA': -1.0}
2025-07-29 06:52:58,261 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-29 06:53:32,375 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-29 06:53:32,377 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-29 06:54:14,532 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-29 06:54:14,533 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-29 06:54:14,533 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-22, 累计收益=-0.0345, 周收益=-0.0345
2025-07-29 06:54:29,103 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-29 06:54:29,103 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-29 06:54:29,103 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-29 06:54:29,103 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-29 06:54:29,103 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-29 06:54:29,103 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-29 06:54:29,103 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-29 06:54:29,103 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '经过对NVDA的多维度综合分析，得出以下结论：\n\n1. **基础面评估**：FAA显示NVDA财务健康度良好，估值合理，显示出公司具备较强的竞争力。NAA分析显示，尽管有关于华为加大AI芯片生产的报道，但分析师认为对NVDA的影响有限，且NVDA CEO Huang在日本游说增加AI电力供应，显示出公司对AI市场的重视，整体情绪偏向中性。\n\n2. **技术面分析**：TAA分析结果显示，NVDA目前处于上升趋势，支撑位价格为$96.91，阻力位价格为$101.49。技术指标如RSI接近超买区域，MACD线在信号线上方，5日和20日移动平均线均向上，表明市场趋势看涨。\n\n3. **展望层综合**：BOA看涨信号强于BeOA，NOA认为上涨概率较高，但市场不确定性和观望理由也存在。这表明虽然看涨信号明显，但需谨慎对待潜在的市场风险。\n\n4. **风险评估**：各层级信号基本一致，但需关注NVDA在中国市场的风险以及华为AI芯片生产对NVDA市场份额的影响。\n\n**最终决策：买入**。考虑到NVDA的基本面稳健，技术面看涨，且展望层信号支持买入，但需注意风险控制。', 'action': 'buy', 'trading_actions': {'NVDA': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-22'}
2025-07-29 06:54:29,104 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'NVDA': 1.0}
2025-07-29 06:54:29,104 - __main__ - INFO - 📈 第5天交易动作: {'NVDA': 1.0}
2025-07-29 06:54:29,108 - __main__ - INFO - 💰 第5天净值变化: 收益率=-0.0280, 净值=$938473.97
2025-07-29 06:54:29,108 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-29 06:54:29,108 - __main__ - INFO - ============================================================
2025-07-29 06:54:29,108 - __main__ - INFO - 第 16 周性能分析 - 联盟: frozenset({'FAA', 'BeOA', 'TRA', 'NAA', 'BOA', 'NOA', 'TAA'})
2025-07-29 06:54:29,109 - __main__ - INFO - 周总收益率: -0.0622
2025-07-29 06:54:29,109 - __main__ - INFO - 周夏普比率: -10.8044
2025-07-29 06:54:29,109 - __main__ - INFO - 交易天数: 5
2025-07-29 06:54:29,109 - __main__ - INFO - ============================================================