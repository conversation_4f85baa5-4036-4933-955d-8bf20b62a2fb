line_number,timestamp,date,cumulative_return,weekly_return,full_line
16,"2025-07-28 21:18:59,588",2025-01-02,0.0000,0.0000,"2025-07-28 21:18:59,588 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000"
33,"2025-07-28 21:19:57,562",2025-01-03,0.0284,0.0284,"2025-07-28 21:19:57,562 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-03, 累计收益=0.0284, 周收益=0.0284"
50,"2025-07-28 21:20:50,561",2025-01-06,0.0703,0.0703,"2025-07-28 21:20:50,561 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-06, 累计收益=0.0703, 周收益=0.0703"
67,"2025-07-28 21:22:04,140",2025-01-07,0.0943,0.0943,"2025-07-28 21:22:04,140 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-07, 累计收益=0.0943, 周收益=0.0943"
82,"2025-07-28 21:23:10,782",2025-01-08,0.0851,0.0851,"2025-07-28 21:23:10,782 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-08, 累计收益=0.0851, 周收益=0.0851"
118,"2025-07-28 21:56:39,103",2025-01-08,0.0000,0.0000,"2025-07-28 21:56:39,103 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-08, 累计收益=0.0000, 周收益=0.0000"
136,"2025-07-28 21:57:27,664",2025-01-09,0.0209,0.0209,"2025-07-28 21:57:27,664 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-09, 累计收益=0.0209, 周收益=0.0209"
151,"2025-07-28 21:58:46,214",2025-01-10,0.0070,0.0070,"2025-07-28 21:58:46,214 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-10, 累计收益=0.0070, 周收益=0.0070"
167,"2025-07-28 21:59:51,695",2025-01-13,0.0070,0.0070,"2025-07-28 21:59:51,695 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-13, 累计收益=0.0070, 周收益=0.0070"
181,"2025-07-28 22:00:30,685",2025-01-14,0.0070,0.0070,"2025-07-28 22:00:30,685 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-14, 累计收益=0.0070, 周收益=0.0070"
214,"2025-07-28 22:33:09,715",2025-01-15,0.0000,0.0000,"2025-07-28 22:33:09,715 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-15, 累计收益=0.0000, 周收益=0.0000"
231,"2025-07-28 22:34:13,312",2025-01-16,0.0489,0.0489,"2025-07-28 22:34:13,312 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-16, 累计收益=0.0489, 周收益=0.0489"
248,"2025-07-28 22:35:24,179",2025-01-17,0.0410,0.0410,"2025-07-28 22:35:24,179 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-17, 累计收益=0.0410, 周收益=0.0410"
269,"2025-07-28 22:36:11,238",2025-01-20,0.0465,0.0465,"2025-07-28 22:36:11,238 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-20, 累计收益=0.0465, 周收益=0.0465"
287,"2025-07-28 22:37:09,510",2025-01-21,0.0128,0.0128,"2025-07-28 22:37:09,510 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-21, 累计收益=0.0128, 周收益=0.0128"
323,"2025-07-28 23:09:57,004",2025-01-22,0.0000,0.0000,"2025-07-28 23:09:57,004 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-22, 累计收益=0.0000, 周收益=0.0000"
340,"2025-07-28 23:10:49,020",2025-01-23,0.0066,0.0066,"2025-07-28 23:10:49,020 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-23, 累计收益=0.0066, 周收益=0.0066"
357,"2025-07-28 23:12:59,030",2025-01-24,-0.0050,-0.0050,"2025-07-28 23:12:59,030 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-24, 累计收益=-0.0050, 周收益=-0.0050"
374,"2025-07-28 23:14:22,285",2025-01-27,0.0164,0.0164,"2025-07-28 23:14:22,285 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-27, 累计收益=0.0164, 周收益=0.0164"
391,"2025-07-28 23:15:33,552",2025-01-28,0.0192,0.0192,"2025-07-28 23:15:33,552 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-28, 累计收益=0.0192, 周收益=0.0192"
425,"2025-07-28 23:49:56,436",2025-01-29,0.0000,0.0000,"2025-07-28 23:49:56,436 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-29, 累计收益=0.0000, 周收益=0.0000"
439,"2025-07-28 23:51:04,802",2025-01-30,0.0000,0.0000,"2025-07-28 23:51:04,802 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-30, 累计收益=0.0000, 周收益=0.0000"
454,"2025-07-28 23:52:19,360",2025-01-31,0.0348,0.0348,"2025-07-28 23:52:19,360 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-31, 累计收益=0.0348, 周收益=0.0348"
470,"2025-07-28 23:53:32,455",2025-02-03,0.0342,0.0342,"2025-07-28 23:53:32,455 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-03, 累计收益=0.0342, 周收益=0.0342"
486,"2025-07-28 23:54:49,687",2025-02-04,0.0342,0.0342,"2025-07-28 23:54:49,687 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-04, 累计收益=0.0342, 周收益=0.0342"
522,"2025-07-29 00:28:04,489",2025-02-05,0.0000,0.0000,"2025-07-29 00:28:04,489 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-05, 累计收益=0.0000, 周收益=0.0000"
539,"2025-07-29 00:28:48,968",2025-02-06,-0.0269,-0.0269,"2025-07-29 00:28:48,968 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-06, 累计收益=-0.0269, 周收益=-0.0269"
556,"2025-07-29 00:29:47,371",2025-02-07,0.0036,0.0036,"2025-07-29 00:29:47,371 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-07, 累计收益=0.0036, 周收益=0.0036"
573,"2025-07-29 00:30:45,740",2025-02-10,-0.0106,-0.0106,"2025-07-29 00:30:45,740 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-10, 累计收益=-0.0106, 周收益=-0.0106"
590,"2025-07-29 00:31:45,321",2025-02-11,-0.0329,-0.0329,"2025-07-29 00:31:45,321 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-11, 累计收益=-0.0329, 周收益=-0.0329"
626,"2025-07-29 01:05:15,219",2025-02-12,0.0000,0.0000,"2025-07-29 01:05:15,219 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-12, 累计收益=0.0000, 周收益=0.0000"
643,"2025-07-29 01:06:12,744",2025-02-13,-0.0122,-0.0122,"2025-07-29 01:06:12,744 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-13, 累计收益=-0.0122, 周收益=-0.0122"
660,"2025-07-29 01:07:14,937",2025-02-14,-0.0234,-0.0234,"2025-07-29 01:07:14,937 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-14, 累计收益=-0.0234, 周收益=-0.0234"
678,"2025-07-29 01:08:04,828",2025-02-17,-0.0100,-0.0100,"2025-07-29 01:08:04,828 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-17, 累计收益=-0.0100, 周收益=-0.0100"
695,"2025-07-29 01:09:39,382",2025-02-18,0.0205,0.0205,"2025-07-29 01:09:39,382 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-18, 累计收益=0.0205, 周收益=0.0205"
731,"2025-07-29 01:43:39,460",2025-02-19,0.0000,0.0000,"2025-07-29 01:43:39,460 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-19, 累计收益=0.0000, 周收益=0.0000"
748,"2025-07-29 01:45:48,379",2025-02-20,0.0039,0.0039,"2025-07-29 01:45:48,379 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-20, 累计收益=0.0039, 周收益=0.0039"
765,"2025-07-29 01:46:50,702",2025-02-21,0.0019,0.0019,"2025-07-29 01:46:50,702 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-21, 累计收益=0.0019, 周收益=0.0019"
782,"2025-07-29 01:47:59,129",2025-02-24,-0.0190,-0.0190,"2025-07-29 01:47:59,129 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-24, 累计收益=-0.0190, 周收益=-0.0190"
799,"2025-07-29 01:49:16,949",2025-02-25,-0.0156,-0.0156,"2025-07-29 01:49:16,949 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-25, 累计收益=-0.0156, 周收益=-0.0156"
835,"2025-07-29 02:21:41,492",2025-02-26,0.0000,0.0000,"2025-07-29 02:21:41,492 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-26, 累计收益=0.0000, 周收益=0.0000"
852,"2025-07-29 02:22:58,285",2025-02-27,-0.0233,-0.0233,"2025-07-29 02:22:58,285 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-27, 累计收益=-0.0233, 周收益=-0.0233"
867,"2025-07-29 02:24:19,823",2025-02-28,0.0060,0.0060,"2025-07-29 02:24:19,823 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-28, 累计收益=0.0060, 周收益=0.0060"
884,"2025-07-29 02:25:14,300",2025-03-03,0.0508,0.0508,"2025-07-29 02:25:14,300 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-03, 累计收益=0.0508, 周收益=0.0508"
899,"2025-07-29 02:26:13,878",2025-03-04,0.0868,0.0868,"2025-07-29 02:26:13,878 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-04, 累计收益=0.0868, 周收益=0.0868"
935,"2025-07-29 02:59:34,279",2025-03-05,0.0000,0.0000,"2025-07-29 02:59:34,279 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-05, 累计收益=0.0000, 周收益=0.0000"
952,"2025-07-29 03:00:30,266",2025-03-06,-0.0622,-0.0622,"2025-07-29 03:00:30,266 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-06, 累计收益=-0.0622, 周收益=-0.0622"
968,"2025-07-29 03:01:23,388",2025-03-07,-0.0624,-0.0624,"2025-07-29 03:01:23,388 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-07, 累计收益=-0.0624, 周收益=-0.0624"
985,"2025-07-29 03:02:12,595",2025-03-10,-0.0905,-0.0905,"2025-07-29 03:02:12,595 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-10, 累计收益=-0.0905, 周收益=-0.0905"
1002,"2025-07-29 03:03:16,354",2025-03-11,-0.1084,-0.1084,"2025-07-29 03:03:16,354 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-11, 累计收益=-0.1084, 周收益=-0.1084"
1038,"2025-07-29 03:36:06,407",2025-03-12,0.0000,0.0000,"2025-07-29 03:36:06,407 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-12, 累计收益=0.0000, 周收益=0.0000"
1055,"2025-07-29 03:37:04,340",2025-03-13,-0.0110,-0.0110,"2025-07-29 03:37:04,340 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-13, 累计收益=-0.0110, 周收益=-0.0110"
1072,"2025-07-29 03:37:43,513",2025-03-14,0.0226,0.0226,"2025-07-29 03:37:43,513 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-14, 累计收益=0.0226, 周收益=0.0226"
1089,"2025-07-29 03:38:45,697",2025-03-17,0.0026,0.0026,"2025-07-29 03:38:45,697 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-17, 累计收益=0.0026, 周收益=0.0026"
1106,"2025-07-29 03:39:46,155",2025-03-18,0.0336,0.0336,"2025-07-29 03:39:46,155 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-18, 累计收益=0.0336, 周收益=0.0336"
1140,"2025-07-29 04:12:57,544",2025-03-19,0.0000,0.0000,"2025-07-29 04:12:57,544 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-19, 累计收益=0.0000, 周收益=0.0000"
1157,"2025-07-29 04:14:06,752",2025-03-20,0.0443,0.0443,"2025-07-29 04:14:06,752 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-20, 累计收益=0.0443, 周收益=0.0443"
1174,"2025-07-29 04:15:45,587",2025-03-21,0.0454,0.0454,"2025-07-29 04:15:45,587 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-21, 累计收益=0.0454, 周收益=0.0454"
1191,"2025-07-29 04:17:01,214",2025-03-24,0.0127,0.0127,"2025-07-29 04:17:01,214 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-24, 累计收益=0.0127, 周收益=0.0127"
1208,"2025-07-29 04:18:09,993",2025-03-25,-0.1591,-0.1591,"2025-07-29 04:18:09,993 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-25, 累计收益=-0.1591, 周收益=-0.1591"
1244,"2025-07-29 04:51:03,411",2025-03-26,0.0000,0.0000,"2025-07-29 04:51:03,411 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-26, 累计收益=0.0000, 周收益=0.0000"
1264,"2025-07-29 04:55:05,706",2025-03-27,0.0893,0.0893,"2025-07-29 04:55:05,706 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-27, 累计收益=0.0893, 周收益=0.0893"
1279,"2025-07-29 04:56:00,808",2025-03-28,0.0446,0.0446,"2025-07-29 04:56:00,808 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-28, 累计收益=0.0446, 周收益=0.0446"
1294,"2025-07-29 04:57:13,939",2025-03-31,0.0526,0.0526,"2025-07-29 04:57:13,939 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-31, 累计收益=0.0526, 周收益=0.0526"
1308,"2025-07-29 04:58:31,070",2025-04-01,0.0526,0.0526,"2025-07-29 04:58:31,070 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-01, 累计收益=0.0526, 周收益=0.0526"
1344,"2025-07-29 05:31:44,062",2025-04-02,0.0000,0.0000,"2025-07-29 05:31:44,062 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-02, 累计收益=0.0000, 周收益=0.0000"
1361,"2025-07-29 05:32:53,204",2025-04-03,0.0171,0.0171,"2025-07-29 05:32:53,204 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-03, 累计收益=0.0171, 周收益=0.0171"
1378,"2025-07-29 05:33:47,140",2025-04-04,0.0700,0.0700,"2025-07-29 05:33:47,140 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-04, 累计收益=0.0700, 周收益=0.0700"
1394,"2025-07-29 05:35:11,744",2025-04-07,0.0700,0.0700,"2025-07-29 05:35:11,744 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-07, 累计收益=0.0700, 周收益=0.0700"
1411,"2025-07-29 05:36:33,849",2025-04-08,0.0797,0.0797,"2025-07-29 05:36:33,849 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-08, 累计收益=0.0797, 周收益=0.0797"
1445,"2025-07-29 06:09:47,541",2025-04-09,0.0000,0.0000,"2025-07-29 06:09:47,541 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-09, 累计收益=0.0000, 周收益=0.0000"
1462,"2025-07-29 06:11:01,665",2025-04-10,-0.0058,-0.0058,"2025-07-29 06:11:01,665 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-10, 累计收益=-0.0058, 周收益=-0.0058"
1479,"2025-07-29 06:12:04,900",2025-04-11,-0.0182,-0.0182,"2025-07-29 06:12:04,900 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-11, 累计收益=-0.0182, 周收益=-0.0182"
1496,"2025-07-29 06:13:15,269",2025-04-14,0.0129,0.0129,"2025-07-29 06:13:15,269 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-14, 累计收益=0.0129, 周收益=0.0129"
1513,"2025-07-29 06:14:42,185",2025-04-15,0.0395,0.0395,"2025-07-29 06:14:42,185 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-15, 累计收益=0.0395, 周收益=0.0395"
1549,"2025-07-29 06:48:20,480",2025-04-16,0.0000,0.0000,"2025-07-29 06:48:20,480 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-16, 累计收益=0.0000, 周收益=0.0000"
1565,"2025-07-29 06:49:44,229",2025-04-17,0.0000,0.0000,"2025-07-29 06:49:44,229 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-17, 累计收益=0.0000, 周收益=0.0000"
1583,"2025-07-29 06:51:16,860",2025-04-18,0.0063,0.0063,"2025-07-29 06:51:16,860 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-18, 累计收益=0.0063, 周收益=0.0063"
1598,"2025-07-29 06:52:41,065",2025-04-21,-0.0345,-0.0345,"2025-07-29 06:52:41,065 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-21, 累计收益=-0.0345, 周收益=-0.0345"
1614,"2025-07-29 06:54:14,533",2025-04-22,-0.0345,-0.0345,"2025-07-29 06:54:14,533 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-22, 累计收益=-0.0345, 周收益=-0.0345"
