# Log of Comments on Writing

This log file contains common writing mistakes committed by students, particularly those from China. [cite_start]Many comments contain personal biases and stylistic preferences, but most are "objective." [cite: 4, 8, 9]

## General Writing Principles

* [cite_start]**Reuse:** This log file is an exercise of the "principle of reuse" and is updated periodically. [cite: 7]
* [cite_start]**Simplicity and Clarity:** In technical writing, the simplest and most straightforward way to convey an idea is always the best. [cite: 23] [cite_start]Focus on technical innovations, not writing innovations. [cite: 25]
* [cite_start]**Conciseness:** Avoid distracting the reader with non-customary usage. [cite: 22]
* **Audience Awareness:** Think about your reader. [cite_start]If you spend 30 minutes on a paragraph to make it comprehensible in 3 minutes for the reader, it's time well spent. [cite: 63]
* [cite_start]**Flow and Transitions:** Develop a main theme, plot, and storyline. [cite: 671] [cite_start]Write so each paragraph flows logically from the previous one and leads naturally to the next, using good transitions. [cite: 672, 673]
* [cite_start]**Highlight Contributions:** Clearly state your main contributions; don't expect the reader to discover them. [cite: 678, 679]
* **Persuasion:** Think "persuasion" when writing; information and facts are your tools to persuade. [cite_start]State a claim and provide evidence. [cite: 681]
* [cite_start]**Proofreading:** Run a spelling check on your paper before submitting it. [cite: 137] [cite_start]Correct your own simple typos. [cite: 138, 140, 142]

## Specific Writing Points

1.  **"et al." usage:**
    * [cite_start]Some reviewers dislike "et al." because the first author may not be the main contributor. [cite: 10, 11]
    * [cite_start]Consider mentioning the work itself instead of authors' names to avoid inadvertently offending people. [cite: 12]
    * [cite_start]If there are fewer than four authors, list all names. [cite: 13]
    * [cite_start]When using authors' names, customary practice is to refer only to last names without initials. [cite: 14]
    * [cite_start]Google the correct spelling and italicization of "et al." [cite: 15]
2.  [cite_start]**Learning from others' papers:** Pay attention to presentation style in technical papers by non-Chinese authors and learn from them. [cite: 17, 48] [cite_start]As a novice, avoid words not commonly seen in technical papers (e.g., "keystone work"). [cite: 18, 20, 21]
3.  [cite_start]**Hyphenation:** Use hyphens to make writing clearer and less ambiguous (e.g., "channel-coded PNC" is clearer than "channel coded PNC"). [cite: 27, 28, 31, 32]
4.  **Avoiding "which":**
    * [cite_start]As a novice, try to avoid "which." [cite: 33]
    * [cite_start]Often, sentences can be improved by removing "which" and replacing the verb with its "-ing" form (e.g., "leading to decreased throughput" instead of "which leads to decreased throughput"). [cite: 44, 45, 46]
    * [cite_start]"Which" can be a lazy way to string things together or create long sentences. [cite: 40]
    * [cite_start]Using "which" often makes what follows play a secondary role, potentially understating important points. [cite: 65, 66]
5.  **Articles "a" and "the":**
    * [cite_start]These are difficult for Chinese speakers to master. [cite: 47]
    * [cite_start]Pay careful attention to their usage in non-Chinese papers. [cite: 48]
    * [cite_start]It may take years and many papers to learn proper usage. [cite: 49]
    * [cite_start]Avoid "the" when describing a general process or action (e.g., "Running is a good exercise," not "The running is a good exercise"). [cite: 243, 246]
    * [cite_start]Use "the" when referring to a particular instance or object that is clear to the reader. [cite: 257, 258, 260, 263]
    * [cite_start]For singular countable nouns, if you don't use "a," you usually need "the." [cite: 254]
    * [cite_start]Do not use "the" with proper nouns unless referring to a specific instance or realization. [cite: 345, 631, 635]
6.  [cite_start]**"Denote by" vs. "denote as":** Use "denote by" or "x denotes the transmitted symbol," not "denote as." [cite: 69, 70]
7.  [cite_start]**"Recently" vs. "soon":** "Recently" refers to the near past, not the near future. [cite: 72, 73]
8.  [cite_start]**Semicolon (`;`) for contrast:** Use semicolons effectively, especially for contrasting ideas or breaking long sentences into shorter, clearer ones. [cite: 77, 79, 80]
9.  **"Less" vs. "Fewer":**
    * [cite_start]Use "fewer" for countable items (e.g., "fewer bit errors"). [cite: 81, 82]
    * [cite_start]Use "less" for uncountable things. [cite: 81]
10. **"Less" vs. "Lesser":**
    * [cite_start]"Lesser" often sounds grammatically incorrect in technical writing, where "less" is usually more appropriate for quantitative comparisons. [cite: 83, 84, 92]
    * [cite_start]"Lesser" is typically used in a qualitative or rhetorical sense. [cite: 86, 93]
11. [cite_start]**Pronoun ambiguity:** Avoid using pronouns like "it" if they create ambiguity about what is being referred to. [cite: 96, 97, 100, 106, 112]
12. **"Shows" vs. "Depicts" vs. "Illustrates":**
    * [cite_start]Use "shows" for simple, easy-to-understand figures that display exactly what you want to show. [cite: 113, 121, 281]
    * [cite_start]Use "depicts" for more complex figures illustrating a subtle idea; it's close to "illustrate the idea of... with pictures." [cite: 114, 116] [cite_start]Avoid "depict" for simple presentations. [cite: 115]
    * "Illustrate" implies showing things indirectly, a concept or abstraction. [cite_start]You "illustrate a principle with an example." [cite: 120, 122, 282, 283]
13. [cite_start]**Comparative terms:** When using comparative terms like "difference," "more complex," or "better," clearly establish the benchmark or what you are comparing against. [cite: 123, 124, 126, 127, 128]
14. **"Normal":**
    * [cite_start]Avoid "normal" for precise technical writing. [cite: 132]
    * [cite_start]Use quantifiable statements (e.g., "The algorithm terminates within five iterations in 25 out of 30 runs"). [cite: 133]
    * [cite_start]"A majority of" is better than "normal." [cite: 134]
    * [cite_start]If you use "normal," be prepared to define "abnormal." [cite: 135, 136]
15. [cite_start]**Paragraphing:** Use short paragraphs, with each focusing on one point, for easier reading. [cite: 143, 144]
16. **Math notations in sentences:**
    * [cite_start]Avoid starting a sentence with a mathematical notation. [cite: 149]
    * [cite_start]Insert filler English words to separate a math notation from a preceding period to avoid misinterpretation as multiplication (e.g., "Furthermore, $x(t)$ is positive."). [cite: 147, 148, 151, 153]
    * [cite_start]It's acceptable to have math notation in the middle of a sentence. [cite: 156, 157]
17. [cite_start]**References at sentence beginning:** Insert "Ref." or "Refs." if a citation is at the beginning of a sentence. [cite: 158, 160]
18. [cite_start]**Fraction notation:** Use `1/R` for inline math notation and `$\frac{1}{R}$` for displayed math notation on a separate line. [cite: 163, 164, 165]
19. [cite_start]**Pronunciation of abbreviations:** Pronounce abbreviations as words if they can be easily said that way (e.g., "ACK" like "Act" with a 'k' sound, "OPEC" in two syllables, "sync" as one syllable). [cite: 167, 169, 170, 173, 174]
20. [cite_start]**"Let the node to send" vs. "Let the node send":** Use "We let the node send five packets." [cite: 177]
21. **"In detail" vs. "Specifically" / "In particular":**
    * [cite_start]Replace "In detail" with "Specifically" or "In particular" for brevity and better effect. [cite: 179, 180, 183]
    * [cite_start]These terms help transition from a general idea to a detailed elaboration. [cite: 184, 186]
    * [cite_start]"In detail" usually comes at the end of a sentence. [cite: 187, 189]
22. [cite_start]**Noun as adjective (singular form):** When using a noun as an adjective, typically use the singular form (e.g., "a 64-point FFT," not "a 64-points FFT"). [cite: 190, 191]
23. **Overuse of "propose":**
    * [cite_start]Students from China tend to overuse "propose." [cite: 192]
    * [cite_start]Often, "our scheme" is sufficient instead of "our proposed scheme." [cite: 194]
    * [cite_start]"We put forth" or "We present" can be more forceful than "We propose." [cite: 195, 200]
    * [cite_start]"Propose" implies asking for approval; if you have objective evidence, you "put forth" or "present" your idea. [cite: 196, 197, 198, 200]
    * [cite_start]Avoid the oxymoron "propose a novel scheme." [cite: 201, 202, 203]
24. **"Besides" vs. other transitions:**
    * [cite_start]"Besides" is informal in technical writing. [cite: 205]
    * [cite_start]Use "In addition," "Furthermore," or "Importantly." [cite: 206]
    * [cite_start]"Besides" suggests a side point, not a main focus. [cite: 207, 210]
25. **Plural forms:**
    * [cite_start]"User A and B" should be "users A and B." [cite: 214]
    * [cite_start]"More than one" is singular (e.g., "More than one item is missing"). [cite: 215, 216]
    * [cite_start]"All but one" and "all except one" are plural (e.g., "All but one item are missing"). [cite: 217, 218]
    * [cite_start]"Zero" is plural (e.g., "Zero experiments are successful"). [cite: 219, 220]
26. **"Denote" vs. "Is" vs. "Define":**
    * [cite_start]Often, "is" is crisper and more direct than "denotes" (e.g., "where v is the speed"). [cite: 232, 234, 235, 236]
    * [cite_start]"Define" is used for formal definitions (e.g., speed is "defined by" d/t). [cite: 237, 238]
    * [cite_start]Use "define" or "denote" sparingly for emphasis. [cite: 241, 242]
27. **"Actually":**
    * [cite_start]Often used redundantly by Chinese students. [cite: 266]
    * [cite_start]Use "actually" to emphasize that something appearing to be "A" is "B." [cite: 268]
    * [cite_start]If there's no wrong impression to dispel, omit "actually." [cite: 275, 276]
28. **"Comparing with" vs. "Compared with":**
    * [cite_start]"Compared with" is the common idiomatic usage (e.g., "Compared with device A, device B is better"). [cite: 293, 297, 298]
    * [cite_start]"Comparing with" is not commonly used in idiomatic English. [cite: 294, 304]
29. **"Even if" vs. "Even though":**
    * [cite_start]**"Even if":** Asks the reader to imagine a case and states what *will* happen; implies results may not have been shown yet. [cite: 306, 308, 309, 310, 314]
    * **"Even though":** States something that *has* happened; emphasizes that a condition doesn't prevent an outcome. [cite_start]Results are typically already shown. [cite: 307, 312, 313, 315]
30. **"Number of" vs. "Noun number":**
    * [cite_start]Use "number of" to avoid ambiguity (e.g., "number of slots," not "slot number"). [cite: 323, 324, 326, 327]
    * [cite_start]"Student number" can mean "student ID," while "number of students" is unambiguous. [cite: 325, 326]
31. **Describing systems with multiple properties:**
    * [cite_start]"Our method is useful to multiuser and multislot systems" (two distinct systems). [cite: 330, 331]
    * [cite_start]"Our method is useful to multiuser-multislot systems" (systems possessing both properties simultaneously, using a hyphen). [cite: 330, 332, 333]
32. [cite_start]**Singular nouns without articles:** If a singular noun is not a proper name or an action/process, it usually needs "a" or "the" before it. [cite: 334, 340, 341, 342, 344]
33. [cite_start]**"Performance" redundancy:** Often, "BER" or "Throughput" is sufficient instead of "BER performance" or "Throughput performance." [cite: 349, 350] [cite_start]Similarly, "Comparison" can often be redundant in figure captions. [cite: 350, 351]
34. **"There exists":**
    * [cite_start]Use "there exists" only when stating a theorem in mathematical writing. [cite: 352, 353]
    * [cite_start]For non-mathematical parts, "There is" or simply stating the subject and verb (e.g., "A phase error causes BER degradation") is more natural. [cite: 354, 355, 356]
35. **"Similar to" vs. "As in":**
    * [cite_start]"As in" is often better than "Similar to" when referring to a paper or technique. [cite: 359, 367]
    * [cite_start]"Similar to" compares two things of the same kind. [cite: 359, 360]
    * [cite_start]It is awkward to say a process is "similar to" a paper. [cite: 363]
36. **"Different from," "Unlike," "Unlike in":**
    * [cite_start]"Unlike in [11], our DoF maximization process can be performed using FFT" or "Unlike the technique in [11],..." are better than "Different from [11],..." [cite: 371, 372, 373]
37. **"A lot of":**
    * [cite_start]Used informally; in technical writing, use "much" or "many." [cite: 374, 375, 376]
38. **"Due to" alternatives:** Avoid indiscriminate use of "due to." [cite_start]Consider "Thanks to," "Caused by," "Owing to," "Attributed to," or "Because of." [cite: 378, 379, 380, 381, 382, 383, 384, 385, 386]
39. **"Precision" vs. "Accuracy":**
    * [cite_start]**Accuracy:** Refers to the error between an estimation and the true value. [cite: 387, 388]
    * **Precision:** May refer to variance or resolution. [cite_start]You can be precise without being accurate. [cite: 389, 390, 391, 392, 393]
40. **"Novel":**
    * [cite_start]Avoid using "novel" to describe your own work; it's often redundant and pompous. [cite: 402, 404, 405, 406]
    * [cite_start]Whether something is novel is for the reader to judge. [cite: 404]
    * [cite_start]Overuse can make you seem uncreative or prone to clichés. [cite: 407, 408]
    * [cite_start]"Novel" translates to "new and clever," so claiming "a novel scheme" can sound hubristic. [cite: 410, 412, 414, 415]
41. [cite_start]**"a" vs. "an":** Based on pronunciation, not spelling (e.g., "An hour," "An ML decoder"). [cite: 416, 417, 418, 419, 420]
42. [cite_start]**"as" vs. "because":** Avoid using "as" to mean "because" in technical writing, as "as" has multiple meanings and can lead to ambiguity. [cite: 422, 424, 425, 427, 428]
43. **"We" and "I" in scientific writing:**
    * [cite_start]It is acceptable and often preferred to use "We" or "I" for direct, less pompous sentences. [cite: 429, 430, 432, 433, 436]
    * [cite_start]Avoid passive constructions like "It is shown that..." which can be ambiguous ("Who is 'it'?"). [cite: 431, 434]
44. **"Advantages" vs. "Merits":**
    * [cite_start]"Advantages" implies comparison; specify what your invention has advantages *compared with*. [cite: 441, 442, 444, 446, 448]
    * [cite_start]"Merits" can be used without explicit comparison. [cite: 450]
45. **"Work" or "works" (in "Related Work"):**
    * [cite_start]Common practice is "Related Work" (singular). [cite: 452, 454]
    * [cite_start]"Work" (singular) refers to the collective effort in a subject area. [cite: 455]
    * [cite_start]"Works" (plural) traditionally refers to art, music, etc. [cite: 454]
46. **"Ends" before "Means" (Chinese influence):**
    * [cite_start]Native English speakers typically put the "means" before the "ends" (e.g., "We filter out the noise to achieve good performance"). [cite: 457, 460, 461, 641, 642, 645]
    * [cite_start]The "ends before means" style is reserved for emphasis when the "ends" have already been discussed at length. [cite: 462, 463]
47. **"First," "Second" vs. "Firstly," "Secondly":** Use "First," "Second" for American conferences/journals. [cite_start]"Firstly," "Secondly" are British usages. [cite: 464, 465, 466, 469]
48. **"Compose," "Consist," "Comprise":**
    * [cite_start]Correct: "Algorithm A is composed of two parts." [cite: 471, 472]
    * [cite_start]Correct: "Algorithm A consists of two parts." [cite: 473, 474]
    * [cite_start]Correct: "Algorithm A comprises two parts." [cite: 475, 476]
49. **"Estimate" vs. "Estimation" (as nouns):**
    * [cite_start]**Estimate (noun):** Refers to a particular instance or value. [cite: 477, 480, 481, 484]
    * [cite_start]**Estimation (noun):** Refers to the process or action of estimating. [cite: 478, 479, 482, 483]
50. [cite_start]**"More and more":** Colloquial; replace with "increasing" or similar terms in technical writing. [cite: 485, 486, 488]
51. [cite_start]**Colon before displayed equations:** Use a colon before a displayed equation if you would use one for an inline equation in the same context; otherwise, don't. [cite: 489, 490, 491, 492, 494, 497, 500, 502]
52. **"Compute" vs. "Calculate":**
    * [cite_start]**Compute:** Used for operations requiring a computer or sophisticated math beyond simple arithmetic (e.g., "compute FFT," "compute n!"). [cite: 505, 506, 507]
    * [cite_start]**Calculate:** Related to arithmetic. [cite: 505]
    * [cite_start]**Derive:** Use "derive" when deriving an equation or expression. [cite: 509, 510]
53. **"Totally" vs. "in total":** Use "in total" for quantities (e.g., "There are N devices in total"). [cite_start]"Totally" means "completely." [cite: 511, 512, 513]
54. **"In the following," "as follows," "below":**
    * [cite_start]"We elaborate the FFT algorithm below" and "We elaborate the FFT algorithm in the following" are correct. [cite: 516]
    * [cite_start]"We elaborate the FFT algorithm as follows" sounds awkward. [cite: 517]
    * [cite_start]Avoid "as below," "as follow," "as following." [cite: 519, 520, 521]
55. **"Call" and "referred to as":**
    * Correct: "We call the algorithm a turbo algorithm." / "We refer to the algorithm as a turbo algorithm." / "The algorithm is called a turbo algorithm." [cite_start]/ "The algorithm is referred to as a turbo algorithm." [cite: 523, 524, 525, 526]
    * Incorrect: "We call the algorithm as a turbo algorithm." [cite_start]/ "We refer to the algorithm a turbo algorithm." [cite: 528, 529, 530, 531]
56. **"Utilize" vs. "Use":**
    * [cite_start]Prefer "use" unless you are making clever, unobvious use of something for a purpose other than its original one. [cite: 532, 535, 539, 540, 541, 543]
    * [cite_start]Overuse of "utilize" makes writing sound unnecessarily pompous. [cite: 535, 545]
    * [cite_start]Similarly, minimize "leverage" when "use" suffices. [cite: 546]
57. [cite_start]**Using "big words":** Unless you are highly proficient in English, avoid "big words" when simpler, common words will do. [cite: 559, 560]
58. [cite_start]**"Traffic" (uncountable):** "Traffic" is uncountable; its plural form is still "traffic," not "traffics." [cite: 563]
59. **Contrastive phrases:**
    * [cite_start]**"In contrast to," "Contrary to," "By contrast," "Unlike":** Used to highlight differences. [cite: 564, 565, 566, 567, 568, 569, 570]
    * [cite_start]**"Compared with":** More neutral, states outcomes of a comparison. [cite: 564, 571, 573]
    * [cite_start]**"Compared to":** States that two things are comparable on certain aspects. [cite: 564, 574, 575]
60. [cite_start]**Figure captions:** Insert a period (`.`) or colon (`:`) after "Figure X" or "Fig. x" (e.g., "Figure 1. Block Diagram of FFT."). [cite: 577, 578, 579, 580, 581, 582]
61. **"Ever" (redundant):** In simple past statements, "ever" is often redundant and unnatural (e.g., "I saw a unicorn," not "I ever saw a unicorn"). [cite_start]It is correct in questions (e.g., "Have you ever seen a unicorn?"). [cite: 584, 586, 587, 588, 589, 591, 595]
62. **Verb tenses (past vs. present):**
    * [cite_start]Use past tense for actions you performed in an experiment. [cite: 597, 599, 600, 605]
    * [cite_start]Use present tense for system properties that remain valid today or interactions between machines/software/hardware that are still true. [cite: 597, 601, 602, 604, 607, 610, 612]
    * [cite_start]For past work by others, use past tense for their actions ("proved") but present tense for the validity of their findings if still true today ("Their proof makes use of..."). [cite: 610, 611, 612]
63. **"Next we..." vs. "We next...":**
    * [cite_start]**"Next, we...":** More natural when describing a sequence of actions within an algorithm or procedure. [cite: 619, 623]
    * [cite_start]**"We next...":** More natural as a transition to a new topic or a new paragraph. [cite: 625, 626, 629]
64. **Aim before action vs. action before aim:**
    * [cite_start]Most native English speakers prefer "action before aim" (e.g., "I take the MTR to go to Shatin"). [cite: 641, 642]
    * [cite_start]"Aim before action" ("To go to Shatin, I take the MTR") is usually reserved for significant aims. [cite: 643]
    Generated candidates will first undergo A/B testing in the next stage. If the A/B test shows improvement, the new prompt will be adopted; otherwise, it will be discarded.

## [cite_start]Trademarks of Writing Styles in My Group [cite: 654]

1.  [cite_start]The paper should not be a pain to read. [cite: 656]
2.  [cite_start]The paper should be enjoyable to read. [cite: 657]
3.  [cite_start]The reader should easily recognize and appreciate your contributions. [cite: 658]