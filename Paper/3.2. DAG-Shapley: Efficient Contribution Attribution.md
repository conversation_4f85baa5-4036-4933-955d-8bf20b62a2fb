## 3.2. DAG-Shapley: Efficient Contribution Attribution

To address the computational challenges of real-time contribution attribution in multi-agent systems, we model the collaborative workflow as a cooperative game where agents $\mathcal{N} = \{a_1, a_2, \ldots, a_n\}$ constitute the players. In financial trading applications, real-time attribution is critical for dynamic strategy adaptation and risk management, as market conditions change rapidly and agent performance must be continuously monitored \cite{zhang2024multiagent, kumar2024unleashing}. The performance of any coalition $S \subseteq \mathcal{N}$ is quantified by a characteristic function $v(S)$, which in our implementation represents the Sharpe ratio achieved by coalition $S$ over a specified trading period—a standard measure of risk-adjusted return \cite{sharpe1966mutual}.

The classical Shapley value \cite{shapley1953value} provides a unique, axiomatically fair distribution of the total surplus $v(\mathcal{N})$ among agents. For agent $a_i$, the Shapley value $\phi_i(v)$ represents its weighted average marginal contribution across all possible coalitions:

$$\phi_i(v) = \sum_{S \subseteq \mathcal{N} \setminus \{a_i\}} \frac{|S|!(n - |S| - 1)!}{n!} [v(S \cup \{a_i\}) - v(S)]$$

While axiomatically desirable, the classical Shapley value requires evaluating $v(S)$ for all $2^n$ possible coalitions, making it computationally intractable for real-time systems with more than 10-15 agents. Recent approaches have addressed this through stochastic sampling \cite{strumbelj2014explaining, castro2017improving} or specialized approximations for convex games \cite{fatima2008linear}, but these either sacrifice accuracy through Monte Carlo estimation or are limited to specific game classes.

Our **DAG-Shapley** framework provides exact Shapley value computation while exploiting the structural properties of agent workflows. The method consists of two synergistic optimization stages that transform the complexity from exponential in the number of agents to polynomial in the layer structure, enabling real-time contribution attribution even for large-scale systems.

### Stage 1: Coalition Space Pruning via Structural Analysis  

Our first contribution leverages the inherent DAG structure to identify and eliminate coalitions that cannot produce meaningful outcomes. This approach builds upon recent advances in structure-aware game theory \cite{michalak2013efficient, li2024shapley} while providing novel pruning rules specific to workflow-based multi-agent systems.

The key insight is that in DAG-structured systems, not all coalitions are functionally viable. We formalize this through the concept of **workflow completeness**:

**Definition 1 (Viable Coalition).** A coalition $S \subseteq \mathcal{N}$ is viable if and only if it possesses the structural capacity to execute a complete information-to-action workflow. Formally:

$$\text{S is viable} \iff (a_T \in S) \land (S \cap \mathcal{V}_{\text{source}} \neq \emptyset) \land \text{PathExists}(S)$$

where $\text{PathExists}(S)$ verifies that there exists at least one directed path from some source node in $S \cap \mathcal{V}_{\text{source}}$ to the sink node $a_T$ using only agents in $S$.

**Theorem 1 (Nullity of Non-viable Coalitions).** For any non-viable coalition $S$, the characteristic function value is zero: $v(S) = 0$.

*Proof Sketch:* A coalition lacking the sink node cannot execute the final action required to interact with the environment. A coalition without source nodes cannot initiate the decision-making process. A coalition without connecting paths cannot propagate information from inputs to outputs. In all cases, no measurable performance can be achieved. $\square$

This enables two deterministic pruning rules that dramatically reduce the evaluation space:

**Pruning Rule 1 (Endpoint Rule).** Any coalition $S$ not containing the sink node $a_T$ is non-viable:
$$a_T \notin S \Rightarrow v(S) = 0$$

**Pruning Rule 2 (Source Rule).** Any coalition $S$ containing no source agents is non-viable:
$$S \cap \mathcal{V}_{\text{source}} = \emptyset \Rightarrow v(S) = 0$$

**Pruning Rule 3 (Connectivity Rule).** Any coalition $S$ without a directed path from sources to sink is non-viable:
$$\neg\text{PathExists}(S) \Rightarrow v(S) = 0$$

**Theorem 2 (Shapley Value Preservation).** The DAG-Shapley value computed using only viable coalitions $\mathcal{C}_{\text{viable}}$ equals the classical Shapley value computed over all coalitions $2^{\mathcal{N}}$.

*Proof:* Since $v(S) = 0$ for all $S \notin \mathcal{C}_{\text{viable}}$, these coalitions contribute zero to the marginal contribution terms in the Shapley formula. Thus, the sum over viable coalitions yields the same result as the sum over all coalitions. $\square$

```latex
\begin{algorithm}[h]
\caption{Coalition Space Pruning}
\label{alg:coalition_pruning}
\begin{algorithmic}[1]
\Require Agent set $\mathcal{N}$, source set $\mathcal{V}_{\text{source}}$, sink agent $a_T$, DAG structure $\mathcal{G}$
\Ensure Viable coalition set $\mathcal{C}_{\text{viable}}$, pruned coalition set $\mathcal{C}_{\text{pruned}}$
\State $\mathcal{C}_{\text{viable}} \leftarrow \emptyset$, $\mathcal{C}_{\text{pruned}} \leftarrow \emptyset$
\For{each coalition $S \in 2^{\mathcal{N}}$}
    \If{$a_T \notin S$} \Comment{Endpoint Rule}
        \State $\mathcal{C}_{\text{pruned}} \leftarrow \mathcal{C}_{\text{pruned}} \cup \{S\}$
        \State \textbf{continue}
    \EndIf
    \If{$S \cap \mathcal{V}_{\text{source}} = \emptyset$} \Comment{Source Rule}
        \State $\mathcal{C}_{\text{pruned}} \leftarrow \mathcal{C}_{\text{pruned}} \cup \{S\}$
        \State \textbf{continue}
    \EndIf
    \If{$\neg\text{PathExists}(S, \mathcal{G})$} \Comment{Connectivity Rule}
        \State $\mathcal{C}_{\text{pruned}} \leftarrow \mathcal{C}_{\text{pruned}} \cup \{S\}$
        \State \textbf{continue}
    \EndIf
    \State $\mathcal{C}_{\text{viable}} \leftarrow \mathcal{C}_{\text{viable}} \cup \{S\}$
\EndFor
\State \Return $\mathcal{C}_{\text{viable}}, \mathcal{C}_{\text{pruned}}$
\end{algorithmic}
\end{algorithm}
```

For our 7-agent financial trading system, this pruning reduces the coalition space from $2^7 = 128$ to 56 viable coalitions—a **56.25%** reduction. The reduction ratio scales favorably with system size, as larger systems typically have more complex DAG structures that enable more aggressive pruning.

### Stage 2: Generalized Hierarchical Memoization (GHM)

While coalition space pruning reduces the *number* of evaluations required, we introduce **Generalized Hierarchical Memoization (GHM)** to minimize the *computational cost* of evaluating the remaining viable coalitions. This technique represents our second major contribution, exploiting the layered structure of DAG workflows to achieve exponential computational savings through intelligent caching.

The foundation of GHM rests on a critical observation about layered computation: in a DAG with layers $\mathcal{L}_1, \mathcal{L}_2, \ldots, \mathcal{L}_{N_L}$, the output of any agent $a \in \mathcal{L}_i$ depends only on the collective outputs from preceding layers $\mathcal{L}_1, \ldots, \mathcal{L}_{i-1}$. This functional dependency enables aggressive memoization where agent computations can be cached and reused across multiple coalition evaluations.

**Definition 2 (Layer Input Configuration).** For layer $\mathcal{L}_i$, a **layer input configuration** $\mathcal{X}_i$ is defined as the set of active agents from all preceding layers: $\mathcal{X}_i = \bigcup_{j=1}^{i-1} (S \cap \mathcal{L}_j)$ for some coalition $S$.

**Theorem 3 (Functional Determinism).** For any agent $a \in \mathcal{L}_i$ and layer input configuration $\mathcal{X}_i$, the agent's output is deterministic: $O(a, \mathcal{X}_i) = f_a(\{O(a') : a' \in \mathcal{X}_i\})$ for some function $f_a$.

This determinism enables the core GHM optimization: **if two coalitions induce the same layer input configuration for layer $\mathcal{L}_i$, all agents in $\mathcal{L}_i$ will produce identical outputs.** Thus, we can compute each unique layer input configuration once and reuse the results across multiple coalition evaluations.

**Computational Complexity Analysis:**

The total computational cost under GHM is determined by the number of unique layer input configurations across all layers:

$$\text{Cost}_{\text{GHM}} = \sum_{i=1}^{N_L} \left[ \left( \prod_{j=1}^{i-1} \min(2^{|\mathcal{L}_j|}, |\mathcal{C}_{\text{viable}}|) \right) \cdot \sum_{a \in \mathcal{L}_i} C(a) \right]$$

where $C(a)$ represents the computational cost of executing agent $a$, and the product term counts the number of unique input configurations for layer $\mathcal{L}_i$.

**Theorem 4 (GHM Complexity Bound).** For a DAG with maximum layer width $W = \max_i |\mathcal{L}_i|$ and $N_L$ layers, GHM achieves computational complexity $O(2^{W \cdot N_L})$ compared to $O(|\mathcal{C}_{\text{viable}}| \cdot C_{\text{total}})$ for naive evaluation, where $C_{\text{total}}$ is the cost of one complete system execution.

For our 3-3-1 layered system with mandatory source and sink layers, GHM reduces the total agent executions from 448 (naive approach) to 80—an **82.14%** reduction in computational cost.

```latex
\begin{algorithm}[h]
\caption{Generalized Hierarchical Memoization (GHM)}
\label{alg:ghm}
\begin{algorithmic}[1]
\Require Layered DAG $\mathcal{L}_1, \ldots, \mathcal{L}_{N_L}$, viable coalitions $\mathcal{C}_{\text{viable}}$
\Ensure Agent output cache $\mathcal{M}$ for all required computations
\State $\mathcal{M} \leftarrow \{\}$ \Comment{Initialize memoization cache}
\For{$i \leftarrow 1$ to $N_L$}
    \State $\mathcal{X}_{\text{configs}} \leftarrow \{\}$ \Comment{Unique input configurations for layer $i$}
    \For{each coalition $S \in \mathcal{C}_{\text{viable}}$}
        \State $\mathcal{X}_i \leftarrow \bigcup_{j=1}^{i-1} (S \cap \mathcal{L}_j)$ \Comment{Layer input configuration}
        \State $\mathcal{X}_{\text{configs}} \leftarrow \mathcal{X}_{\text{configs}} \cup \{\mathcal{X}_i\}$
    \EndFor
    \For{each configuration $\mathcal{X}_i \in \mathcal{X}_{\text{configs}}$}
        \For{each agent $a \in \mathcal{L}_i \cap S$ for some $S$ with input config $\mathcal{X}_i$}
            \State $\text{key} \leftarrow (a, \mathcal{X}_i)$
            \If{$\text{key} \notin \mathcal{M}$}
                \State $\text{inputs} \leftarrow \{O(a') : a' \in \mathcal{X}_i\}$ from $\mathcal{M}$
                \State $\mathcal{M}[\text{key}] \leftarrow \text{ExecuteAgent}(a, \text{inputs})$
            \EndIf
        \EndFor
    \EndFor
\EndFor
\State \Return $\mathcal{M}$
\end{algorithmic}
\end{algorithm}
```

### Integration and Optimality Analysis

The combination of coalition space pruning and GHM achieves near-optimal efficiency for DAG-structured multi-agent systems. The overall computational complexity becomes:

$$\text{Complexity}_{\text{DAG-Shapley}} = O\left(|\mathcal{C}_{\text{viable}}| + \sum_{i=1}^{N_L} 2^{W_i \cdot i}\right)$$

where $W_i$ is the width of layer $i$. This represents a fundamental improvement over the $O(2^n \cdot C_{\text{total}})$ complexity of naive Shapley computation.

**Theorem 5 (Approximation Quality).** DAG-Shapley produces exact Shapley values (zero approximation error) while achieving exponential computational savings for systems with bounded layer widths.

This theoretical foundation enables real-time contribution attribution in large-scale multi-agent systems, making it practically feasible to apply game-theoretic fairness principles to dynamic collaborative environments. The method's effectiveness has been demonstrated in our financial trading system, where it enables continuous agent performance monitoring and optimization without compromising system responsiveness.

### Generalization and Applicability

The DAG-Shapley framework generalizes to any multi-agent system that can be modeled as a DAG with clear information flow dependencies. Key requirements include:

1. **Workflow Structure**: Agents must form a directed acyclic graph with identifiable information dependencies
2. **Functional Determinism**: Agent outputs must be deterministic functions of their inputs
3. **Performance Measurability**: Coalition performance must be quantifiable through a characteristic function

The method is particularly effective for systems with:

- **Hierarchical Processing**: Natural layered structures (e.g., data processing pipelines, decision support systems)
- **Clear Dependencies**: Well-defined information flow patterns
- **Real-time Requirements**: Need for continuous contribution assessment

Common applications include automated trading systems, collaborative robotics, distributed sensing networks, and multi-stage recommendation systems.

---

## References (Additional Citations Needed)

- \cite{jang2023learning}: Jang et al. (2023). "Learning Multiple Coordinated Agents under Directed Acyclic Graph Constraints." ICML.
- \cite{chen2024dag}: Chen et al. (2024). "DAG-Plan: Generating Directed Acyclic Dependency Graphs for Dual-Arm Cooperative Planning."
- \cite{wang2024multiagent}: Wang et al. (2024). "Multi-agent Reinforcement Learning-based Adaptive Heterogeneous DAG Scheduling."
- \cite{li2024shapley}: Li et al. (2024). "Shapley Value Based Multi-Agent Reinforcement Learning: Theory, Method and Its Application to Energy Network." arXiv:2402.15324.
- \cite{rodriguez2024redefining}: Rodriguez et al. (2024). "Redefining Contributions: Shapley-Driven Federated Learning." IJCAI.
- \cite{liu2024tradingagents}: Liu et al. (2024). "TradingAgents: Multi-Agents LLM Financial Trading Framework." arXiv:2412.20138.
- \cite{zhang2024multiagent}: Zhang et al. (2024). "A Multi-Agent Reinforcement Learning Framework for Optimizing Financial Trading Strategies Based on TimesNet." Expert Systems with Applications.
- \cite{thompson2023collective}: Thompson et al. (2023). "Collective eXplainable AI: Explaining Cooperative Strategies and Agent Contribution in Multiagent Reinforcement Learning with Shapley Values."
- \cite{kumar2024unleashing}: Kumar et al. (2024). "Unleashing the Power of Multi-Agent Reinforcement Learning for Algorithmic Trading." Computers, Materials & Continua.
- \cite{strumbelj2014explaining}: Štrumbelj, E., & Kononenko, I. (2014). "Explaining Prediction Models and Individual Predictions with Feature Contributions." Knowledge and Information Systems, 41(3), 647-665.
- \cite{castro2017improving}: Castro, J., Gómez, D., & Tejada, J. (2017). "Improving Polynomial Estimation of the Shapley Value by Stratified Random Sampling with Optimum Allocation." Computers & Operations Research, 82, 180-188.
- \cite{michalak2013efficient}: Michalak, T. P., Aadithya, K. V., Szczepanski, P. L., Ravindran, B., & Jennings, N. R. (2013). "Efficient Computation of the Shapley Value for Game-Theoretic Network Centrality." Journal of Artificial Intelligence Research, 46, 607-650.
- \cite{sharpe1966mutual}: Sharpe, W. F. (1966). "Mutual Fund Performance." Journal of Business, 39(1), 119-138.
- \cite{fatima2008linear}: Fatima, S., Wooldridge, M., & Jennings, N. R. (2008). "A Linear Approximation Method for the Shapley Value." Artificial Intelligence, 172(14), 1673-1699.