# 实现计划

- [x] 1. 增强周期性Shapley值计算回调机制
  - 修改 `_default_weekly_shapley_callback` 方法，使其能够进行实际的Shapley值计算
  - 添加 `_calculate_and_store_weekly_shapley` 方法来在交易模拟过程中计算Shapley值
  - 实现周期性结果存储机制，在每个交易周期结束时保存Shapley值
  - _需求: 1.1, 1.2, 1.3_

- [x] 2. 创建周期性Shapley值管理器
  - 实现 `PeriodicShapleyManager` 类来管理周期性Shapley值结果
  - 添加数据存储、检索和验证功能
  - 确保数据在交易模拟过程中正确累积
  - _需求: 2.1, 2.2, 3.2_

- [ ] 3. 修改阶段3的Shapley值计算逻辑
  - 更新 `_run_periodic_shapley_calculation_phase` 方法，优先使用预先计算的周期性结果
  - 实现结果汇总模式，而不是重新计算所有Shapley值
  - 保持向后兼容性，在没有预先计算结果时降级到原有逻辑
  - _需求: 3.1, 3.3, 3.4_

- [x] 4. 实现联盟数据收集机制
  - 添加 `_collect_current_week_coalition_data` 方法来收集当前周期的联盟数据
  - 确保在每个交易周期结束时能够获取到完整的联盟性能数据
  - 实现数据验证，确保收集到的数据足够进行Shapley值计算
  - _需求: 1.4, 2.3_

- [x] 5. 创建测试用例验证修复效果
  - 编写测试用例验证每5个交易日进行一次Shapley值计算
  - 确保每 5 个交易日,能够完整触发阶段一,阶段二,阶段三,阶段四
  - 编写测试用例验证多个周期的Shapley值计算正确性
  - 编写测试用例验证Phase4能够获得正确的周期性Shapley值数据
  - 编写测试用例验证异常情况下的降级处理
  - _需求: 4.1, 4.2, 4.3, 4.4_

- [ ] 6. 运行回归测试确保系统稳定性
  - 运行现有的所有测试用例确保没有破坏现有功能
  - 验证标准四阶段流程仍然正常工作
  - 确认不会重新引入重复优化问题
  - 验证系统性能没有显著下降
  - _需求: 3.3, 3.4_