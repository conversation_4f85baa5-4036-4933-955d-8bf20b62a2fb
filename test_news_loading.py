#!/usr/bin/env python3
"""
测试新闻数据加载的一致性
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stock_trading_env import StockTradingEnv
import pandas as pd

def test_news_loading(symbol):
    """测试特定股票的新闻数据加载"""
    print(f"\n{'='*50}")
    print(f"测试股票: {symbol}")
    print(f"{'='*50}")
    
    # 创建交易环境
    config = {
        "stocks": [symbol],
        "start_date": "2025-01-01",
        "end_date": "2025-01-10",
        "starting_cash": 1000000,
        "news_window": 5  # 设置新闻窗口为5天，确保能获取到数据
    }
    env = StockTradingEnv(config)
    
    # 获取状态
    state = env._get_state()
    
    # 检查新闻历史
    news_history = state.get('news_history', {})
    
    print(f"新闻历史日期数量: {len(news_history)}")
    
    # 统计每日新闻数量
    total_news = 0
    for date, stocks_news in news_history.items():
        news_count = len(stocks_news.get(symbol, []))
        if news_count > 0:
            print(f"  {date}: {news_count} 条新闻")
            total_news += news_count
    
    print(f"总新闻数量: {total_news}")
    
    # 显示第一条新闻样例（如果有的话）
    for date, stocks_news in news_history.items():
        if symbol in stocks_news and len(stocks_news[symbol]) > 0:
            first_news = stocks_news[symbol][0]
            print(f"\n第一条新闻样例 ({date}):")
            print(f"  时间: {first_news.get('time', 'N/A')}")
            print(f"  标题: {first_news.get('title', 'N/A')[:50]}...")
            break
    
    return total_news

def main():
    print("测试新闻数据加载一致性")
    
    # 测试AAPL
    aapl_news_count = test_news_loading("AAPL")
    
    # 测试META
    meta_news_count = test_news_loading("META")
    
    print(f"\n{'='*50}")
    print("总结:")
    print(f"AAPL 新闻数量: {aapl_news_count}")
    print(f"META 新闻数量: {meta_news_count}")
    
    if aapl_news_count > 0 and meta_news_count > 0:
        print("✅ 两个股票都能成功加载新闻数据")
    elif aapl_news_count > 0 and meta_news_count == 0:
        print("❌ AAPL能加载新闻，但META无法加载新闻")
    elif aapl_news_count == 0 and meta_news_count > 0:
        print("❌ META能加载新闻，但AAPL无法加载新闻")
    else:
        print("❌ 两个股票都无法加载新闻数据")

if __name__ == "__main__":
    main()