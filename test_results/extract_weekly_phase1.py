#!/usr/bin/env python3
"""
Extract Phase 1 data from each week in log files and save as JSON.
"""

import re
import os
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from datetime import datetime
import logging

def extract_weekly_phase1_data(log_file_path: str) -> Dict[str, Dict]:
    """
    Extract Phase 1 data from all weeks in a log file.
    
    Args:
        log_file_path: Path to the log file
        
    Returns:
        Dictionary with week numbers as keys and phase1 data as values
    """
    weekly_data = {}
    current_phase1 = False
    current_week = None
    current_week_data = {
        'daily_returns': [],
        'week_performance': {},
        'start_date': None,
        'end_date': None,
        'trading_days': 0
    }
    
    # Patterns to match
    phase1_pattern = r'🔍 阶段1: 完整联盟详细分析'
    week_pattern = r'📅 当前周数: (\d+)'
    return_pattern = r'💰 第(\d+)天净值变化: 收益率=([-+]?\d*\.?\d+), 净值=\$([0-9,]+\.?\d*)'
    week_performance_pattern = r'第 (\d+) 周性能分析.*联盟:'
    week_return_pattern = r'周总收益率: ([-+]?\d*\.?\d+)'
    week_sharpe_pattern = r'周夏普比率: ([-+]?\d*\.?\d+)'
    trading_days_pattern = r'交易天数: (\d+)'
    date_adjustment_pattern = r'📅 调整交易起始日期: 从 .* 到 (\d{4}-\d{2}-\d{2})'
    
    try:
        with open(log_file_path, 'r', encoding='utf-8') as f:
            for line in f:
                # Check for week number
                week_match = re.search(week_pattern, line)
                if week_match:
                    week_num = int(week_match.group(1))
                    if current_week != week_num:
                        # Save previous week data if exists
                        if current_week is not None and current_week_data['daily_returns']:
                            weekly_data[f"week_{current_week}"] = current_week_data.copy()
                        
                        # Start new week
                        current_week = week_num
                        current_week_data = {
                            'daily_returns': [],
                            'week_performance': {},
                            'start_date': None,
                            'end_date': None,
                            'trading_days': 0,
                            'week_number': week_num
                        }
                    continue
                
                # Check for date adjustment (to get actual start date)
                date_match = re.search(date_adjustment_pattern, line)
                if date_match and current_week is not None:
                    current_week_data['start_date'] = date_match.group(1)
                    continue
                
                # Check if we're entering Phase 1
                if re.search(phase1_pattern, line):
                    current_phase1 = True
                    continue
                
                # Check if we're leaving Phase 1 (week performance analysis)
                perf_match = re.search(week_performance_pattern, line)
                if current_phase1 and perf_match:
                    current_phase1 = False
                    continue
                
                # Extract daily return data if we're in Phase 1
                if current_phase1 and current_week is not None:
                    return_match = re.search(return_pattern, line)
                    if return_match:
                        day_num = int(return_match.group(1))
                        return_rate = float(return_match.group(2))
                        net_value = return_match.group(3).replace(',', '')
                        
                        current_week_data['daily_returns'].append({
                            'day': day_num,
                            'return_rate': return_rate,
                            'return_percent': return_rate * 100,
                            'net_value': float(net_value)
                        })
                        continue
                
                # Extract week performance metrics
                if current_week is not None:
                    # Week total return
                    week_ret_match = re.search(week_return_pattern, line)
                    if week_ret_match:
                        current_week_data['week_performance']['total_return'] = float(week_ret_match.group(1))
                        current_week_data['week_performance']['total_return_percent'] = float(week_ret_match.group(1)) * 100
                    
                    # Week Sharpe ratio
                    sharpe_match = re.search(week_sharpe_pattern, line)
                    if sharpe_match:
                        current_week_data['week_performance']['sharpe_ratio'] = float(sharpe_match.group(1))
                    
                    # Trading days
                    days_match = re.search(trading_days_pattern, line)
                    if days_match:
                        current_week_data['trading_days'] = int(days_match.group(1))
        
        # Don't forget the last week
        if current_week is not None and current_week_data['daily_returns']:
            weekly_data[f"week_{current_week}"] = current_week_data
            
    except Exception as e:
        logging.error(f"Error processing {log_file_path}: {e}")
        return {}
    
    return weekly_data

def process_all_logs_weekly(test_results_dir: str) -> Dict[str, Dict]:
    """
    Process all log files and extract weekly Phase 1 data.
    
    Args:
        test_results_dir: Directory containing log files
        
    Returns:
        Dictionary mapping stock symbols to their weekly data
    """
    all_stocks_data = {}
    log_files = [f for f in os.listdir(test_results_dir) if f.endswith('.log')]
    
    for log_file in log_files:
        # Extract stock symbol from filename
        stock_symbol = log_file.split('_')[0]
        log_path = os.path.join(test_results_dir, log_file)
        
        print(f"Processing {log_file} for weekly Phase 1 data...")
        weekly_data = extract_weekly_phase1_data(log_path)
        
        if weekly_data:
            all_stocks_data[stock_symbol] = {
                'stock_symbol': stock_symbol,
                'log_file': log_file,
                'total_weeks': len(weekly_data),
                'extraction_time': datetime.now().isoformat(),
                'weekly_data': weekly_data
            }
            print(f"  Found {len(weekly_data)} weeks of Phase 1 data for {stock_symbol}")
            
            # Print summary for each week
            for week_key, week_data in weekly_data.items():
                daily_count = len(week_data['daily_returns'])
                total_ret = week_data['week_performance'].get('total_return_percent', 'N/A')
                print(f"    {week_key}: {daily_count} days, Total return: {total_ret}%")
        else:
            print(f"  No weekly Phase 1 data found for {stock_symbol}")
    
    return all_stocks_data

def save_weekly_data(data: Dict, output_dir: str):
    """
    Save weekly data to JSON files.
    
    Args:
        data: Dictionary with all stocks' weekly data
        output_dir: Directory to save JSON files
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # Save individual stock files
    for stock_symbol, stock_data in data.items():
        stock_file = os.path.join(output_dir, f'{stock_symbol}_weekly_phase1.json')
        with open(stock_file, 'w', encoding='utf-8') as f:
            json.dump(stock_data, f, indent=2, ensure_ascii=False)
        print(f"Saved: {stock_file}")
    
    # Save combined file
    combined_file = os.path.join(output_dir, 'all_stocks_weekly_phase1.json')
    with open(combined_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    print(f"Saved combined data: {combined_file}")
    
    # Generate summary statistics
    summary = {
        'extraction_summary': {
            'total_stocks': len(data),
            'extraction_time': datetime.now().isoformat(),
            'stocks_processed': list(data.keys())
        },
        'weekly_summary': {}
    }
    
    for stock_symbol, stock_data in data.items():
        weekly_data = stock_data['weekly_data']
        total_days = sum(len(week['daily_returns']) for week in weekly_data.values())
        total_weeks = len(weekly_data)
        
        # Calculate overall performance
        all_returns = []
        for week_data in weekly_data.values():
            for day_data in week_data['daily_returns']:
                all_returns.append(day_data['return_rate'])
        
        if all_returns:
            avg_daily_return = sum(all_returns) / len(all_returns)
            max_daily_return = max(all_returns)
            min_daily_return = min(all_returns)
            
            # Calculate cumulative return
            cumulative_return = 1.0
            for ret in all_returns:
                cumulative_return *= (1 + ret)
            cumulative_return -= 1
        else:
            avg_daily_return = max_daily_return = min_daily_return = cumulative_return = 0
        
        summary['weekly_summary'][stock_symbol] = {
            'total_weeks': total_weeks,
            'total_trading_days': total_days,
            'avg_days_per_week': total_days / total_weeks if total_weeks > 0 else 0,
            'overall_performance': {
                'cumulative_return': cumulative_return,
                'cumulative_return_percent': cumulative_return * 100,
                'avg_daily_return': avg_daily_return,
                'avg_daily_return_percent': avg_daily_return * 100,
                'max_daily_return_percent': max_daily_return * 100,
                'min_daily_return_percent': min_daily_return * 100
            }
        }
    
    summary_file = os.path.join(output_dir, 'weekly_phase1_summary.json')
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    print(f"Saved summary: {summary_file}")

def main():
    """Main execution function."""
    # Configuration
    test_results_dir = "/Users/<USER>/Code/Multi_Agent_Optimization/test_results"
    output_dir = os.path.join(test_results_dir, "weekly_phase1_data")
    
    print("Extracting weekly Phase 1 data from all log files...")
    print("="*70)
    
    # Process all log files
    all_data = process_all_logs_weekly(test_results_dir)
    
    if not all_data:
        print("No weekly Phase 1 data found in any log files.")
        return
    
    print(f"\nProcessed {len(all_data)} stocks with weekly Phase 1 data")
    
    # Save data to JSON files
    print("\nSaving weekly data to JSON files...")
    save_weekly_data(all_data, output_dir)
    
    print(f"\nAll weekly Phase 1 data saved to: {output_dir}")
    print("="*70)

if __name__ == "__main__":
    main()