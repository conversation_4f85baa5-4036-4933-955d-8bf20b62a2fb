# 基础设施层组件

本目录包含了贡献度评估系统的基础设施层组件，实现了低耦合高聚合的架构设计。

## 组件概览

### 1. 事件总线系统 (`event_bus.py`)

提供解耦的事件发布-订阅机制：

- **核心功能**：
  - 事件发布和订阅
  - 事件过滤
  - 异步事件处理
  - 订阅管理

- **主要类**：
  - `IEventBus`: 事件总线接口
  - `EventBus`: 事件总线实现
  - `Event`: 事件数据结构
  - `EventFilter`: 事件过滤器

- **特性**：
  - 线程安全
  - 支持同步和异步发布
  - 错误隔离（单个回调失败不影响其他回调）
  - 灵活的事件过滤

### 2. 配置管理器 (`configuration_manager.py`)

提供集中化的配置管理功能：

- **核心功能**：
  - 多层级配置
  - 环境变量覆盖
  - 配置验证和类型转换
  - 配置数据模型

- **主要类**：
  - `IConfigurationManager`: 配置管理器接口
  - `ConfigurationManager`: 配置管理器实现
  - `AssessmentConfig`: 评估配置数据模型
  - `OPROConfig`: OPRO配置数据模型

- **特性**：
  - 支持JSON配置文件
  - 环境变量自动覆盖（前缀：`ASSESSMENT_`）
  - 类型安全的配置访问
  - 配置验证和错误报告

### 3. 服务注册表和依赖注入 (`service_registry.py`)

提供服务实例管理和依赖注入功能：

- **核心功能**：
  - 单例和瞬态服务注册
  - 自动依赖注入
  - 循环依赖检测
  - 服务生命周期管理

- **主要类**：
  - `IServiceRegistry`: 服务注册表接口
  - `ServiceRegistry`: 服务注册表实现
  - `ServiceContainer`: 服务容器
  - `ServiceScope`: 服务作用域

- **特性**：
  - 基于类型注解的自动依赖分析
  - 支持工厂方法和实例注册
  - 线程安全的服务解析
  - 循环依赖检测和报告

### 4. 统一错误处理框架 (`error_handler.py`)

提供集中化的错误处理功能：

- **核心功能**：
  - 结构化异常层次
  - 错误恢复和重试机制
  - 错误上下文和追踪
  - 与事件总线集成

- **主要类**：
  - `IErrorHandler`: 错误处理器接口
  - `ErrorHandler`: 错误处理器实现
  - `AssessmentError`: 基础异常类
  - `RetryStrategy`: 重试策略

- **异常层次**：
  - `ConfigurationError`: 配置错误
  - `ValidationError`: 验证错误
  - `NetworkError`: 网络错误
  - `DatabaseError`: 数据库错误
  - `BusinessLogicError`: 业务逻辑错误
  - `ExternalServiceError`: 外部服务错误
  - `SystemError`: 系统错误

- **特性**：
  - 自动错误分类
  - 可配置的重试策略
  - 错误恢复机制
  - 装饰器支持

## 设计原则

### 低耦合高聚合

1. **高聚合**：
   - 每个组件专注于单一职责
   - 相关功能集中在同一模块
   - 避免上帝类反模式

2. **低耦合**：
   - 基于接口的依赖注入
   - 事件驱动的组件通信
   - 最小化全局状态

### 架构模式

- **依赖注入模式**：通过构造函数注入依赖
- **观察者模式**：事件总线实现发布-订阅
- **策略模式**：错误恢复和重试策略
- **装饰器模式**：错误处理装饰器

## 使用示例

### 基本使用

```python
from contribution_assessment.infrastructure import (
    EventBus, ConfigurationManager, ServiceRegistry, ErrorHandler
)

# 创建基础设施组件
config_manager = ConfigurationManager(config_dict={"key": "value"})
event_bus = EventBus()
error_handler = ErrorHandler(event_bus=event_bus)

# 创建服务容器
container = ServiceContainer()
container.configure_services(lambda registry: (
    registry.register_singleton(ConfigurationManager, instance=config_manager),
    registry.register_singleton(EventBus, instance=event_bus),
    registry.register_singleton(ErrorHandler, instance=error_handler)
))

service_provider = container.build_service_provider()
```

### 集成示例

参见 `example_integration.py` 文件，展示了所有组件如何协同工作。

## 测试

所有组件都有完整的单元测试：

- `test_event_bus.py`: 事件总线测试（12个测试）
- `test_configuration_manager.py`: 配置管理器测试（23个测试）
- `test_service_registry.py`: 服务注册表测试（22个测试）
- `test_error_handler.py`: 错误处理框架测试（31个测试）

运行所有测试：

```bash
python -m pytest contribution_assessment/tests/ -v
```

## 性能特性

- **线程安全**：所有组件都是线程安全的
- **异步支持**：事件总线支持异步处理
- **内存效率**：单例模式减少内存占用
- **错误隔离**：单个组件错误不影响整体系统

## 扩展性

- **插件化**：通过服务注册表轻松添加新组件
- **配置化**：通过配置管理器支持不同环境
- **事件驱动**：通过事件总线实现松耦合扩展
- **错误处理**：通过错误处理框架统一异常管理

## 最佳实践

1. **使用依赖注入**：避免硬编码依赖
2. **事件驱动通信**：使用事件总线解耦组件
3. **配置外部化**：将配置从代码中分离
4. **统一错误处理**：使用错误处理框架管理异常
5. **接口优先**：基于接口编程，便于测试和扩展