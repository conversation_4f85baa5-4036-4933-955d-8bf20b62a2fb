#!/usr/bin/env python3
"""
周期循环管理器 (Weekly Cycle Manager) - 清理版

实现"周 > 阶段"的层级结构，每个"周"作为一个完整的优化周期，
内部包含固定的四个阶段的优化流程。

完全使用新架构组件，无任何回退逻辑。

架构设计：
- 周 (Week): 作为顶层循环单位
- 阶段 (Phase): 每个周内部的四个执行步骤
  - 阶段1: 性能评估与问题识别
  - 阶段2: 优化策略生成与应用  
  - 阶段3: 优化效果验证
  - 阶段4: 结果固化与系统更新
"""

import os
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

# 导入新架构组件
from .refactored_assessor import RefactoredContributionAssessor
from .services.phase_coordinator import WeeklyResult as PhaseCoordinatorWeeklyResult

# 导入跨周状态管理器
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from portfolio_state_manager import PortfolioStateManager
from .dto.assessment_dto import AssessmentRequest


class WeekPhase(Enum):
    """周内阶段枚举"""
    PHASE_1_EVALUATION = "phase_1_evaluation"          # 阶段1: 性能评估与问题识别
    PHASE_2_OPTIMIZATION = "phase_2_optimization"      # 阶段2: 优化策略生成与应用
    PHASE_3_VERIFICATION = "phase_3_verification"      # 阶段3: 优化效果验证
    PHASE_4_CONSOLIDATION = "phase_4_consolidation"    # 阶段4: 结果固化与系统更新


@dataclass
class WeekPhaseResult:
    """周内阶段执行结果"""
    phase: WeekPhase
    success: bool
    execution_time: float
    data: Dict[str, Any]
    error_message: Optional[str] = None
    timestamp: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，处理枚举序列化"""
        return {
            "phase": self.phase.value,  # 转换枚举为字符串
            "success": self.success,
            "execution_time": self.execution_time,
            "data": self.data,
            "error_message": self.error_message,
            "timestamp": self.timestamp
        }


@dataclass
class WeekResult:
    """单周执行结果"""
    week_number: int
    success: bool
    total_execution_time: float
    phases: List[WeekPhaseResult]
    agents_optimized: List[str]
    performance_improvement: Dict[str, float]
    timestamp: str
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "week_number": self.week_number,
            "success": self.success,
            "total_execution_time": self.total_execution_time,
            "phases": [phase.to_dict() for phase in self.phases],  # 使用自定义to_dict方法
            "agents_optimized": self.agents_optimized,
            "performance_improvement": self.performance_improvement,
            "timestamp": self.timestamp
        }


@dataclass
class CycleResult:
    """多周期执行结果"""
    total_weeks: int
    successful_weeks: int
    total_execution_time: float
    weeks: List[WeekResult]
    overall_improvement: Dict[str, float]
    timestamp: str
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "total_weeks": self.total_weeks,
            "successful_weeks": self.successful_weeks,
            "success_rate": self.successful_weeks / max(1, self.total_weeks) * 100,
            "total_execution_time": self.total_execution_time,
            "weeks": [week.to_dict() for week in self.weeks],
            "overall_improvement": self.overall_improvement,
            "timestamp": self.timestamp
        }


class WeeklyCycleManager:
    """
    周期循环管理器
    
    负责管理多周的优化循环，每周内部执行四个阶段的优化流程。
    完全基于新架构，无回退逻辑。
    """
    
    def __init__(self, 
                 assessor: RefactoredContributionAssessor,
                 config: Dict[str, Any],
                 logger: Optional[logging.Logger] = None):
        """
        初始化周期循环管理器
        
        Args:
            assessor: 重构版本的贡献度评估器
            config: 配置字典
            logger: 日志记录器
        """
        self.assessor = assessor
        self.config = config
        self.logger = logger or logging.getLogger(__name__)
        
        # 初始化日常投资组合跟踪器
        self.portfolio_tracker = None
        # 修复配置读取逻辑，确保enable_daily_tracking能够正确继承系统级配置
        enable_daily_tracking = config.get("enable_daily_tracking", config.get("enable_portfolio_tracking", True))
        
        # 检查是否已经有现成的tracker实例
        existing_tracker = config.get("portfolio_tracker")
        if existing_tracker:
            self.portfolio_tracker = existing_tracker
            self.logger.info("📊 使用现有的投资组合跟踪器实例")
        elif enable_daily_tracking:
            try:
                sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                from portfolio_state_tracker import PortfolioStateTracker
                persistence_path = config.get("tracker_persistence_path")
                self.portfolio_tracker = PortfolioStateTracker(persistence_path)
                self.logger.info(f"📊 启用日常投资组合跟踪器 (持久化: {persistence_path or '内存模式'})")
            except Exception as e:
                self.logger.error(f"❌ 投资组合跟踪器初始化失败: {e}")
                self.portfolio_tracker = None
        
        # 记录最终状态
        if self.portfolio_tracker:
            self.logger.info("✅ 投资组合跟踪器初始化成功")
        else:
            self.logger.warning("⚠️ 投资组合跟踪器未启用，将使用旧版状态传递机制")
        
        # 统计信息
        self.stats = {
            "total_cycles": 0,
            "successful_cycles": 0,
            "failed_cycles": 0,
            "total_execution_time": 0.0,
            "phases_executed": 0,
            "successful_phases": 0,
            "failed_phases": 0
        }
        
        # 初始化组件
        self._initialize_components()
    
    def _initialize_components(self):
        """初始化子组件"""
        try:
            # 使用新架构的WeeklyOptimizationService
            from .services.weekly_optimization_service import WeeklyOptimizationService
            from .llm_interface import LLMInterface
            
            # 获取PhaseCoordinator
            phase_coordinator = getattr(self.assessor, 'phase_coordinator', None)
            if not phase_coordinator:
                raise RuntimeError("无法获取PhaseCoordinator")
            
            # 创建LLM接口（如果有LLM provider）
            llm_interface = None
            if hasattr(self.assessor, 'llm_provider') and self.assessor.llm_provider:
                llm_interface = LLMInterface(provider=self.assessor.llm_provider, logger=self.logger)
            
            # 创建WeeklyOptimizationService
            self.optimization_executor = WeeklyOptimizationService(
                phase_coordinator=phase_coordinator,
                llm_interface=llm_interface,
                logger=self.logger
            )
            self.logger.info("✅ 使用WeeklyOptimizationService作为优化执行器")
            
            self.logger.info("✅ WeeklyCycleManager组件初始化成功")
            
        except Exception as e:
            self.logger.error(f"❌ WeeklyCycleManager组件初始化失败: {e}")
            raise RuntimeError(f"组件初始化失败: {e}")
    
    def run_weekly_cycles(self, 
                         num_weeks: int = 1,
                         agents_per_week: int = 2,
                         target_agents: Optional[List[str]] = None,
                         max_coalitions: Optional[int] = None,
                         weekly_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        运行多周的优化循环
        
        Args:
            num_weeks: 运行周数
            agents_per_week: 每周优化的智能体数量
            target_agents: 目标智能体列表
            max_coalitions: 最大联盟数
            weekly_config: 周期配置
            
        Returns:
            循环执行结果字典
        """
        start_time = time.time()
        self.stats["total_cycles"] += 1
        
        self.logger.info(f"🚀 开始{num_weeks}周的优化循环")
        
        weeks: List[WeekResult] = []
        overall_improvement = {}
        
        try:
            for week_num in range(1, num_weeks + 1):
                self.logger.info(f"\n📅 === 第{week_num}周优化循环 ===")
                
                # 记录所有智能体的提示词状态（在周开始前）
                self._log_all_agent_prompts(week_num, detailed=True)
                
                # 执行单周优化 - 使用新的6阶段工作流
                week_result = self._execute_single_week_with_phase_coordinator(
                    week_number=week_num,
                    target_agents=target_agents,
                    max_coalitions=max_coalitions,
                    weekly_config=weekly_config
                )
                
                weeks.append(week_result)
                
                # 合并性能改进数据
                for agent, improvement in week_result.performance_improvement.items():
                    if agent not in overall_improvement:
                        overall_improvement[agent] = 0.0
                    overall_improvement[agent] += improvement
            
            # 统计成功周数
            successful_weeks = sum(1 for week in weeks if week.success)
            execution_time = time.time() - start_time
            
            # 更新统计
            if successful_weeks > 0:
                self.stats["successful_cycles"] += 1
            else:
                self.stats["failed_cycles"] += 1
            
            self.stats["total_execution_time"] += execution_time
            
            # 构建结果
            cycle_result = CycleResult(
                total_weeks=num_weeks,
                successful_weeks=successful_weeks,
                total_execution_time=execution_time,
                weeks=weeks,
                overall_improvement=overall_improvement,
                timestamp=datetime.now().isoformat()
            )
            
            # 保存结果
            self._save_cycle_result(cycle_result, weekly_config)
            
            self.logger.info(f"🏁 {num_weeks}周优化循环完成，成功{successful_weeks}周")
            
            return {
                "success": successful_weeks > 0,
                "cycle_result": cycle_result.to_dict(),
                "architecture": "weekly_cycle_manager"
            }
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.stats["failed_cycles"] += 1
            self.stats["total_execution_time"] += execution_time
            
            self.logger.error(f"❌ 周期循环执行失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "execution_time": execution_time,
                "partial_results": [week.to_dict() for week in weeks],
                "architecture": "weekly_cycle_manager"
            }
    
    def _execute_single_week(self,
                            week_number: int,
                            agents_per_week: int,
                            target_agents: Optional[List[str]],
                            max_coalitions: Optional[int],
                            weekly_config: Optional[Dict[str, Any]]) -> WeekResult:
        """
        执行单周的优化循环
        
        Args:
            week_number: 周数
            agents_per_week: 每周优化的智能体数量
            target_agents: 目标智能体列表
            max_coalitions: 最大联盟数
            weekly_config: 周期配置
            
        Returns:
            单周执行结果
        """
        start_time = time.time()
        phases: List[WeekPhaseResult] = []
        agents_optimized: List[str] = []
        performance_improvement: Dict[str, float] = {}
        
        try:
            # 阶段1: 性能评估与问题识别
            phase1_result = self._execute_phase_1_evaluation(
                week_number=week_number,
                target_agents=target_agents,
                max_coalitions=max_coalitions,
                weekly_config=weekly_config
            )
            phases.append(phase1_result)
            
            if not phase1_result.success:
                raise RuntimeError(f"阶段1失败: {phase1_result.error_message}")
            
            # 阶段2: 优化策略生成与应用
            phase2_result = self._execute_phase_2_optimization(
                evaluation_result=phase1_result.data,
                agents_per_week=agents_per_week,
                weekly_config=weekly_config
            )
            phases.append(phase2_result)
            
            if phase2_result.success:
                agents_optimized = phase2_result.data.get("optimized_agents", [])
            
            # 阶段3: 优化效果验证
            phase3_result = self._execute_phase_3_verification(
                optimization_result=phase2_result.data,
                agents_optimized=agents_optimized,
                weekly_config=weekly_config
            )
            phases.append(phase3_result)
            
            if phase3_result.success:
                performance_improvement = phase3_result.data.get("performance_improvement", {})
            
            # 阶段4: 结果固化与系统更新
            phase4_result = self._execute_phase_4_consolidation(
                week_number=week_number,
                phases_data=[p.data for p in phases],
                weekly_config=weekly_config
            )
            phases.append(phase4_result)
            
            # 判断整周是否成功
            all_phases_success = all(phase.success for phase in phases)
            execution_time = time.time() - start_time
            
            # 更新统计
            self.stats["phases_executed"] += len(phases)
            self.stats["successful_phases"] += sum(1 for p in phases if p.success)
            self.stats["failed_phases"] += sum(1 for p in phases if not p.success)
            
            return WeekResult(
                week_number=week_number,
                success=all_phases_success,
                total_execution_time=execution_time,
                phases=phases,
                agents_optimized=agents_optimized,
                performance_improvement=performance_improvement,
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"❌ 第{week_number}周执行失败: {e}")
            
            return WeekResult(
                week_number=week_number,
                success=False,
                total_execution_time=execution_time,
                phases=phases,
                agents_optimized=agents_optimized,
                performance_improvement=performance_improvement,
                timestamp=datetime.now().isoformat()
            )
    
    def _execute_phase_1_evaluation(self,
                                   week_number: int,
                                   target_agents: Optional[List[str]],
                                   max_coalitions: Optional[int],
                                   weekly_config: Optional[Dict[str, Any]]) -> WeekPhaseResult:
        """执行阶段1: 性能评估与问题识别"""
        phase_start = time.time()
        
        try:
            self.logger.info(f"  📊 阶段1: 性能评估与问题识别 (第{week_number}周)")
            
            # 使用RefactoredContributionAssessor进行评估
            evaluation_result = self.assessor.run(
                target_agents=target_agents,
                max_coalitions=max_coalitions
            )
            
            if not evaluation_result.get("success", False):
                raise RuntimeError(f"评估失败: {evaluation_result.get('error', 'Unknown error')}")
            
            execution_time = time.time() - phase_start
            
            return WeekPhaseResult(
                phase=WeekPhase.PHASE_1_EVALUATION,
                success=True,
                execution_time=execution_time,
                data={
                    "evaluation_result": evaluation_result,
                    "week_number": week_number
                },
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            execution_time = time.time() - phase_start
            self.logger.error(f"    ❌ 阶段1失败: {e}")
            
            return WeekPhaseResult(
                phase=WeekPhase.PHASE_1_EVALUATION,
                success=False,
                execution_time=execution_time,
                data={},
                error_message=str(e),
                timestamp=datetime.now().isoformat()
            )
    
    def _execute_phase_2_optimization(self,
                                     evaluation_result: Dict[str, Any],
                                     agents_per_week: int,
                                     weekly_config: Optional[Dict[str, Any]]) -> WeekPhaseResult:
        """执行阶段2: 优化策略生成与应用"""
        phase_start = time.time()
        
        try:
            self.logger.info("  🔧 阶段2: 优化策略生成与应用")
            
            # 使用优化执行器进行OPRO优化
            if hasattr(self.optimization_executor, 'execute_optimization'):
                optimization_result = self.optimization_executor.execute_optimization(
                    evaluation_result=evaluation_result,
                    max_agents=agents_per_week,
                    config=weekly_config or {}
                )
            else:
                # 备用：使用assessor的OPRO功能
                optimization_result = self.assessor.run_with_weekly_optimization(
                    weekly_config=weekly_config
                )
            
            optimized_agents = optimization_result.get("optimized_agents", [])
            
            execution_time = time.time() - phase_start
            
            return WeekPhaseResult(
                phase=WeekPhase.PHASE_2_OPTIMIZATION,
                success=True,
                execution_time=execution_time,
                data={
                    "optimization_result": optimization_result,
                    "optimized_agents": optimized_agents
                },
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            execution_time = time.time() - phase_start
            self.logger.error(f"    ❌ 阶段2失败: {e}")
            
            return WeekPhaseResult(
                phase=WeekPhase.PHASE_2_OPTIMIZATION,
                success=False,
                execution_time=execution_time,
                data={},
                error_message=str(e),
                timestamp=datetime.now().isoformat()
            )
    
    def _execute_phase_3_verification(self,
                                     optimization_result: Dict[str, Any],
                                     agents_optimized: List[str],
                                     weekly_config: Optional[Dict[str, Any]]) -> WeekPhaseResult:
        """执行阶段3: 优化效果验证"""
        phase_start = time.time()
        
        try:
            self.logger.info("  ✅ 阶段3: 优化效果验证")
            
            # 重新运行评估以验证优化效果
            verification_result = self.assessor.run(
                target_agents=agents_optimized
            )
            
            # 计算性能改进
            performance_improvement = {}
            if verification_result.get("success", False):
                shapley_values = verification_result.get("shapley_values", {})
                for agent in agents_optimized:
                    if agent in shapley_values:
                        # 简化的改进计算
                        performance_improvement[agent] = shapley_values[agent]
            
            execution_time = time.time() - phase_start
            
            return WeekPhaseResult(
                phase=WeekPhase.PHASE_3_VERIFICATION,
                success=True,
                execution_time=execution_time,
                data={
                    "verification_result": verification_result,
                    "performance_improvement": performance_improvement
                },
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            execution_time = time.time() - phase_start
            self.logger.error(f"    ❌ 阶段3失败: {e}")
            
            return WeekPhaseResult(
                phase=WeekPhase.PHASE_3_VERIFICATION,
                success=False,
                execution_time=execution_time,
                data={},
                error_message=str(e),
                timestamp=datetime.now().isoformat()
            )
    
    def _execute_phase_4_consolidation(self,
                                      week_number: int,
                                      phases_data: List[Dict[str, Any]],
                                      weekly_config: Optional[Dict[str, Any]]) -> WeekPhaseResult:
        """执行阶段4: 结果固化与系统更新"""
        phase_start = time.time()
        
        try:
            self.logger.info("  💾 阶段4: 结果固化与系统更新")
            
            # 保存本周结果
            week_data = {
                "week_number": week_number,
                "phases": phases_data,
                "timestamp": datetime.now().isoformat()
            }
            
            # 保存到文件
            results_dir = weekly_config.get("results_dir", "results/weekly_cycles") if weekly_config else "results/weekly_cycles"
            os.makedirs(results_dir, exist_ok=True)
            
            week_file = os.path.join(results_dir, f"week_{week_number}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
            with open(week_file, 'w', encoding='utf-8') as f:
                json.dump(week_data, f, indent=2, ensure_ascii=False)
            
            execution_time = time.time() - phase_start
            
            return WeekPhaseResult(
                phase=WeekPhase.PHASE_4_CONSOLIDATION,
                success=True,
                execution_time=execution_time,
                data={
                    "saved_file": week_file,
                    "consolidated_data": week_data
                },
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            execution_time = time.time() - phase_start
            self.logger.error(f"    ❌ 阶段4失败: {e}")
            
            return WeekPhaseResult(
                phase=WeekPhase.PHASE_4_CONSOLIDATION,
                success=False,
                execution_time=execution_time,
                data={},
                error_message=str(e),
                timestamp=datetime.now().isoformat()
            )
    
    def _save_cycle_result(self, cycle_result: CycleResult, weekly_config: Optional[Dict[str, Any]]):
        """保存循环结果"""
        try:
            results_dir = weekly_config.get("results_dir", "results/weekly_cycles") if weekly_config else "results/weekly_cycles"
            os.makedirs(results_dir, exist_ok=True)
            
            cycle_file = os.path.join(
                results_dir, 
                f"cycle_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            )
            
            with open(cycle_file, 'w', encoding='utf-8') as f:
                json.dump(cycle_result.to_dict(), f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"📁 循环结果已保存: {cycle_file}")
            
        except Exception as e:
            self.logger.error(f"❌ 保存循环结果失败: {e}")
    
    def _log_all_agent_prompts(self, week_number: int, detailed: bool = True) -> None:
        """
        记录所有智能体的提示词状态
        
        Args:
            week_number: 当前周数
            detailed: 是否输出详细信息（包含提示词预览）
        """
        try:
            # 获取所有智能体
            agents = getattr(self.assessor, 'agents', {})
            if not agents:
                self.logger.warning(f"第{week_number}周开始前：未找到智能体实例")
                return
            
            self.logger.info(f"📋 第{week_number}周开始前 - 智能体提示词状态总览")
            self.logger.info("=" * 80)
            
            # 统计信息
            total_agents = len(agents)
            opro_enabled_count = 0
            optimized_count = 0
            
            # 遍历所有智能体
            for agent_id, agent_instance in agents.items():
                # 检查智能体是否有OPRO支持
                if hasattr(agent_instance, 'format_prompt_log'):
                    # 使用OPROBaseAgent的格式化方法
                    prompt_log = agent_instance.format_prompt_log(include_preview=detailed)
                    self.logger.info(prompt_log)
                    
                    # 统计OPRO相关信息
                    if hasattr(agent_instance, 'opro_enabled') and agent_instance.opro_enabled:
                        opro_enabled_count += 1
                        
                    if hasattr(agent_instance, '_opro_stats'):
                        opro_stats = agent_instance._opro_stats
                        if opro_stats.get("successful_optimizations", 0) > 0:
                            optimized_count += 1
                            
                elif hasattr(agent_instance, 'get_prompt_template'):
                    # 对于没有OPRO支持的智能体，显示基础信息
                    prompt = agent_instance.get_prompt_template()
                    prompt_length = len(prompt)
                    preview = prompt[:100] + "..." if len(prompt) > 100 else prompt
                    
                    self.logger.info(f"📝 {agent_id}: 静态提示词 ({prompt_length}字符)")
                    if detailed:
                        self.logger.info(f"   💬 预览: {preview}")
                else:
                    self.logger.info(f"⚠️ {agent_id}: 无法获取提示词信息")
            
            # 输出总览统计
            self.logger.info("=" * 80)
            self.logger.info(f"📊 提示词状态统计:")
            self.logger.info(f"   总智能体数: {total_agents}")
            self.logger.info(f"   OPRO启用: {opro_enabled_count}/{total_agents}")
            self.logger.info(f"   已优化智能体: {optimized_count}/{total_agents}")
            
            # 如果有优化过的智能体，显示优化传递状态
            if optimized_count > 0:
                self.logger.info(f"✅ 检测到 {optimized_count} 个智能体已完成OPRO优化，提示词将在本周继续使用")
            else:
                self.logger.info("🔄 所有智能体使用默认提示词，等待首次OPRO优化")
                
            self.logger.info("=" * 80)
            
        except Exception as e:
            self.logger.error(f"记录智能体提示词状态失败: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "total_cycles": self.stats["total_cycles"],
            "successful_cycles": self.stats["successful_cycles"],
            "failed_cycles": self.stats["failed_cycles"],
            "success_rate": (self.stats["successful_cycles"] / max(1, self.stats["total_cycles"])) * 100,
            "total_execution_time": self.stats["total_execution_time"],
            "phases_executed": self.stats["phases_executed"],
            "successful_phases": self.stats["successful_phases"],
            "failed_phases": self.stats["failed_phases"],
            "phase_success_rate": (self.stats["successful_phases"] / max(1, self.stats["phases_executed"])) * 100
        }
    
    def _execute_single_week_with_phase_coordinator(self,
                                                   week_number: int,
                                                   target_agents: Optional[List[str]],
                                                   max_coalitions: Optional[int],
                                                   weekly_config: Optional[Dict[str, Any]]) -> WeekResult:
        """
        使用PhaseCoordinator的6阶段工作流执行单周优化
        
        Args:
            week_number: 周数
            target_agents: 目标智能体列表
            max_coalitions: 最大联盟数
            weekly_config: 周期配置
            
        Returns:
            WeekResult: 单周执行结果
        """
        start_time = time.time()
        
        try:
            self.logger.info(f"🚀 开始第 {week_number} 周优化周期 (6阶段工作流)")
            
            # 获取PhaseCoordinator
            phase_coordinator = getattr(self.assessor, 'phase_coordinator', None)
            if not phase_coordinator:
                raise RuntimeError("无法获取PhaseCoordinator")
            
            # 确保有完整的agents配置
            agents = getattr(self.assessor, 'agents', {})
            if not agents and hasattr(self.assessor, '_create_agents'):
                self.logger.info("创建智能体配置...")
                agents = self.assessor._create_agents()
                # 重要：将创建的智能体保存回assessor以保持OPRO优化状态
                self.assessor.agents = agents
                self.logger.info("✅ 智能体已保存到assessor，后续周期将重用优化后的智能体")
            
            self.logger.info(f"可用智能体: {list(agents.keys())}")

            # 在新的一周开始时，清空所有智能体的周度IO数据
            self.logger.info(f"🔄 清理第{week_number}周的旧IO数据...")
            for agent_id, agent_instance in agents.items():
                if hasattr(agent_instance, 'clear_weekly_io_data'):
                    agent_instance.clear_weekly_io_data()
                    self.logger.debug(f"  - 已清空 {agent_id} 的IO数据")
            self.logger.info("✅ 所有智能体的周度IO数据已清空")

            # 投资组合状态管理（新旧兼容）
            config_dict = {**self.config, "current_week_number": week_number}
            
            # 确保 current_week_number 正确传递到配置中（修复日期设置问题）
            config_dict["current_week_number"] = week_number
            
            # 优先使用portfolio_tracker，回退到旧的状态管理
            if self.portfolio_tracker:
                # 通知tracker开始新的一周
                self.portfolio_tracker.start_new_week(week_number)
                # 传递tracker实例
                config_dict["portfolio_tracker"] = self.portfolio_tracker
                self.logger.info(f"📊 第{week_number}周使用日常投资组合跟踪器")
            else:
                # 使用旧的跨周状态传递机制
                previous_week_state = PortfolioStateManager.load_previous_week_state(week_number)
                if previous_week_state:
                    # 确保周收益率被正确重置：将当前周收益率保存为上周收益率，重置当前周收益率
                    previous_week_state["last_week_return"] = previous_week_state.get("weekly_return", 0.0)
                    previous_week_state["weekly_return"] = 0.0
                    self.logger.info(f"🔄 第{week_number}周继承状态: 累计收益率={previous_week_state.get('cumulative_return', 0):.4f}, 上周收益率={previous_week_state.get('last_week_return', 0):.4f}")
                config_dict["inherited_portfolio_state"] = previous_week_state
                self.logger.info(f"🔄 第{week_number}周使用旧版跨周状态传递")
            
            # 构建评估请求
            assessment_request = AssessmentRequest(
                request_id=f"weekly_assessment_{week_number}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                agents=agents,
                target_agents=target_agents or [],
                max_coalitions=max_coalitions or 50,
                enable_opro=weekly_config.get("enable_opro", True) if weekly_config else True,
                config=config_dict,
                opro_config=weekly_config.get("opro_config", {}) if weekly_config else {},
                max_concurrent_api_calls=weekly_config.get("max_concurrent_api_calls", 3) if weekly_config else 3
            )
            
            # 通过PhaseCoordinator执行完整的6阶段工作流
            phase_result = phase_coordinator.execute_weekly_assessment_cycle(
                request=assessment_request,
                week_num=week_number
            )
            
            # 转换PhaseCoordinator的结果为WeeklyCycleManager的WeekResult格式
            execution_time = time.time() - start_time
            
            # 构建WeekPhaseResult列表（为了兼容现有接口）
            phases = []
            if phase_result.coalition_result:
                phases.append(WeekPhaseResult(
                    phase=WeekPhase.PHASE_1_EVALUATION,  # 重用现有枚举
                    success=phase_result.coalition_result.success,
                    execution_time=phase_result.coalition_result.execution_time,
                    data={"coalition_count": len(phase_result.coalition_result.valid_coalitions)},
                    timestamp=datetime.now().isoformat()
                ))
            
            # 提取优化的智能体信息
            agents_optimized = phase_result.optimized_agents or []
            
            # 提取性能改进数据
            performance_improvement = {}
            if phase_result.performance_metrics:
                for agent_id, score in phase_result.performance_metrics.items():
                    performance_improvement[agent_id] = float(score)
            
            # 保存第week_number周结束时的投资组合状态（跨周状态传递）
            if phase_result.success:
                try:
                    # 优先使用portfolio_tracker的状态（新方法）
                    if self.portfolio_tracker:
                        week_end_state = self.portfolio_tracker.get_week_end_return_state(week_number)
                        if week_end_state:
                            PortfolioStateManager.save_week_end_state(week_number, week_end_state)
                            self.logger.info(f"✅ 通过跟踪器保存第{week_number}周末状态: 累计收益率={week_end_state.get('cumulative_return', 0):.4f}")
                        else:
                            self.logger.warning(f"⚠️ 跟踪器中未找到第{week_number}周的状态数据")
                    # 回退到传统状态提取方法
                    elif hasattr(self.assessor, 'trading_simulator') and hasattr(self.assessor.trading_simulator, 'env'):
                        current_portfolio_state = PortfolioStateManager.extract_portfolio_state_from_trading_env(
                            self.assessor.trading_simulator.env
                        )
                        if current_portfolio_state:
                            PortfolioStateManager.save_week_end_state(week_number, current_portfolio_state)
                            self.logger.info(f"✅ 通过交易环境保存第{week_number}周末状态")
                    elif phase_result.simulation_result and hasattr(phase_result.simulation_result, 'final_portfolio_state'):
                        # 如果simulation_result中有final_portfolio_state，使用它
                        PortfolioStateManager.save_week_end_state(week_number, phase_result.simulation_result.final_portfolio_state)
                        self.logger.info(f"✅ 通过仿真结果保存第{week_number}周末状态")
                    else:
                        self.logger.warning(f"⚠️ 无法找到有效的状态来源保存第{week_number}周状态")
                except Exception as e:
                    self.logger.warning(f"⚠️ 保存第{week_number}周投资组合状态失败: {e}")
            
            # 更新统计
            self.stats["phases_executed"] += 6  # 6个阶段
            if phase_result.success:
                self.stats["successful_phases"] += 6
            
            return WeekResult(
                week_number=week_number,
                success=phase_result.success,
                total_execution_time=execution_time,
                phases=phases,
                agents_optimized=agents_optimized,
                performance_improvement=performance_improvement,
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"❌ 第{week_number}周6阶段工作流执行失败: {e}")
            
            # 更新失败统计
            self.stats["failed_phases"] += 6
            
            return WeekResult(
                week_number=week_number,
                success=False,
                total_execution_time=execution_time,
                phases=[],
                agents_optimized=[],
                performance_improvement={},
                timestamp=datetime.now().isoformat()
            )