2025-07-28 00:44:28,024 - __main__ - INFO - ======================================================================
2025-07-28 00:44:28,024 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-28 00:44:28,024 - __main__ - INFO - ======================================================================
2025-07-28 00:44:28,024 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-28 00:44:28,024 - __main__ - INFO - 开始模拟联盟: {'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'}
2025-07-28 00:44:28,025 - __main__ - INFO - 📅 当前周数: 1
2025-07-28 00:44:28,025 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-28 00:44:28,025 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-28 00:44:28,025 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-28 00:44:28,154 - __main__ - INFO - 📅 设置当前模拟周数: 1 (来自参数)
2025-07-28 00:44:28,156 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 00:45:06,061 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 00:45:06,062 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 00:45:34,830 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 00:45:34,831 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 00:45:34,831 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000
2025-07-28 00:45:52,939 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 00:45:52,940 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 00:45:52,940 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 00:45:52,940 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 00:45:52,940 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 00:45:52,940 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们综合考虑了来自分析层和展望层的智能体输出。NAA显示市场情绪较为积极，尽管苹果在中国市场面临挑战，但科技巨头的积极展望和对音乐制作软件市场的增长预测提供了正面影响。TAA显示短期趋势可能为看跌，技术指标如RSI、MACD和移动平均线均显示出潜在的看跌信号。FAA显示苹果的估值合理，财务健康得分较高，竞争地位领先，长期趋势向好。展望层智能体中，BOA和BeOA的信号方向相反，BOA给出中性展望，而BeOA给出看跌展望。NOA认为市场应保持中性观望。综合分析，尽管市场情绪和基本面分析显示出一定的乐观，但技术分析显示短期趋势可能看跌，且展望层智能体信号不一致。因此，我们倾向于采取中性观望的策略。**最终决策：保持不变**。', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-02'}
2025-07-28 00:45:52,940 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 00:45:52,945 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 00:47:07,282 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 00:47:07,284 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 00:47:45,084 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 00:47:45,085 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 00:47:45,086 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-03, 累计收益=0.0000, 周收益=0.0000
2025-07-28 00:48:03,007 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 00:48:03,007 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 00:48:03,007 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 00:48:03,007 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 00:48:03,007 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 00:48:03,007 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 00:48:03,007 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 00:48:03,007 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析日期：2025-01-03\n\n展望层智能体信号分析：\n- BOA（看涨展望）提供了市场看涨的信号，主要基于分析层智能体的输出，包括美国第六巡回上诉法院推翻了联邦通信委员会的网中立性规则，对苹果公司有利；比尔·盖茨和乔治·索罗斯等亿万富翁买入苹果股票，对苹果股价有正面影响；以及人工智能股票占标普500指数的28.8%，对科技行业有利。\n- BeOA（看跌展望）没有提供强烈的看跌信号，主要因素包括苹果公司就Siri隐私违规问题达成9500万美元和解，对公司形象有负面影响，以及苹果iPhone在中国市场份额下降，对公司业绩有负面影响。\n- NOA（中性展望）认为当前市场展现出中性趋势，没有明显的看涨或看跌信号。\n\n决策理由：由于BOA提供了看涨信号，而BeOA没有提供强烈的看跌信号，且NOA认为市场中性，因此优先考虑BOA的建议。\n\n风险评估：分析层智能体的NAA、TAA、FAA结果提供了对市场风险的综合评估，包括对苹果公司的影响、技术面分析以及基本面分析。尽管存在一些负面因素，但整体风险可控。\n\n最终决策：买入\n\n', 'action': 'buy', 'trading_actions': {'AAPL': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-03'}
2025-07-28 00:48:03,008 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'AAPL': 1.0}
2025-07-28 00:48:03,008 - __main__ - INFO - 📈 第2天交易动作: {'AAPL': 1.0}
2025-07-28 00:48:03,010 - __main__ - INFO - 💰 第2天净值变化: 收益率=-0.0033, 净值=$996733.05
2025-07-28 00:48:03,011 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 00:48:30,049 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 00:48:30,050 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 00:49:12,276 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 00:49:12,280 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 00:49:12,280 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-06, 累计收益=-0.0033, 周收益=-0.0033
2025-07-28 00:49:28,559 - __main__ - WARNING - 未找到明确的最终决策声明，保持action=hold
2025-07-28 00:49:28,561 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 00:49:28,561 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 00:49:28,561 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 00:49:28,561 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 00:49:28,561 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在2025-01-06的分析中，NAA显示市场情绪偏向乐观，多篇新闻提到沃伦·巴菲特的投资策略和苹果股价上涨预测，对AAPL股价产生正面影响。TAA分析显示AAPL在当天呈现看涨趋势，技术指标支持这一趋势。FAA分析表明AAPL的估值合理，财务健康，竞争地位领先。展望层智能体中，BOA和BeOA的信号方向相反，但NOA认为应中性观望。由于BOA和BeOA信号相反，我们优先考虑NOA的建议。因此，最终决策为保持不变。', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-06'}
2025-07-28 00:49:28,561 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 00:49:28,567 - __main__ - INFO - 💰 第3天净值变化: 收益率=0.0214, 净值=$1018035.35
2025-07-28 00:49:28,568 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 00:50:30,168 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 00:50:30,169 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 00:51:05,535 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 00:51:05,537 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 00:51:05,537 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-07, 累计收益=0.0180, 周收益=0.0180
2025-07-28 00:51:18,840 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 00:51:18,841 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 00:51:18,841 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 00:51:18,841 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 00:51:18,841 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 00:51:18,841 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析日期：2025-01-07\n\n持仓情况：AAPL 4430股\n\n分析层智能体输出：\nNAA情绪评分：0.2，显示市场情绪较为中性。新闻分析中，苹果公司AI新闻摘要存在误导性可能对声誉产生负面影响，但富士康强劲的第四季度业绩和分析师对苹果的积极评估可能产生正面影响。TAA技术分析显示AAPL趋势看跌，RSI低于50，MACD线在信号线下方，移动平均线均向下。FAA基本面分析显示AAPL估值合理，财务健康得分8.5，竞争地位领先。\n展望层智能体输出：BOA给出看涨展望，BeOA给出看跌展望，NOA认为市场展望中性。\n综合分析：尽管TAA显示技术面看跌，但FAA显示基本面良好，且NOA认为市场中性。因此，最终决策倾向于保持不变。\n**最终决策：保持不变**', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-07'}
2025-07-28 00:51:18,841 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 00:51:18,846 - __main__ - INFO - 💰 第4天净值变化: 收益率=-0.0023, 净值=$1015708.21
2025-07-28 00:51:18,849 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 00:51:58,957 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 00:51:58,958 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 00:52:47,038 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 00:52:47,039 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 00:52:47,039 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-08, 累计收益=0.0157, 周收益=0.0157
2025-07-28 00:53:05,208 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 00:53:05,209 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 00:53:05,209 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 00:53:05,209 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 00:53:05,209 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 00:53:05,210 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析过程如下：\n\n展望层智能体输出分析：\n- BOA（看涨展望）分析显示，尽管TAA显示市场短期内可能继续下跌，但结合NAA和FAA的输出，市场对苹果公司的股票持积极态度，且整体市场情绪相对乐观。这表明BOA倾向于看涨。\n- BeOA（看跌展望）分析显示，市场情绪偏向乐观，但并非极度乐观，这可能意味着市场存在一定的下行风险。BeOA倾向于看跌。\n- NOA（中性展望）分析认为，当前市场表现出一定的波动性和不确定性，既有看涨因素，也有看跌因素。这表明NOA认为市场应保持中性观望。\n\n决策理由：\n- 由于BOA和BeOA给出了相反的信号，我们优先考虑NOA的建议，即保持中性观望。\n- 分析层的NAA、TAA、FAA结果支持了这一决策，NAA显示市场对苹果公司股票持积极态度，TAA显示短期内市场可能继续下跌，FAA显示苹果公司的财务状况良好，竞争地位领先。\n\n风险评估：\n- 市场情绪和公司基本面分析显示，虽然存在一定的下行风险，但整体风险可控。\n\n最终决策声明：**最终决策：保持不变**', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-08'}
2025-07-28 00:53:05,210 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 00:53:05,213 - __main__ - INFO - 💰 第5天净值变化: 收益率=-0.0120, 净值=$1003490.71
2025-07-28 00:53:05,213 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-28 00:53:05,213 - __main__ - INFO - ============================================================
2025-07-28 00:53:05,213 - __main__ - INFO - 第 1 周性能分析 - 联盟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-28 00:53:05,214 - __main__ - INFO - 周总收益率: 0.0038
2025-07-28 00:53:05,214 - __main__ - INFO - 周夏普比率: 0.9720
2025-07-28 00:53:05,214 - __main__ - INFO - 交易天数: 5
2025-07-28 00:53:05,214 - __main__ - INFO - ============================================================
2025-07-28 01:24:59,409 - __main__ - INFO - ======================================================================
2025-07-28 01:24:59,409 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-28 01:24:59,409 - __main__ - INFO - ======================================================================
2025-07-28 01:24:59,409 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-28 01:24:59,410 - __main__ - INFO - 开始模拟联盟: {'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'}
2025-07-28 01:24:59,410 - __main__ - INFO - 📅 当前周数: 2
2025-07-28 01:24:59,410 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-28 01:24:59,410 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-28 01:24:59,410 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-01-08 (第2周)
2025-07-28 01:24:59,410 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-01-08, end_date=2025-01-15
2025-07-28 01:24:59,410 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-28 01:24:59,494 - __main__ - INFO - 📅 设置当前模拟周数: 2 (来自参数)
2025-07-28 01:24:59,495 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 01:25:37,868 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 01:25:37,868 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 01:26:18,850 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 01:26:18,851 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 01:26:18,851 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-08, 累计收益=0.0000, 周收益=0.0000
2025-07-28 01:26:35,663 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 01:26:35,664 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 01:26:35,664 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 01:26:35,664 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 01:26:35,664 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 01:26:35,664 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '今日分析结果显示，市场对苹果公司（AAPL）持积极态度，尤其是受到“Magnificent Seven”股票推荐和科技行业兴趣增加的影响。然而，技术分析显示AAPL股票呈现下跌趋势，尽管存在超卖情况和反弹可能性。基本面分析认为AAPL的估值合理，财务健康，竞争地位领先。展望层智能体中，BOA给出中性展望，BeOA给出看跌展望，而NOA认为市场应保持中性观望。由于BOA和BeOA的信号方向相反且强度相当，我们优先考虑NOA的建议，即保持中性观望。因此，最终决策为保持不变。**最终决策：保持不变**', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-08'}
2025-07-28 01:26:35,665 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 01:26:35,668 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 01:26:35,669 - __main__ - INFO - TAA检测到无价格数据，建议跳过当前日期: 2025-01-09
2025-07-28 01:26:58,097 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 01:26:58,098 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 01:27:21,666 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 01:27:21,666 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 01:27:21,666 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-09, 累计收益=0.0000, 周收益=0.0000
2025-07-28 01:27:41,576 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 01:27:41,577 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 01:27:41,577 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 01:27:41,577 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 01:27:41,577 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 01:27:41,577 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': "分析日期：2025-01-09\n持仓：{'AAPL': 0}\n\n分析层智能体输出分析：\nNAA的分析结果显示市场情绪评分较低，但存在多种正面和负面因素，如苹果公司在印度尼西亚的工厂问题、Siri隐私政策的澄清、与微软在AI领域的竞争等。这些因素可能会对苹果公司的股价产生影响。TAA无法进行技术分析，因此未提供相关分析。FAA显示苹果公司的估值合理，财务健康得分高，竞争地位领先，长期趋势看好。\n\n展望层智能体输出分析：\nBOA和BeOA的信号方向相反，BOA看涨，BeOA看跌，NOA保持中性。这种情况下，我们优先考虑NOA的建议，认为市场目前保持中性。\n\n决策理由：由于市场展望中性，且苹果公司的基本面分析显示其财务状况良好，竞争地位领先，因此建议保持当前持仓不变。\n\n最终决策：保持不变\n风险评估：虽然存在一些负面因素，但苹果公司的整体基本面仍然强劲，因此风险可控。", 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-09'}
2025-07-28 01:27:41,577 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 01:27:41,584 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 01:28:05,628 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 01:28:05,628 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 01:28:44,065 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 01:28:44,067 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 01:28:44,067 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-10, 累计收益=0.0000, 周收益=0.0000
2025-07-28 01:29:04,759 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 01:29:04,760 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 01:29:04,760 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 01:29:04,760 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 01:29:04,760 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 01:29:04,760 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析过程如下：\n\n展望层智能体输出分析：\n- BOA（看涨展望）认为，尽管TAA显示AAPL当天可能处于看跌趋势，但结合其他分析层智能体的输出，市场整体情绪偏向乐观，因此倾向于看涨。\n- BeOA（看跌展望）指出，AAPL市场情绪虽然乐观，但存在看跌风险因素，如技术分析显示的市场可能处于看跌趋势，因此倾向于看跌。\n- NOA（中性展望）认为，AAPL市场情绪偏向乐观，但存在看跌因素，如苹果闹钟问题，因此建议中性观望。\n\n决策理由：由于BOA和BeOA给出的信号方向相反，我们优先考虑NOA的建议，即中性观望。\n\n风险评估：NAA显示AAPL市场情绪偏向乐观，但存在负面影响如苹果闹钟问题；TAA显示市场可能处于看跌趋势；FAA显示AAPL估值合理，财务健康，竞争地位领先。综合分析，AAPL存在一定的不确定性。\n\n最终决策：保持不变\n', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-10'}
2025-07-28 01:29:04,760 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 01:29:04,763 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 01:29:41,075 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 01:29:41,076 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 01:29:53,237 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 01:29:53,237 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 01:29:53,237 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-13, 累计收益=0.0000, 周收益=0.0000
2025-07-28 01:30:09,707 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 01:30:09,708 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 01:30:09,708 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 01:30:09,708 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 01:30:09,708 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 01:30:09,708 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们综合考虑了来自分析层和展望层的智能体输出。NAA的分析显示，尽管苹果面临一些挑战，如在中国市场的份额下降和估值过高，但整体情绪评分较低，且沃伦·巴菲特的股息收入可能对投资者产生正面影响。TAA的技术分析显示AAPL在2025-01-13的技术面是看跌的，支撑位可能在$230左右，阻力位可能在$235-$237之间。FAA的基本面分析显示苹果的估值合理，财务健康得分较高，竞争地位领先。展望层智能体中，BOA给出了看涨信号，而BeOA给出了看跌信号，NOA则保持中性。由于BOA和BeOA的信号方向相反，我们优先考虑NOA的建议，认为市场目前处于中性观望状态。因此，**最终决策：保持不变**。', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-13'}
2025-07-28 01:30:09,709 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 01:30:09,715 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 01:31:15,392 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 01:31:15,393 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 01:31:51,414 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 01:31:51,414 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 01:31:51,414 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-14, 累计收益=0.0000, 周收益=0.0000
2025-07-28 01:32:02,897 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 01:32:02,898 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 01:32:02,899 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 01:32:02,899 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 01:32:02,899 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 01:32:02,899 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 01:32:02,899 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 01:32:02,899 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析过程如下：展望层智能体BOA给出了看涨信号，认为尽管短期内市场有看跌趋势，但基于长期市场趋势和潜在积极因素，市场展望为看涨。BeOA给出了看跌信号，基于分析层智能体的输出，市场展望为看跌，指出Arm Holdings的计划、欧盟对大型科技公司的审查等因素可能对市场产生负面影响。NOA给出了中性信号，认为当前市场展现出一定的波动性，既有看涨因素也有看跌因素。综合分析，BOA和BeOA给出了相反的信号，而NOA的建议为中性，因此优先考虑NOA的建议。分析层智能体NAA、TAA和FAA的结果显示，市场存在一定的风险和机会，但不是决策主导因素。最终决策：买入。', 'action': 'buy', 'trading_actions': {'AAPL': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-14'}
2025-07-28 01:32:02,899 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'AAPL': 1.0}
2025-07-28 01:32:02,900 - __main__ - INFO - 📈 第5天交易动作: {'AAPL': 1.0}
2025-07-28 01:32:02,904 - __main__ - INFO - 💰 第5天净值变化: 收益率=0.0134, 净值=$1013422.22
2025-07-28 01:32:02,904 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-28 01:32:02,904 - __main__ - INFO - ============================================================
2025-07-28 01:32:02,905 - __main__ - INFO - 第 2 周性能分析 - 联盟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-28 01:32:02,905 - __main__ - INFO - 周总收益率: 0.0134
2025-07-28 01:32:02,905 - __main__ - INFO - 周夏普比率: 7.7026
2025-07-28 01:32:02,905 - __main__ - INFO - 交易天数: 5
2025-07-28 01:32:02,905 - __main__ - INFO - ============================================================

2025-07-28 02:03:57,668 - __main__ - INFO - ======================================================================
2025-07-28 02:03:57,668 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-28 02:03:57,668 - __main__ - INFO - ======================================================================
2025-07-28 02:03:57,668 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-28 02:03:57,668 - __main__ - INFO - 开始模拟联盟: {'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'}
2025-07-28 02:03:57,668 - __main__ - INFO - 📅 当前周数: 3
2025-07-28 02:03:57,668 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-28 02:03:57,668 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-28 02:03:57,670 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-01-15 (第3周)
2025-07-28 02:03:57,670 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-01-15, end_date=2025-01-22
2025-07-28 02:03:57,670 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-28 02:03:57,757 - __main__ - INFO - 📅 设置当前模拟周数: 3 (来自参数)
2025-07-28 02:03:57,758 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 02:04:42,343 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 02:04:42,343 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 02:05:22,599 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 02:05:22,600 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 02:05:22,600 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-15, 累计收益=0.0000, 周收益=0.0000
2025-07-28 02:05:39,802 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 02:05:39,802 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 02:05:39,803 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 02:05:39,803 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 02:05:39,803 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 02:05:39,803 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '今日分析结果显示，市场情绪较为乐观，尤其是关于AI和科技股票的上涨预测，以及市场交易策略的提供。然而，对苹果股票的潜在冲击分析可能会对股价产生短期压力。展望层智能体中，BOA和BeOA给出了相反的信号，BOA倾向于看涨，而BeOA倾向于看跌。在这种情况下，NOA的中性展望起到了关键作用。技术分析显示AAPL显示出一定的下跌趋势，但具体的技术指标分析需要进一步计算。基本面分析认为AAPL的估值合理，财务健康得分较高，竞争地位领先。综合分析，虽然市场情绪和技术分析显示一定的看跌趋势，但基本面分析支持AAPL的长期增长潜力。因此，最终决策倾向于保持不变。**最终决策：保持不变**', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-15'}
2025-07-28 02:05:39,803 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 02:05:39,810 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 02:06:10,682 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 02:06:10,683 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 02:06:50,072 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 02:06:50,072 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 02:06:50,072 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-16, 累计收益=0.0000, 周收益=0.0000
2025-07-28 02:06:58,935 - __main__ - INFO - 🎯 找到最终决策声明: sell
2025-07-28 02:06:58,937 - __main__ - WARNING - 修正决策不一致: 声明sell vs 字段hold
2025-07-28 02:06:58,937 - __main__ - INFO - 一致性修正：hold -> sell
2025-07-28 02:06:58,937 - __main__ - INFO - 🤖 TRA 输出: action=sell, position_size=1.0
2025-07-28 02:06:58,937 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: sell (信心度: 0.00)
2025-07-28 02:06:58,937 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 02:06:58,938 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 02:06:58,938 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析过程如下：\n\n- NAA提供了积极的市场新闻，包括苹果公司探索新的信用卡合作伙伴、iPhone制造成本下降等，这些因素可能对苹果公司的股价产生积极影响。\n- TAA显示AAPL股票处于下跌趋势，技术指标如RSI、MACD和移动平均线均显示出下跌趋势，这表明短期内股票可能存在下跌风险。\n- FAA对苹果公司的估值评估为合理，财务健康分数为8.5，竞争地位领先，长期趋势看好。\n- BOA给出了看涨展望，认为苹果公司的股价还有上涨空间。\n- BeOA给出了看跌展望，指出苹果公司在中国市场面临挑战，这可能会影响其全球业务。\n- NOA认为应该中性观望。\n\n综合以上分析，虽然NAA和BOA给出了看涨信号，但TAA和BeOA给出了看跌信号，且TAA的分析信心度较高。因此，优先考虑TAA的建议，认为短期内AAPL股票可能存在下跌风险。最终决策：卖出。', 'action': 'sell', 'trading_actions': {'AAPL': -1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-16'}
2025-07-28 02:06:58,938 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'AAPL': -1.0}
2025-07-28 02:06:58,938 - __main__ - INFO - 📈 第2天交易动作: {'AAPL': -1.0}
2025-07-28 02:06:58,944 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 02:07:37,898 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 02:07:37,900 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 02:08:13,301 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 02:08:13,302 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 02:08:13,302 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-17, 累计收益=0.0000, 周收益=0.0000
2025-07-28 02:08:35,916 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 02:08:35,916 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 02:08:35,917 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 02:08:35,917 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 02:08:35,917 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 02:08:35,918 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 02:08:35,918 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 02:08:35,918 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析过程如下：\n\n展望层智能体输出分析：\n- BOA（看涨展望）给出了较为中性的分析，没有强烈的看涨信号。\n- BeOA（看跌展望）给出了较为强烈的看跌信号，认为短期内市场将呈现看跌趋势。\n- NOA（中性展望）认为当前市场情绪评分较低，但存在多个正面和负面因素，因此市场展望保持中性。\n\n决策理由：\n- BeOA的看跌信号较为强烈，结合TAA（技术分析）的看跌趋势，以及FAA（基本面分析）的合理估值和良好的财务健康状况，我们认为市场短期内存在下跌风险。\n- 虽然BOA和NOA给出了中性或看涨信号，但考虑到市场情绪和风险控制，我们倾向于遵循BeOA的建议。\n\n风险评估：\n- 市场情绪较为悲观，存在多个负面因素，如TikTok面临美国禁令、美国半导体制裁等。\n- 技术分析显示股票处于超卖状态，但短期内可能存在下跌趋势。\n- 基本面分析显示公司估值合理，财务状况良好，但市场风险仍然存在。\n\n最终决策：买入\n\n基于以上分析，我们决定买入股票。', 'action': 'buy', 'trading_actions': {'AAPL': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-17'}
2025-07-28 02:08:35,918 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'AAPL': 1.0}
2025-07-28 02:08:35,918 - __main__ - INFO - 📈 第3天交易动作: {'AAPL': 1.0}
2025-07-28 02:08:35,925 - __main__ - INFO - 💰 第3天净值变化: 收益率=-0.0021, 净值=$997903.93
2025-07-28 02:08:35,931 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 02:08:35,931 - __main__ - INFO - TAA检测到无价格数据，建议跳过当前日期: 2025-01-20
2025-07-28 02:08:53,838 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 02:08:53,839 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 02:09:28,856 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 02:09:28,857 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 02:09:28,858 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-20, 累计收益=-0.0021, 周收益=-0.0021
2025-07-28 02:09:45,419 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 02:09:45,421 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 02:09:45,421 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 02:09:45,421 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 02:09:45,421 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 02:09:45,421 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析日期：2025-01-20\n持仓：AAPL 4323\n\n分析层智能体输出：\nNAA分析结果显示，与AAPL相关的新闻情绪偏向乐观，特朗普的声明和macOS 15.4的更新被视为正面消息。然而，TAA无法进行技术分析，FAA显示AAPL的估值合理，财务健康得分高，竞争地位领先。展望层智能体输出中，BOA和BeOA的信号方向相反，BOA倾向于看涨，而BeOA倾向于看跌。NOA的建议是中性观望。\n\n展望层决策权重分配显示，如果BOA和BeOA都给出强烈信号但方向相反，应优先考虑NOA的建议。由于NOA的建议是中性观望，我们优先考虑这一建议。\n\n风险评估方面，NAA的分析信心度为0.9，FAA的信心度未知，TAA的信心度为1.0。整体来看，风险可控。\n\n最终决策：保持不变\n', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-20'}
2025-07-28 02:09:45,421 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 02:09:45,425 - __main__ - INFO - 💰 第4天净值变化: 收益率=0.0059, 净值=$1003799.13
2025-07-28 02:09:45,428 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 02:10:57,796 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 02:10:57,797 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 02:11:32,267 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 02:11:32,267 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 02:11:32,267 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-21, 累计收益=0.0038, 周收益=0.0038
2025-07-28 02:11:46,044 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 02:11:46,045 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 02:11:46,045 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 02:11:46,045 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 02:11:46,045 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 02:11:46,045 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析过程如下：\n\n展望层智能体输出显示，BOA给出看涨信号，BeOA给出看跌信号，NOA认为市场中性。根据核心决策逻辑，当BOA和BeOA信号相反时，优先考虑NOA的建议。由于NOA认为市场中性，我们倾向于保持不变。\n\n分析层智能体NAA的输出显示，苹果公司面临Jefferies的罕见降级，分析师警告收入增长放缓、预测失误和iPhone需求下降。同时，台湾地震影响全球芯片供应，可能对苹果供应链产生负面影响。这些因素可能导致苹果股价下跌。然而，沃伦·巴菲特的三只股票在2025年有高达50%的上涨空间，分析师认为苹果的人工智能进步无法抵消中国市场放缓的影响，但服务、可穿戴设备和Vision Pro提供了增长潜力。这些因素可能对苹果股价产生积极影响。\n\nTAA的输出显示，AAPL的价格在2025-01-21经历了显著下跌，技术指标如RSI、MACD和移动平均线均显示看跌趋势。然而，技术评分仅为-0.5，分析信心度为0.7，表明技术分析结果并不十分明确。\n\nFAA的输出显示，苹果的估值合理，财务健康得分较高，竞争地位领先。这些因素可能对苹果股价产生积极影响。\n\n综合以上分析，虽然存在一些负面因素，但也存在积极因素。考虑到展望层智能体的中性展望和分析层智能体的不确定性，最终决策为保持不变。\n\n**最终决策：保持不变**', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-21'}
2025-07-28 02:11:46,045 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 02:11:46,049 - __main__ - INFO - 💰 第5天净值变化: 收益率=0.0131, 净值=$1016899.56
2025-07-28 02:11:46,049 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-28 02:11:46,049 - __main__ - INFO - ============================================================
2025-07-28 02:11:46,049 - __main__ - INFO - 第 3 周性能分析 - 联盟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-28 02:11:46,049 - __main__ - INFO - 周总收益率: 0.0169
2025-07-28 02:11:46,049 - __main__ - INFO - 周夏普比率: 9.4580
2025-07-28 02:11:46,049 - __main__ - INFO - 交易天数: 5
2025-07-28 02:11:46,049 - __main__ - INFO - ============================================================

2025-07-28 02:44:08,580 - __main__ - INFO - ======================================================================
2025-07-28 02:44:08,580 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-28 02:44:08,580 - __main__ - INFO - ======================================================================
2025-07-28 02:44:08,580 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-28 02:44:08,580 - __main__ - INFO - 开始模拟联盟: {'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'}
2025-07-28 02:44:08,580 - __main__ - INFO - 📅 当前周数: 4
2025-07-28 02:44:08,580 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-28 02:44:08,580 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-28 02:44:08,581 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-01-22 (第4周)
2025-07-28 02:44:08,581 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-01-22, end_date=2025-01-29
2025-07-28 02:44:08,581 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-28 02:44:08,663 - __main__ - INFO - 📅 设置当前模拟周数: 4 (来自参数)
2025-07-28 02:44:08,664 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 02:44:57,140 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 02:44:57,141 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 02:45:42,002 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 02:45:42,005 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 02:45:42,005 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-22, 累计收益=0.0000, 周收益=0.0000
2025-07-28 02:45:59,609 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 02:45:59,610 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 02:45:59,610 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 02:45:59,610 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 02:45:59,610 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 02:45:59,610 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析过程如下：\n\n展望层智能体输出分析：\n- BOA（看涨展望）给出了看涨信号，尽管存在一些负面因素，但整体市场情绪和苹果公司的基本面分析支持看涨展望。\n- BeOA（看跌展望）给出了看跌信号，主要基于苹果在中国市场的市场份额下降等因素。\n- NOA（中性展望）认为市场对苹果公司的看法呈现出一定的中性趋势。\n\n决策理由：\n由于BOA和BeOA给出了相反的信号，我们优先考虑NOA的建议，即保持中性观望。\n风险评估：\nNAA、TAA和FAA的分析结果提供了对市场情绪、技术面和基本面的综合评估，但它们不是决策的主导因素。\n最终决策：根据以上分析，我们选择保持中性观望。\n\n**最终决策：保持不变**', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-22'}
2025-07-28 02:45:59,611 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 02:45:59,615 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 02:46:57,782 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 02:46:57,783 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 02:47:28,690 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 02:47:28,692 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 02:47:28,692 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-23, 累计收益=0.0000, 周收益=0.0000
2025-07-28 02:47:47,857 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 02:47:47,858 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 02:47:47,858 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 02:47:47,858 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 02:47:47,858 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 02:47:47,859 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在2025-01-23的分析中，我们首先考虑了分析层智能体的输出。NAA提供了关于市场情绪和关键事件的详细分析，TAA提供了技术分析，而FAA提供了基本面分析。NAA显示了一些可能对苹果公司股价产生影响的新闻和事件，如苹果市值下降和特斯拉品牌价值下降。TAA显示AAPL在短期内可能存在反弹机会，但整体趋势仍然偏弱。FAA显示苹果的估值合理，财务健康，且在竞争中处于领先地位。\n\n展望层智能体的输出显示，BOA给出了看涨信号，认为苹果市值下降为其他公司创造了机会。BeOA给出了看跌信号，预计短期内AAPL股票价格将呈现看跌趋势。NOA则认为市场展望为中性，因为市场展现出一定的波动性，既有看涨因素也有看跌因素。\n\n根据决策规则，如果BOA和BeOA都给出强烈信号但方向相反，我们优先考虑NOA的建议。在这种情况下，由于NOA认为市场展望为中性，我们倾向于保持不变。此外，分析层的NAA、TAA、FAA结果作为风险评估的参考，但不是决策主导因素。\n\n最终决策：保持不变。', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-23'}
2025-07-28 02:47:47,859 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 02:47:47,864 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 02:48:11,958 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 02:48:11,959 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 02:48:50,916 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 02:48:50,917 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 02:48:50,917 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-24, 累计收益=0.0000, 周收益=0.0000
2025-07-28 02:49:10,005 - __main__ - INFO - 🎯 找到最终决策声明: sell
2025-07-28 02:49:10,006 - __main__ - WARNING - 修正决策不一致: 声明sell vs 字段hold
2025-07-28 02:49:10,007 - __main__ - INFO - 一致性修正：hold -> sell
2025-07-28 02:49:10,007 - __main__ - INFO - 🤖 TRA 输出: action=sell, position_size=1.0
2025-07-28 02:49:10,007 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: sell (信心度: 0.00)
2025-07-28 02:49:10,007 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 02:49:10,007 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 02:49:10,007 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析日期：2025-01-24\n\n分析层智能体输出分析：\nNAA情绪评分0.6，显示出市场情绪较为积极，但新闻摘要中包含多个积极因素，如Epic Games CEO的长期对抗苹果和谷歌的言论，以及分析师对苹果股票的积极分析。TAA技术分析显示AAPL处于短期下跌趋势，技术指标如RSI、MACD和移动平均线均显示出看跌信号。FAA基本面分析显示AAPL估值合理，财务健康得分较高，竞争地位领先。\n展望层智能体输出分析：\nBOA看涨展望，认为市场情绪积极；BeOA看跌展望，预计市场将呈现看跌趋势；NOA中性展望，认为市场情绪显示出一定的谨慎态度。\n综合分析，尽管市场情绪积极，但技术分析和基本面分析显示AAPL可能面临短期下跌风险。因此，优先考虑BeOA的看跌展望。\n最终决策：卖出\n\n风险评估：考虑到Epic Games CEO的言论可能对苹果产生负面影响，以及TAA显示的看跌趋势，风险评估为中等风险。', 'action': 'sell', 'trading_actions': {'AAPL': -1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-24'}
2025-07-28 02:49:10,007 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'AAPL': -1.0}
2025-07-28 02:49:10,007 - __main__ - INFO - 📈 第3天交易动作: {'AAPL': -1.0}
2025-07-28 02:49:10,010 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 02:50:27,713 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 02:50:27,716 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 02:51:07,231 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 02:51:07,233 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 02:51:07,233 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-27, 累计收益=0.0000, 周收益=0.0000
2025-07-28 02:51:34,318 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 02:51:34,318 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 02:51:34,318 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 02:51:34,318 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 02:51:34,318 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 02:51:34,318 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在2025-01-27的分析中，我们综合考虑了来自分析层和展望层的智能体输出。NAA提供了关于苹果公司新产品探索、市场增长预测以及行业影响的新闻摘要，这些信息对评估苹果公司的长期潜力具有积极意义。TAA的技术分析显示AAPL在分析日期当天表现出上涨趋势，尽管存在超买信号，但整体趋势仍然看涨。FAA的基本面分析表明苹果公司的估值合理，财务健康，竞争地位领先，这些都是积极的信号。展望层智能体中，BOA给出了看涨展望，而BeOA给出了看跌展望，NOA则认为市场中性。由于BOA和BeOA的信号相反，我们优先考虑NOA的中性展望。综合分析，我们得出结论，尽管存在一些不确定性，但整体市场展望偏向中性。因此，**最终决策：保持不变**。', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-27'}
2025-07-28 02:51:34,318 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 02:51:34,322 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 02:52:20,627 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 02:52:20,628 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 02:53:14,591 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 02:53:14,593 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 02:53:14,593 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-28, 累计收益=0.0000, 周收益=0.0000
2025-07-28 02:53:29,118 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 02:53:29,119 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 02:53:29,119 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 02:53:29,119 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 02:53:29,119 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 02:53:29,119 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 02:53:29,119 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 02:53:29,119 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析日期: 2025-01-28\n\n分析层智能体输出分析：\n\n- NAA情绪评分：0.3，表明市场情绪较为中性。新闻摘要中，苹果公司发布iOS 18.3更新对苹果公司正面影响，但深度探索公司遭受网络攻击和特朗普总统提议对台湾产品征收关税可能对苹果等公司产生负面影响。投资者对科技股的兴趣可能对相关股票产生正面影响。\n- TAA技术分析显示，当前市场趋势看涨，支撑位价格约为$230.00，阻力位价格约为$240.00。技术指标如RSI、MACD和移动平均线均支持看涨趋势。\n- FAA基本面分析显示，苹果公司的估值合理，财务健康得分高，竞争地位领先，长期增长前景良好。\n\n展望层智能体输出分析：\n- BOA看涨展望，认为市场整体展望积极。\n- BeOA看跌展望，指出存在潜在风险因素可能导致市场下跌。\n- NOA中性展望，认为市场整体展望中性，存在不确定性因素。\n\n综合分析，尽管存在一些潜在风险，但技术分析和基本面分析均支持看涨趋势。因此，倾向于执行买入操作。\n**最终决策：买入**', 'action': 'buy', 'trading_actions': {'AAPL': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-28'}
2025-07-28 02:53:29,119 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'AAPL': 1.0}
2025-07-28 02:53:29,120 - __main__ - INFO - 📈 第5天交易动作: {'AAPL': 1.0}
2025-07-28 02:53:29,131 - __main__ - INFO - 💰 第5天净值变化: 收益率=0.0128, 净值=$1012771.82
2025-07-28 02:53:29,131 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-28 02:53:29,132 - __main__ - INFO - ============================================================
2025-07-28 02:53:29,132 - __main__ - INFO - 第 4 周性能分析 - 联盟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-28 02:53:29,132 - __main__ - INFO - 周总收益率: 0.0128
2025-07-28 02:53:29,132 - __main__ - INFO - 周夏普比率: 7.6906
2025-07-28 02:53:29,132 - __main__ - INFO - 交易天数: 5
2025-07-28 02:53:29,132 - __main__ - INFO - ============================================================

2025-07-28 03:26:17,634 - __main__ - INFO - ======================================================================
2025-07-28 03:26:17,634 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-28 03:26:17,634 - __main__ - INFO - ======================================================================
2025-07-28 03:26:17,634 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-28 03:26:17,634 - __main__ - INFO - 开始模拟联盟: {'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'}
2025-07-28 03:26:17,634 - __main__ - INFO - 📅 当前周数: 5
2025-07-28 03:26:17,634 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-28 03:26:17,634 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-28 03:26:17,634 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-01-29 (第5周)
2025-07-28 03:26:17,634 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-01-29, end_date=2025-02-05
2025-07-28 03:26:17,634 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-28 03:26:17,711 - __main__ - INFO - 📅 设置当前模拟周数: 5 (来自参数)
2025-07-28 03:26:17,713 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 03:26:45,188 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 03:26:45,188 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 03:27:17,056 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 03:27:17,057 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 03:27:17,057 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-29, 累计收益=0.0000, 周收益=0.0000
2025-07-28 03:27:29,292 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 03:27:29,292 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 03:27:29,292 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 03:27:29,292 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 03:27:29,292 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 03:27:29,292 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们综合了来自分析层和展望层智能体的输出。NAA显示大部分新闻对AAPL持积极态度，尤其是与SpaceX和T-Mobile的合作，以及非技术投资带来的增长。然而，也存在对AI延迟和市场份额损失的担忧。TAA显示AAPL显示出上升趋势，技术指标如RSI、MACD和移动平均线均显示出看涨信号。FAA评估AAPL的估值合理，财务健康得分高，竞争地位领先。展望层智能体中，BOA给出看涨展望，而BeOA给出看跌展望，NOA认为应中性观望。考虑到BOA和BeOA的信号，我们优先考虑NOA的建议，因为它提供了一个平衡的观点。最终决策基于这些分析，我们得出**最终决策：保持不变**。', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-29'}
2025-07-28 03:27:29,293 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 03:27:29,298 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 03:28:01,338 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 03:28:01,339 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 03:28:34,412 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 03:28:34,414 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 03:28:34,414 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-30, 累计收益=0.0000, 周收益=0.0000
2025-07-28 03:28:51,550 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 03:28:51,550 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 03:28:51,550 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 03:28:51,550 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 03:28:51,550 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 03:28:51,550 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 03:28:51,550 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 03:28:51,551 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析过程如下：\n\n展望层智能体输出分析：\n- BOA（看涨展望）给出了积极的市场情绪和苹果公司财报的正面影响，但未给出具体信号。\n- BeOA（看跌展望）未提供具体分析。\n- NOA（中性展望）认为市场存在不确定性，建议中性观望。\n\n决策理由：\n- 分析层智能体NAA显示市场情绪积极，苹果公司财报超出预期，预计股价将上涨。\n- TAA显示技术指标支持看涨趋势，但存在市场波动性风险。\n- FAA显示苹果公司估值合理，财务状况良好，市场竞争力强。\n\n风险评估：\n- 市场情绪和财报分析支持看涨，但存在市场波动风险。\n\n最终决策：考虑到市场情绪积极，技术指标支持看涨，且苹果公司财务状况良好，**最终决策：买入**。', 'action': 'buy', 'trading_actions': {'AAPL': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-30'}
2025-07-28 03:28:51,551 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'AAPL': 1.0}
2025-07-28 03:28:51,551 - __main__ - INFO - 📈 第2天交易动作: {'AAPL': 1.0}
2025-07-28 03:28:51,555 - __main__ - INFO - 💰 第2天净值变化: 收益率=0.0015, 净值=$1001483.62
2025-07-28 03:28:51,558 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 03:29:38,343 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 03:29:38,343 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 03:30:18,306 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 03:30:18,307 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 03:30:18,307 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-31, 累计收益=0.0015, 周收益=0.0015
2025-07-28 03:30:25,584 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 03:30:25,585 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 03:30:25,585 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 03:30:25,585 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 03:30:25,585 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 03:30:25,585 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们综合考虑了来自分析层和展望层的智能体输出。NAA显示大部分新闻对AAPL的报道呈现正面或中性情绪，尤其是关于公司业绩和产品创新的报道，这表明市场对AAPL的业绩持乐观态度。TAA的技术分析显示AAPL可能处于下跌趋势，但具体的技术指标需要进一步确认。FAA的基本面分析显示AAPL的估值合理，财务健康，竞争地位领先，长期增长前景良好。展望层智能体中，BOA给出看涨展望，BeOA给出看跌展望，而NOA认为市场对AAPL的展望呈现中性。根据决策规则，由于BOA和BeOA给出的信号方向相反，我们优先考虑NOA的建议，即保持中性观望。因此，**最终决策：保持不变**。', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-01-31'}
2025-07-28 03:30:25,585 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 03:30:25,591 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 03:31:04,528 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 03:31:04,529 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 03:31:47,856 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 03:31:47,856 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 03:31:47,856 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-03, 累计收益=0.0016, 周收益=0.0016
2025-07-28 03:32:01,503 - __main__ - INFO - 🎯 找到最终决策声明: sell
2025-07-28 03:32:01,505 - __main__ - WARNING - 修正决策不一致: 声明sell vs 字段hold
2025-07-28 03:32:01,505 - __main__ - INFO - 一致性修正：hold -> sell
2025-07-28 03:32:01,505 - __main__ - INFO - 🤖 TRA 输出: action=sell, position_size=1.0
2025-07-28 03:32:01,505 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: sell (信心度: 0.00)
2025-07-28 03:32:01,505 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 03:32:01,505 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 03:32:01,505 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析日期：2025-02-03\n\n持仓：AAPL 4079\n\n分析层智能体输出：\nNAA分析结果显示情绪评分较低，市场情绪较为消极，同时提到了Apple Watch Series 10的用户反馈可能影响Apple的声誉和销量，以及美国对加拿大、墨西哥和中国征收关税可能影响Apple的供应链和成本。TAA分析显示短期趋势可能为看跌，技术指标如RSI、MACD和移动平均线可能显示看跌趋势。FAA分析显示Apple的估值评估为低估，财务健康得分为8.5，竞争地位领先。\n\n展望层智能体输出：BOA给出看涨展望，但分析层智能体显示的市场情绪和技术分析结果并不支持这一观点。BeOA给出看跌展望，与TAA的分析结果一致。NOA认为市场展望为中性。\n\n综合分析，尽管BOA给出看涨信号，但NAA和TAA的分析结果以及市场情绪均显示市场可能呈现看跌趋势。因此，优先考虑BeOA的建议，倾向于卖出操作。\n\n**最终决策：卖出**', 'action': 'sell', 'trading_actions': {'AAPL': -1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-03'}
2025-07-28 03:32:01,505 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'AAPL': -1.0}
2025-07-28 03:32:01,505 - __main__ - INFO - 📈 第4天交易动作: {'AAPL': -1.0}
2025-07-28 03:32:01,509 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 03:32:39,713 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 03:32:39,715 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 03:33:09,383 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 03:33:09,384 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 03:33:09,385 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-04, 累计收益=0.0016, 周收益=0.0016
2025-07-28 03:33:27,736 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 03:33:27,736 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 03:33:27,736 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 03:33:27,736 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 03:33:27,737 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 03:33:27,737 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析过程如下：\n\n展望层智能体输出显示，BOA给出看涨信号，BeOA给出看跌信号，NOA认为市场中性。根据核心决策逻辑，当BOA和BeOA信号相反时，优先考虑NOA的建议。由于NOA认为市场中性，我们倾向于保持不变。\n\n分析层智能体NAA显示，市场情绪较为谨慎，但并未出现明显的单边趋势。TAA显示AAPL可能处于看跌趋势，但技术指标也显示出超卖状态。FAA显示AAPL被低估，财务健康得分较高，竞争地位领先。\n\n综合以上分析，尽管存在看涨和看跌的因素，但市场整体表现出一定的波动性和不确定性。因此，最终决策为保持不变。\n\n**最终决策：保持不变**', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-04'}
2025-07-28 03:33:27,737 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 03:33:27,741 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-28 03:33:27,741 - __main__ - INFO - ============================================================
2025-07-28 03:33:27,741 - __main__ - INFO - 第 5 周性能分析 - 联盟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-28 03:33:27,741 - __main__ - INFO - 周总收益率: 0.0016
2025-07-28 03:33:27,741 - __main__ - INFO - 周夏普比率: 6.5900
2025-07-28 03:33:27,742 - __main__ - INFO - 交易天数: 5
2025-07-28 03:33:27,742 - __main__ - INFO - ============================================================

2025-07-28 04:05:22,885 - __main__ - INFO - ======================================================================
2025-07-28 04:05:22,885 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-28 04:05:22,885 - __main__ - INFO - ======================================================================
2025-07-28 04:05:22,885 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-28 04:05:22,885 - __main__ - INFO - 开始模拟联盟: {'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'}
2025-07-28 04:05:22,885 - __main__ - INFO - 📅 当前周数: 6
2025-07-28 04:05:22,885 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-28 04:05:22,885 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-28 04:05:22,887 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-02-05 (第6周)
2025-07-28 04:05:22,887 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-02-05, end_date=2025-02-12
2025-07-28 04:05:22,887 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-28 04:05:22,965 - __main__ - INFO - 📅 设置当前模拟周数: 6 (来自参数)
2025-07-28 04:05:22,966 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 04:06:08,423 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 04:06:08,424 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 04:06:43,167 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 04:06:43,168 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 04:06:43,168 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-05, 累计收益=0.0000, 周收益=0.0000
2025-07-28 04:06:54,300 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 04:06:54,301 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 04:06:54,302 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 04:06:54,302 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 04:06:54,302 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 04:06:54,302 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析过程如下：\n\n展望层智能体输出显示，BOA给出看涨信号，BeOA给出看跌信号，NOA认为市场中性。由于BOA和BeOA信号相反，我们优先考虑NOA的建议，即保持中性观望。\n\n分析层智能体NAA的输出显示，Apple Inc. (AAPL) 提高了其所有iPhone型号的AppleCare+服务的月度订阅费用，并取消了在店内提前购买保修服务。分析师对Apple股票的看涨预测可能会提振投资者信心。然而，中国国家市场监督管理总局正在调查苹果的App Store政策和费用，同时中美贸易紧张局势加剧，这可能对苹果的全球业务产生负面影响。\n\nTAA的输出显示，AAPL经历了明显的下跌趋势，技术指标如RSI接近超卖，MACD看跌，移动平均线看跌，表明可能存在看跌趋势。\n\nFAA的输出显示，AAPL被评估为低估，财务健康得分为8.5，竞争地位领先，长期趋势向好。\n\n综合以上分析，尽管存在一些积极的因素，但市场的不确定性以及技术分析显示的看跌趋势使得我们倾向于保持中性观望。\n\n**最终决策：保持不变**', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-05'}
2025-07-28 04:06:54,302 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 04:06:54,305 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 04:07:48,892 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 04:07:48,892 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 04:08:32,107 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 04:08:32,108 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 04:08:32,108 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-06, 累计收益=0.0000, 周收益=0.0000
2025-07-28 04:08:55,726 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 04:08:55,727 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 04:08:55,727 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 04:08:55,727 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 04:08:55,727 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 04:08:55,727 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': "分析日期：2025-02-06\n\n持仓：{'AAPL': 0}\n\n分析层智能体输出分析：\n\n- NAA分析结果显示情绪评分高（0.8），市场情绪积极，尤其是SiTime的财报、苹果M5芯片的量产以及DeepSeek的AI模型推动中国科技股上涨等因素对AAPL产生积极影响。\n- TAA分析显示AAPL股价呈现小幅下跌趋势，技术指标如RSI、MACD和移动平均线均显示短期趋势可能看跌，技术评分-0.5，分析信心度0.7。\n- FAA分析显示AAPL估值低估，财务健康得分8.5，竞争地位领先，长期趋势看涨。\n\n展望层智能体输出分析：\n- BOA给出看涨信号，认为市场情绪积极，对AAPL产生正面影响。\n- BeOA给出看跌信号，认为技术分析显示AAPL股价可能看跌。\n- NOA认为市场中性，建议保持观望。\n\n综合分析，尽管NAA和BOA给出看涨信号，但TAA和BeOA显示看跌趋势，且技术分析信心度较低。因此，优先考虑NOA的建议，认为应保持中性观望。\n\n**最终决策：保持不变**", 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-06'}
2025-07-28 04:08:55,728 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 04:08:55,732 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 04:09:59,825 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 04:09:59,826 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 04:10:30,180 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 04:10:30,181 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 04:10:30,181 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-07, 累计收益=0.0000, 周收益=0.0000
2025-07-28 04:10:45,240 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 04:10:45,241 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 04:10:45,241 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 04:10:45,241 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 04:10:45,241 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 04:10:45,241 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': "分析日期：2025-02-07\n持仓：{'AAPL': 0}\n\n分析层智能体输出：\nNAA分析结果显示，苹果公司（AAPL）的音乐制作软件市场预计将增长，新的iPhone SE即将发布，全球游戏市场预计将增长。同时，苹果公司面临来自英国政府的压力，有分析称现在是时候出售苹果股票了。技术分析显示，AAPL在短期内显示出下跌趋势，技术评分-0.5。基本面分析显示，AAPL被评估为低估，财务健康得分为8.5，竞争地位领先。\n展望层智能体输出：BOA给出看涨展望，BeOA给出看跌展望，NOA认为市场中性。\n\n综合分析，尽管NAA和TAA显示出了市场的不确定性和短期内的下跌趋势，FAA显示AAPL被低估且具有领先地位。展望层智能体中，BOA和BeOA给出了相反的信号，而NOA认为市场中性。根据决策规则，当BOA和BeOA信号相反时，优先考虑NOA的建议。因此，最终决策为保持不变。\n**最终决策：保持不变**", 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-07'}
2025-07-28 04:10:45,242 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 04:10:45,257 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 04:11:22,904 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 04:11:22,904 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 04:12:06,638 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 04:12:06,640 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 04:12:06,640 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-10, 累计收益=0.0000, 周收益=0.0000
2025-07-28 04:12:21,395 - __main__ - INFO - 🎯 找到最终决策声明: sell
2025-07-28 04:12:21,395 - __main__ - WARNING - 修正决策不一致: 声明sell vs 字段hold
2025-07-28 04:12:21,395 - __main__ - INFO - 一致性修正：hold -> sell
2025-07-28 04:12:21,395 - __main__ - INFO - 🤖 TRA 输出: action=sell, position_size=1.0
2025-07-28 04:12:21,395 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: sell (信心度: 0.00)
2025-07-28 04:12:21,395 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 04:12:21,395 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 04:12:21,395 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们综合了来自分析层和展望层智能体的输出。NAA显示苹果公司正在增加对其家庭机器人团队的招聘，这可能预示着公司在智能家居领域的重大发展，但同时也有来自特斯拉和T-Mobile的竞争，以及中国对苹果的反垄断调查等负面因素。TAA显示AAPL价格呈现下跌趋势，技术指标如RSI、MACD和移动平均线均显示出看跌信号。FAA评估苹果公司的估值低估，财务健康得分较高，竞争地位领先。展望层智能体中，BOA和BeOA给出了相反的信号，BOA认为市场情绪积极，而BeOA则认为股价可能面临下行压力。NOA认为市场情绪中性，略偏向乐观。综合分析，尽管存在一些积极因素，但技术分析和市场情绪显示出的看跌信号更为强烈，因此我们倾向于执行卖出操作。**最终决策：卖出**', 'action': 'sell', 'trading_actions': {'AAPL': -1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-10'}
2025-07-28 04:12:21,395 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'AAPL': -1.0}
2025-07-28 04:12:21,395 - __main__ - INFO - 📈 第4天交易动作: {'AAPL': -1.0}
2025-07-28 04:12:21,400 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 04:13:25,019 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 04:13:25,020 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 04:13:59,124 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 04:13:59,126 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 04:13:59,126 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-11, 累计收益=0.0000, 周收益=0.0000
2025-07-28 04:14:16,793 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 04:14:16,798 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 04:14:16,799 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 04:14:16,799 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 04:14:16,799 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 04:14:16,799 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析过程如下：\n\n展望层智能体输出显示，BOA给出看涨信号，BeOA给出看跌信号，NOA认为市场中性。由于BOA和BeOA信号方向相反，我们优先考虑NOA的建议，即市场中性观望。NAA分析显示，苹果公司与台积电的业务关系可能对苹果产生重大影响，但未来不确定性增加。TAA分析表明，AAPL可能显示出看跌趋势，但技术指标分析需要进一步计算。FAA分析显示，AAPL被低估，财务健康得分较高，竞争地位领先。综合分析，尽管存在看涨和看跌因素，但市场目前呈现出中性展望。因此，最终决策为保持不变。\n\n**最终决策：保持不变**', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-11'}
2025-07-28 04:14:16,799 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 04:14:16,802 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-28 04:14:16,802 - __main__ - INFO - ============================================================
2025-07-28 04:14:16,802 - __main__ - INFO - 第 6 周性能分析 - 联盟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-28 04:14:16,802 - __main__ - INFO - 周总收益率: 0.0000
2025-07-28 04:14:16,802 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-28 04:14:16,802 - __main__ - INFO - 交易天数: 5
2025-07-28 04:14:16,802 - __main__ - INFO - ============================================================

2025-07-28 04:45:57,853 - __main__ - INFO - ======================================================================
2025-07-28 04:45:57,853 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-28 04:45:57,853 - __main__ - INFO - ======================================================================
2025-07-28 04:45:57,853 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-28 04:45:57,853 - __main__ - INFO - 开始模拟联盟: {'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'}
2025-07-28 04:45:57,853 - __main__ - INFO - 📅 当前周数: 7
2025-07-28 04:45:57,853 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-28 04:45:57,853 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-28 04:45:57,854 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-02-12 (第7周)
2025-07-28 04:45:57,854 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-02-12, end_date=2025-02-19
2025-07-28 04:45:57,854 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-28 04:45:57,929 - __main__ - INFO - 📅 设置当前模拟周数: 7 (来自参数)
2025-07-28 04:45:57,930 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 04:47:07,590 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 04:47:07,591 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 04:47:50,551 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 04:47:50,552 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 04:47:50,552 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-12, 累计收益=0.0000, 周收益=0.0000
2025-07-28 04:48:23,743 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 04:48:23,745 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 04:48:23,745 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 04:48:23,745 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 04:48:23,745 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 04:48:23,745 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': "分析日期：2025-02-12\n\n持仓：{'AAPL': 0}\n\n分析层智能体输出分析：\n\n- NAA：情绪评分0.3，显示出市场对AAPL的情绪较为中性，但略微偏向乐观。新闻分析显示AAPL的新闻对股票的影响较为中性，但与阿里巴巴的合作可能对股票产生一定的正面影响。\n- TAA：技术分析结果显示AAPL股价显示出一定的上升趋势，技术指标如RSI、MACD和移动平均线均支持看涨趋势。\n- FAA：基本面分析显示AAPL的估值合理，财务健康评分高，竞争地位领先。\n\n展望层智能体输出分析：\n\n- BOA：看涨展望，认为市场对AAPL的正面情绪和合作可能对股票产生正面影响。\n- BeOA：看跌展望，认为市场情绪和长期趋势可能导致AAPL股价下跌。\n- NOA：中性展望，认为市场对AAPL的展望呈现中性。\n\n决策理由：尽管技术分析和基本面分析支持看涨，但看跌展望提醒我们注意潜在的市场风险。考虑到市场情绪和长期趋势，我们选择中性观望。\n\n最终决策：保持不变\n\n风险评估：市场情绪和长期趋势存在不确定性，需要密切关注市场动态。", 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-12'}
2025-07-28 04:48:23,745 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 04:48:23,752 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 04:49:19,140 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 04:49:19,141 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 04:50:07,304 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 04:50:07,306 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 04:50:07,306 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-13, 累计收益=0.0000, 周收益=0.0000
2025-07-28 04:50:23,875 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 04:50:23,876 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 04:50:23,876 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 04:50:23,876 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 04:50:23,876 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 04:50:23,876 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 04:50:23,876 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 04:50:23,876 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们综合考虑了来自分析层和展望层智能体的输出。NAA显示大部分新闻对AAPL持积极态度，尤其是关于AI功能和智能机器人等创新技术的报道，以及与阿里巴巴合作的消息，这些都有助于提升市场对AAPL的信心。TAA显示AAPL股价呈现上升趋势，技术指标也支持这一趋势。FAA评估AAPL为低估，财务健康得分高，竞争地位领先。展望层智能体中，BOA给出强烈看涨信号，而BeOA则认为存在下跌风险，NOA认为市场展望中性。综合分析，尽管存在一定的回调风险，但积极的新闻和技术分析以及低估的估值支持买入决策。**最终决策：买入**', 'action': 'buy', 'trading_actions': {'AAPL': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-13'}
2025-07-28 04:50:23,876 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'AAPL': 1.0}
2025-07-28 04:50:23,876 - __main__ - INFO - 📈 第2天交易动作: {'AAPL': 1.0}
2025-07-28 04:50:23,880 - __main__ - INFO - 💰 第2天净值变化: 收益率=-0.0214, 净值=$978578.19
2025-07-28 04:50:23,881 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 04:51:11,303 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 04:51:11,303 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 04:51:51,682 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 04:51:51,682 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 04:51:51,683 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-14, 累计收益=-0.0214, 周收益=-0.0214
2025-07-28 04:52:24,629 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 04:52:24,629 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 04:52:24,629 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 04:52:24,630 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 04:52:24,630 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 04:52:24,630 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 04:52:24,630 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 04:52:24,630 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析日期: 2025-02-14\n\n分析对象: AAPL (Apple)\n\n### 情绪评分\n- 情绪评分: 0.6\n- 分析依据: 多数新闻情绪为中性，但有关新产品发布和ARM的积极报道提升了整体情绪。\n\n### 新闻摘要\n- 摘要: 苹果公司即将发布新产品，ARM在芯片销售市场中的竞争，以及与阿里巴巴和百度的合作，均对市场情绪产生积极影响。\n\n### 关键事件列表\n1. 苹果公司CEO Tim Cook确认新产品将于下周发布，引发市场对iPhone SE 4的猜测。\n2. ARM Holdings进入芯片销售市场，直接与客户竞争，包括高通和英伟达。\n3. 苹果公司与阿里巴巴和百度合作，旨在将AI功能引入中国市场。\n\n### 影响评估\n- 影响评估: 正面影响\n- 分析依据: 新产品发布和与重要合作伙伴的合作可能会提升苹果的市场份额和盈利能力。\n\n### 分析信心度\n- 分析信心度: 0.8\n- 分析依据: 新闻报道较为全面，且涉及多个关键事件，对苹果公司的影响较为明确。\n\n### 技术分析\n- 趋势方向: 看涨\n- 支撑位价格: $240.99\n- 阻力位价格: $245.55\n- 技术评分: 0.5\n- 分析信心度: 0.8\n\n### 基本面分析\n- 估值评估: 合理\n- 财务健康评分: 8.5\n- 竞争地位: 领先\n- 长期增长潜力: 高\n- 分析信心度: 未知\n\n### 展望层智能体输出\n- BOA (看涨展望): 强烈看涨信号\n- BeOA (看跌展望): 弱看跌信号\n- NOA (中性展望): 中性观望\n\n综合分析，尽管存在一些潜在风险，但市场情绪和技术分析均显示出积极的趋势。基本面分析也支持苹果公司的长期增长潜力。因此，倾向于执行买入操作。\n\n**最终决策：买入**', 'action': 'buy', 'trading_actions': {'AAPL': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-14'}
2025-07-28 04:52:24,630 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'AAPL': 1.0}
2025-07-28 04:52:24,630 - __main__ - INFO - 📈 第3天交易动作: {'AAPL': 1.0}
2025-07-28 04:52:24,634 - __main__ - INFO - 💰 第3天净值变化: 收益率=0.0070, 净值=$985442.64
2025-07-28 04:52:24,637 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 04:52:24,638 - __main__ - INFO - TAA检测到无价格数据，建议跳过当前日期: 2025-02-17
2025-07-28 04:52:34,804 - __main__ - ERROR - LLM分析请求失败: Error code: 400, with error text {"contentFilter":[{"level":1,"role":"assistant"}],"error":{"code":"1301","message":"系统检测到输入或生成内容可能包含不安全或敏感内容，请您避免输入易产生敏感内容的提示语，感谢您的配合。"}}
2025-07-28 04:52:34,804 - __main__ - WARNING - 🤖 NAA: 数据验证失败，使用默认响应
2025-07-28 04:52:34,805 - __main__ - ERROR - 智能体 NAA LLM调用失败: the JSON object must be str, bytes or bytearray, not NoneType
2025-07-28 04:52:34,859 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 04:52:34,859 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 04:53:26,361 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 04:53:26,362 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 04:53:26,362 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-17, 累计收益=-0.0146, 周收益=-0.0146
2025-07-28 04:53:44,412 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 04:53:44,413 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 04:53:44,413 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 04:53:44,413 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 04:53:44,413 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 04:53:44,413 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析日期：2025-02-17\n持仓：AAPL 3905\n\n分析层智能体输出：\n- NAA：由于LLM不可用，使用默认分析，信心度为0.5\n- TAA：无法进行技术分析，建议跳过，信心度为1.0\n- FAA：基本面分析显示，估值评估合理，财务健康得分为8.5，竞争地位领先，长期趋势向好，信心度未知\n\n展望层智能体输出：\n- BOA：由于分析层智能体输出不完整，无法给出具体分析，信心度未知\n- BeOA：由于分析层智能体输出不完整，无法给出具体分析，信心度未知\n- NOA：由于分析层智能体输出不完整，无法给出具体分析，信心度未知\n\n综合分析，由于分析层智能体输出不完整，无法给出明确的展望层智能体信号。考虑到持仓情况和基本面分析的结果，建议保持当前持仓不变。\n**最终决策：保持不变**', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-17'}
2025-07-28 04:53:44,413 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 04:53:44,416 - __main__ - INFO - 💰 第4天净值变化: 收益率=0.0188, 净值=$1003984.54
2025-07-28 04:53:44,417 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 04:54:57,413 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 04:54:57,415 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 04:55:37,164 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 04:55:37,165 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 04:55:37,165 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-18, 累计收益=0.0040, 周收益=0.0040
2025-07-28 04:55:56,841 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 04:55:56,842 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 04:55:56,842 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 04:55:56,842 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 04:55:56,843 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 04:55:56,843 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 04:55:56,843 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 04:55:56,843 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析日期：2025-02-18\n\n持仓：AAPL 3905\n\n分析层智能体输出分析：\nNAA情绪评分0.6，表明市场情绪较为积极。新闻摘要中提到苹果公司即将推出的iPhone SE 4将采用台积电的5G调制解调器，这可能对公司的产品竞争力产生积极影响。TAA技术分析显示AAPL短期内看涨，技术指标如RSI、MACD和移动平均线均支持这一观点。FAA基本面分析显示AAPL估值合理，财务健康评分高，竞争地位领先。\n展望层智能体输出分析：\nBOA给出看涨展望，认为市场情绪和新闻事件对AAPL有利。BeOA给出看跌展望，但理由不充分。NOA认为市场情绪稳定，建议中性观望。\n综合分析，尽管技术分析和基本面分析支持看涨，但市场情绪和新闻事件对AAPL有利，而看跌展望的理由不充分。因此，倾向于执行买入操作。\n**最终决策：买入**', 'action': 'buy', 'trading_actions': {'AAPL': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-18'}
2025-07-28 04:55:56,843 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'AAPL': 1.0}
2025-07-28 04:55:56,843 - __main__ - INFO - 📈 第5天交易动作: {'AAPL': 1.0}
2025-07-28 04:55:56,847 - __main__ - INFO - 💰 第5天净值变化: 收益率=0.0031, 净值=$1007061.70
2025-07-28 04:55:56,847 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-28 04:55:56,847 - __main__ - INFO - ============================================================
2025-07-28 04:55:56,848 - __main__ - INFO - 第 7 周性能分析 - 联盟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-28 04:55:56,848 - __main__ - INFO - 周总收益率: 0.0075
2025-07-28 04:55:56,848 - __main__ - INFO - 周夏普比率: 1.7128
2025-07-28 04:55:56,848 - __main__ - INFO - 交易天数: 5
2025-07-28 04:55:56,848 - __main__ - INFO - ============================================================

2025-07-28 05:28:02,887 - __main__ - INFO - ======================================================================
2025-07-28 05:28:02,887 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-28 05:28:02,887 - __main__ - INFO - ======================================================================
2025-07-28 05:28:02,887 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-28 05:28:02,887 - __main__ - INFO - 开始模拟联盟: {'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'}
2025-07-28 05:28:02,887 - __main__ - INFO - 📅 当前周数: 8
2025-07-28 05:28:02,887 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-28 05:28:02,887 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-28 05:28:02,888 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-02-19 (第8周)
2025-07-28 05:28:02,888 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-02-19, end_date=2025-02-26
2025-07-28 05:28:02,888 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-28 05:28:02,970 - __main__ - INFO - 📅 设置当前模拟周数: 8 (来自参数)
2025-07-28 05:28:02,971 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 05:28:44,478 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 05:28:44,479 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 05:29:09,350 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 05:29:09,352 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 05:29:09,352 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-19, 累计收益=0.0000, 周收益=0.0000
2025-07-28 05:30:41,073 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 05:30:41,074 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 05:30:41,074 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 05:30:41,074 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 05:30:41,074 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 05:30:41,075 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': "分析日期: 2025-02-19\n持仓: {'AAPL': 0}\n\n🤖 分析层智能体输出:\n  • NAA: 分析日期: 2025-02-19\n\n分析结果：\n- 情绪评分（-1到1）：0.2\n- 新闻摘要：苹果公司（AAPL）近期新闻主要围绕其产品发布、全球贸易政策、AI投资以及市场情绪等方面。美国总统特朗普宣布对汽车、芯片和药品征收25%的关税，对全球贸易产生了一定影响。\n- 关键事件列表：1. 苹果公司即将发布iPhone 17 Air和iPhone 17 Pro。2. 苹果公司发布iPhone 16e。3. 美国总统特朗普宣布对汽车、芯片和药品征收25%的关税。\n- 影响评估：苹果公司即将发布的新产品可能会对市场情绪产生积极影响，但具体影响程度取决于产品性能和市场接受度。美国总统特朗普宣布的关税政策可能会对全球贸易产生负面影响，进而影响苹果公司的业务。\n- 分析信心度（0到1）：0.8\n\n  • TAA: 分析日期: 2025-02-19\n\n📈 股票价格历史（AAPL）:\n- 2025-02-19: 收盘$244.87, 开盘$244.66, 最高$246.01, 最低$243.16, 成交量32204215\n\n### 1. 验证交易日有效性\n- 检查当前日期是否有价格数据：根据提供的数据，2025-02-19有价格数据，因此是有效交易日。\n### 2. 技术分析\n#### 价格趋势和动量\n- 从2025-01-31的最高点$247.19到2025-02-19的收盘价$244.87，AAPL显示出一定的下跌趋势。\n- 成交量在2025-02-19为32204215，较前一日有所减少，这可能表明市场参与者在短期内对上涨动力持谨慎态度。\n#### 支撑位和阻力位\n- 支撑位：根据历史价格，$243.16是近期的一个低点，可能是一个潜在的支撑位。\n- 阻力位：$246.01是当日的最高价，可能是一个阻力位。\n#### 技术指标信号\n- **RSI（相对强弱指数）**：需要计算RSI值，但根据历史数据，如果RSI低于50，可能表明市场处于超卖状态，如果高于50，可能表明市场处于超买状态。\n- **MACD（移动平均收敛发散）**：需要计算MACD值，但根据历史数据，如果MACD线在信号线下方，可能表明看跌趋势。\n#### 趋势方向\n- 根据上述分析，AAPL可能显示出看跌趋势。\n\n### 3. 分析结果\n- **是否跳过该日**：false\n- **趋势方向**：bearish（看跌）\n- **支撑位价格**：$243.16\n- **阻力位价格**：$246.01\n- **技术评分**：-0.5（基于趋势方向和动量分析）\n- **关键技术指标分析**：\n  - RSI和MACD需要具体数值来分析，但根据趋势，RSI可能低于50，MACD可能显示看跌信号。\n- **分析信心度**：0.8（基于历史价格数据和趋势分析）\n\n  • FAA: {'valuation_assessment': '低估', 'financial_health_score': 8.5, 'competitive_position': '领先', 'long_term_growth_potential': '高'}\n\n🔮 展望层智能体输出:\n  • BOA: {'analysis': '基于上述分析层智能体的输出，以下是我的看涨展望结果：\n\n### 市场展望（看涨）\n尽管短期内有看跌趋势，但长期来看，我对苹果公司（AAPL）的市场展望是看涨的。\n\n  • BeOA: {'analysis': '基于上述分析层智能体的输出，以下是我的看跌展望结果：\n\n### 市场展望（看跌）\n- 苹果公司（AAPL）的股票价格显示出看跌趋势，尤其是在美国总统特朗普宣布对汽车、芯片和药品征收25%的关税后。\n\n  • NOA: {'analysis': '### 中性展望结果\n\n#### 市场展望（中性）\n当前市场展望为中性，主要由于市场情绪较为稳定，尽管存在一些积极和消极的因素。\n\n#### 平衡分析\n- **...'}\n\n展望层智能体信号分析：BOA给出看涨信号，BeOA给出看跌信号，NOA认为市场展望中性。\n决策理由：由于BOA和BeOA信号相反，且强度相当，我们优先考虑NOA的中性建议。\n风险评估：NAA、TAA和FAA的分析结果支持中性观望的策略。\n最终决策：保持不变\n", 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-19'}
2025-07-28 05:30:41,075 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 05:30:41,082 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 05:31:39,377 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 05:31:39,378 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 05:32:16,505 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 05:32:16,506 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 05:32:16,506 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-20, 累计收益=0.0000, 周收益=0.0000
2025-07-28 05:32:39,809 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 05:32:39,811 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 05:32:39,811 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 05:32:39,811 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 05:32:39,811 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 05:32:39,811 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们综合考虑了来自分析层和展望层的智能体输出。NAA显示，苹果公司（AAPL）即将推出的iPhone 17 Pro的无线反向充电功能和收入增长预测可能对股价产生积极影响，但同时苹果可能提高iPhone价格可能会对公司的收入和利润产生负面影响。TAA显示，AAPL的价格显示出一定的下降趋势，技术指标如RSI接近70，MACD看跌，短期移动平均线低于长期移动平均线，表明市场可能处于看跌状态。FAA显示，AAPL的估值被低估，财务健康得分较高，竞争地位领先。展望层智能体中，BOA给出强烈看涨信号，而BeOA给出强烈看跌信号，NOA认为市场应保持中性观望。根据决策规则，由于BOA和BeOA信号相反且强度相当，我们优先考虑NOA的建议，即保持中性观望。因此，**最终决策：保持不变**。', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-20'}
2025-07-28 05:32:39,811 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 05:32:39,818 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 05:33:19,637 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 05:33:19,637 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 05:33:58,220 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 05:33:58,221 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 05:33:58,221 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-21, 累计收益=0.0000, 周收益=0.0000
2025-07-28 05:34:17,756 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 05:34:17,756 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 05:34:17,756 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 05:34:17,756 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 05:34:17,756 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 05:34:17,756 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们综合考虑了来自分析层和展望层的智能体输出。NAA显示市场情绪较为积极，技术分析表明AAPL股价呈现上升趋势，且技术指标支持看涨趋势。基本面分析显示AAPL处于低估状态，财务健康得分较高，竞争地位领先。展望层智能体中，BOA给出看涨信号，BeOA给出看跌信号，而NOA认为应保持中性观望。由于BOA和BeOA信号相反，我们优先考虑NOA的建议，并综合分析结果，认为当前市场情况较为中性，因此倾向于保持不变。**最终决策：保持不变**。', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-21'}
2025-07-28 05:34:17,756 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 05:34:17,760 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 05:35:34,613 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 05:35:34,614 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 05:36:15,755 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 05:36:15,756 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 05:36:15,756 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-24, 累计收益=0.0000, 周收益=0.0000
2025-07-28 05:36:29,958 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 05:36:29,959 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 05:36:29,959 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 05:36:29,959 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 05:36:29,959 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 05:36:29,959 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析过程如下：展望层智能体BOA和BeOA给出了相反的信号，BOA看涨，BeOA看跌。然而，NOA认为市场应保持中性观望。考虑到BOA和BeOA的信号强度，我们优先考虑NOA的建议。NAA的分析显示苹果公司的新闻对市场情绪产生了积极影响，技术分析显示AAPL价格呈现上升趋势，但RSI值接近超买区域，表明市场可能需要回调。基本面分析显示AAPL被低估，财务健康得分高，竞争地位领先。综合以上分析，我们得出结论：市场目前处于中性状态，没有明确的买入或卖出信号。因此，我们建议保持不变。**最终决策：保持不变**。', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-24'}
2025-07-28 05:36:29,959 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 05:36:29,963 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 05:37:11,628 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 05:37:11,630 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 05:37:52,412 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 05:37:52,413 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 05:37:52,413 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-25, 累计收益=0.0000, 周收益=0.0000
2025-07-28 05:38:07,210 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 05:38:07,212 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 05:38:07,212 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 05:38:07,212 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 05:38:07,212 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 05:38:07,212 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析过程如下：\n\n展望层智能体的输出显示，BOA给出看涨信号，BeOA给出看跌信号，而NOA认为市场展望中性。由于BOA和BeOA的信号方向相反，我们优先考虑NOA的建议，即保持中性观望。NAA的分析显示，苹果公司的投资计划可能会对其股价产生积极影响，但分析师的怀疑可能会对市场情绪产生负面影响。TAA的技术分析显示，AAPL的价格显示出一定的波动性，但整体趋势是上升的。FAA的基本面分析表明，AAPL的估值合理，财务健康，竞争地位领先。综合以上分析，尽管存在一些积极因素，但也存在不确定性，因此我们倾向于保持不变。\n\n最终决策：保持不变', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-25'}
2025-07-28 05:38:07,212 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 05:38:07,215 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-28 05:38:07,215 - __main__ - INFO - ============================================================
2025-07-28 05:38:07,215 - __main__ - INFO - 第 8 周性能分析 - 联盟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-28 05:38:07,215 - __main__ - INFO - 周总收益率: 0.0000
2025-07-28 05:38:07,215 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-28 05:38:07,215 - __main__ - INFO - 交易天数: 5
2025-07-28 05:38:07,215 - __main__ - INFO - ============================================================

2025-07-28 06:10:06,584 - __main__ - INFO - ======================================================================
2025-07-28 06:10:06,584 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-28 06:10:06,584 - __main__ - INFO - ======================================================================
2025-07-28 06:10:06,584 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-28 06:10:06,584 - __main__ - INFO - 开始模拟联盟: {'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'}
2025-07-28 06:10:06,584 - __main__ - INFO - 📅 当前周数: 9
2025-07-28 06:10:06,585 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-28 06:10:06,585 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-28 06:10:06,586 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-02-26 (第9周)
2025-07-28 06:10:06,586 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-02-26, end_date=2025-03-05
2025-07-28 06:10:06,586 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-28 06:10:06,661 - __main__ - INFO - 📅 设置当前模拟周数: 9 (来自参数)
2025-07-28 06:10:06,663 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 06:10:20,161 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 06:10:20,162 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 06:10:42,498 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 06:10:42,498 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 06:10:42,498 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-26, 累计收益=0.0000, 周收益=0.0000
2025-07-28 06:10:46,754 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 06:10:46,755 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 06:10:46,755 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 06:10:46,755 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 06:10:46,755 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 06:10:46,755 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们综合考虑了来自分析层和展望层的智能体输出。NAA的情绪评分显示市场情绪中性偏乐观，尽管存在一些略微乐观的新闻报道。TAA的技术分析显示AAPL可能处于看跌趋势，但同时也存在超卖状态的可能性。FAA的基本面分析表明AAPL可能被低估，具有领先的市场地位和良好的财务健康状况。展望层智能体中，BOA给出了看涨展望，而BeOA给出了看跌展望，NOA则认为应该中性观望。根据决策规则，由于BOA和BeOA给出了相反的信号，我们优先考虑NOA的建议，认为应该保持中性观望。因此，**最终决策：保持不变**。', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-26'}
2025-07-28 06:10:46,755 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 06:10:46,760 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 06:11:16,451 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 06:11:16,452 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 06:11:40,660 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 06:11:40,661 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 06:11:40,661 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-27, 累计收益=0.0000, 周收益=0.0000
2025-07-28 06:11:48,062 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 06:11:48,062 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 06:11:48,063 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 06:11:48,063 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 06:11:48,063 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 06:11:48,063 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析过程如下：\n\n展望层智能体输出分析：\n- BOA（看涨展望）认为，尽管AAPL在短期内经历了价格下跌，但基于积极因素，市场展望为看涨。\n- BeOA（看跌展望）认为，鉴于当前的市场情绪和关键事件，以及对AAPL的技术分析，市场展望为看跌。\n- NOA（中性展望）认为，目前市场对AAPL的展望较为中性，没有明显的看涨或看跌趋势。\n\n决策理由：\n- 由于BOA和BeOA给出了相反的信号，我们优先考虑NOA的建议，即保持中性观望。\n- 分析层的NAA、TAA、FAA结果作为风险评估的参考，但不是决策主导因素。\n\n风险评估：\n- NAA显示大部分新闻情绪中性，对AAPL的影响有限。\n- TAA显示AAPL可能处于看跌趋势，但需要具体的技术指标值来进一步确认。\n- FAA显示AAPL的估值合理，财务健康，竞争地位领先。\n\n最终决策声明：**最终决策：保持不变**', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-27'}
2025-07-28 06:11:48,063 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 06:11:48,070 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 06:12:14,074 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 06:12:14,076 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 06:12:36,875 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 06:12:36,876 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 06:12:36,876 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-28, 累计收益=0.0000, 周收益=0.0000
2025-07-28 06:12:45,212 - __main__ - WARNING - 未找到明确的最终决策声明，保持action=hold
2025-07-28 06:12:45,212 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 06:12:45,212 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 06:12:45,213 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 06:12:45,213 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 06:12:45,213 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析过程如下：\n\n展望层智能体输出分析：\n- BOA（看涨展望）认为AAPL的市场展望为看涨，主要基于分析层智能体输出中的积极新闻和公司发展前景。\n- BeOA（看跌展望）指出虽然短期内存在反弹迹象，但长期市场情绪和技术指标显示出潜在的下跌风险。\n- NOA（中性展望）认为市场目前显示出一定的中性趋势，但存在看涨的可能性。\n\n决策理由：\n- 考虑到BOA和BeOA的信号方向相反，且强度相当，我们优先考虑NOA的中性建议。\n- 分析层智能体NAA、TAA和FAA的结果显示，AAPL的情绪评分和财务健康状况良好，但技术指标显示出市场动力不足。\n\n风险评估：\n- Ark Invest出售Spotify股票可能会对AAPL产生负面影响，但Apple开发新技术和Warren Buffett的正面信息可能对AAPL产生积极影响。\n- 技术分析显示AAPL显示出一定的下跌趋势，但近期有反弹迹象。\n\n最终决策：考虑到展望层智能体的中性观点和分析层智能体的风险评估，我们决定保持当前持仓不变。', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-02-28'}
2025-07-28 06:12:45,213 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 06:12:45,219 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 06:13:08,375 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 06:13:08,376 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 06:13:08,869 - __main__ - ERROR - LLM分析请求失败: Error code: 400, with error text {"contentFilter":[{"level":1,"role":"user"}],"error":{"code":"1301","message":"系统检测到输入或生成内容可能包含不安全或敏感内容，请您避免输入易产生敏感内容的提示语，感谢您的配合。"}}
2025-07-28 06:13:08,870 - __main__ - WARNING - 🤖 NOA: 数据验证失败，使用默认响应
2025-07-28 06:13:08,870 - __main__ - ERROR - 智能体 NOA LLM调用失败: the JSON object must be str, bytes or bytearray, not NoneType
2025-07-28 06:13:08,957 - __main__ - ERROR - LLM分析请求失败: Error code: 400, with error text {"contentFilter":[{"level":1,"role":"user"}],"error":{"code":"1301","message":"系统检测到输入或生成内容可能包含不安全或敏感内容，请您避免输入易产生敏感内容的提示语，感谢您的配合。"}}
2025-07-28 06:13:08,958 - __main__ - WARNING - 🤖 BOA: 数据验证失败，使用默认响应
2025-07-28 06:13:08,958 - __main__ - ERROR - 智能体 BOA LLM调用失败: the JSON object must be str, bytes or bytearray, not NoneType
2025-07-28 06:13:09,057 - __main__ - ERROR - LLM分析请求失败: Error code: 400, with error text {"contentFilter":[{"level":1,"role":"user"}],"error":{"code":"1301","message":"系统检测到输入或生成内容可能包含不安全或敏感内容，请您避免输入易产生敏感内容的提示语，感谢您的配合。"}}
2025-07-28 06:13:09,058 - __main__ - WARNING - 🤖 BeOA: 数据验证失败，使用默认响应
2025-07-28 06:13:09,058 - __main__ - ERROR - 智能体 BeOA LLM调用失败: the JSON object must be str, bytes or bytearray, not NoneType
2025-07-28 06:13:09,058 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 06:13:09,058 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 06:13:09,058 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-03, 累计收益=0.0000, 周收益=0.0000
2025-07-28 06:13:09,641 - __main__ - ERROR - LLM分析请求失败: Error code: 400, with error text {"contentFilter":[{"level":1,"role":"user"}],"error":{"code":"1301","message":"系统检测到输入或生成内容可能包含不安全或敏感内容，请您避免输入易产生敏感内容的提示语，感谢您的配合。"}}
2025-07-28 06:13:09,641 - __main__ - WARNING - 未找到明确的最终决策声明，保持action=hold
2025-07-28 06:13:09,642 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 06:13:09,642 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 06:13:09,642 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 06:13:09,642 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 06:13:09,642 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': 'None', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-03'}
2025-07-28 06:13:09,642 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 06:13:09,646 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 06:13:40,428 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 06:13:40,428 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 06:13:55,290 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 06:13:55,291 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 06:13:55,291 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-04, 累计收益=0.0000, 周收益=0.0000
2025-07-28 06:14:01,679 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 06:14:01,679 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 06:14:01,679 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 06:14:01,679 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 06:14:01,679 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 06:14:01,679 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析过程如下：\n\n展望层智能体输出分析：\n- BOA给出了看涨展望，认为苹果公司的新产品和投资计划将推动股价上涨。\n- BeOA给出了看跌展望，主要基于NAA的情绪评分较低，TAA的技术分析显示中性状态，以及FAA的估值评估为低估，暗示市场可能对苹果公司的未来增长持谨慎态度。\n- NOA认为市场对苹果公司的展望呈现中性，认为新产品和投资的积极新闻与市场对新产品发布的谨慎态度相平衡。\n\n决策理由：\n- 虽然BOA给出了看涨信号，但BeOA的看跌信号和NOA的中性展望表明市场对苹果公司的未来存在不确定性。\n- TAA的技术分析显示中性状态，没有明确的看涨或看跌信号。\n- FAA的估值评估为低估，但财务健康评分和竞争优势领先，表明苹果公司的基本面仍然稳健。\n\n风险评估：\n- 新产品发布的不确定性是主要风险因素。\n- 市场对苹果公司未来增长的态度可能发生变化。\n\n最终决策：考虑到市场的不确定性和风险评估，**最终决策：保持不变**。', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-04'}
2025-07-28 06:14:01,680 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 06:14:01,683 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-28 06:14:01,684 - __main__ - INFO - ============================================================
2025-07-28 06:14:01,684 - __main__ - INFO - 第 9 周性能分析 - 联盟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-28 06:14:01,684 - __main__ - INFO - 周总收益率: 0.0000
2025-07-28 06:14:01,684 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-28 06:14:01,684 - __main__ - INFO - 交易天数: 5
2025-07-28 06:14:01,684 - __main__ - INFO - ============================================================

2025-07-28 06:45:01,772 - __main__ - INFO - ======================================================================
2025-07-28 06:45:01,772 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-28 06:45:01,772 - __main__ - INFO - ======================================================================
2025-07-28 06:45:01,772 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-28 06:45:01,773 - __main__ - INFO - 开始模拟联盟: {'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'}
2025-07-28 06:45:01,773 - __main__ - INFO - 📅 当前周数: 10
2025-07-28 06:45:01,773 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-28 06:45:01,773 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-28 06:45:01,773 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-03-05 (第10周)
2025-07-28 06:45:01,773 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-03-05, end_date=2025-03-12
2025-07-28 06:45:01,773 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-28 06:45:01,847 - __main__ - INFO - 📅 设置当前模拟周数: 10 (来自参数)
2025-07-28 06:45:01,848 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 06:45:37,000 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 06:45:37,001 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 06:46:11,004 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 06:46:11,005 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 06:46:11,005 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-05, 累计收益=0.0000, 周收益=0.0000
2025-07-28 06:46:27,507 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 06:46:27,507 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 06:46:27,507 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 06:46:27,507 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 06:46:27,508 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 06:46:27,508 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析过程如下：\n\n展望层智能体输出显示，BOA给出了看涨展望，BeOA给出了看跌展望，而NOA认为市场展望中性。由于BOA和BeOA给出了相反的信号，我们优先考虑NOA的建议，即保持中性观望。NAA的分析显示大部分新闻情绪中性，对苹果公司的影响较小。TAA的技术分析显示市场动力不足，趋势不明朗。FAA的基本面分析表明苹果公司的估值被低估，财务健康得分较高，在竞争中处于领先地位。综合以上分析，尽管存在看涨和看跌的因素，但市场整体呈现中性状态，因此我们倾向于保持不变。\n\n**最终决策：保持不变**', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-05'}
2025-07-28 06:46:27,508 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 06:46:27,515 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 06:46:56,720 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 06:46:56,722 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 06:47:42,859 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 06:47:42,860 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 06:47:42,860 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-06, 累计收益=0.0000, 周收益=0.0000
2025-07-28 06:48:00,717 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 06:48:00,718 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 06:48:00,718 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 06:48:00,718 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 06:48:00,718 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 06:48:00,718 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析过程如下：\n\n展望层智能体输出分析：\n- BOA（看涨展望）认为尽管存在一些负面新闻和短期技术指标显示看跌趋势，但长期来看，对苹果公司（AAPL）的市场展望是看涨的。\n- BeOA（看跌展望）指出，尽管整体市场情绪较为稳定，但存在一些负面新闻，如政府裁员和股市预期之外的后果，因此对苹果公司的市场展望是看跌的。\n- NOA（中性展望）认为当前市场展望为中性，主要基于新闻情绪的中性和对苹果公司市场地位的稳定评估。\n\n决策理由：\n- 由于BOA和BeOA给出了相反的信号，我们优先考虑NOA的建议，即保持中性观望。\n- 分析层的NAA、TAA、FAA结果支持NOA的中性观点，其中TAA显示短期看跌趋势，但长期来看，苹果公司的财务健康状况和竞争优势表明其具有长期增长潜力。\n\n风险评估：\n- 技术分析显示市场可能超买，但长期趋势仍然看涨。\n- 基本面分析表明苹果公司处于低估状态，具有领先的市场地位和良好的财务健康状况。\n\n最终决策：保持不变。\n\n', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-06'}
2025-07-28 06:48:00,718 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 06:48:00,722 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 06:48:02,465 - __main__ - ERROR - LLM分析请求失败: Error code: 400, with error text {"contentFilter":[{"level":1,"role":"user"}],"error":{"code":"1301","message":"系统检测到输入或生成内容可能包含不安全或敏感内容，请您避免输入易产生敏感内容的提示语，感谢您的配合。"}}
2025-07-28 06:48:02,465 - __main__ - WARNING - 🤖 NAA: 数据验证失败，使用默认响应
2025-07-28 06:48:02,466 - __main__ - ERROR - 智能体 NAA LLM调用失败: the JSON object must be str, bytes or bytearray, not NoneType
2025-07-28 06:48:29,935 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 06:48:29,937 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 06:48:54,897 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 06:48:54,898 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 06:48:54,898 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-07, 累计收益=0.0000, 周收益=0.0000
2025-07-28 06:49:21,767 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 06:49:21,767 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 06:49:21,767 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 06:49:21,767 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 06:49:21,767 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 06:49:21,767 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在分析AAPL的股票时，我们首先考虑了技术分析。从历史价格数据来看，AAPL的价格呈现上升趋势，动量指标显示近期价格波动较大。支撑位和阻力位的分析表明，市场在$234.76和$230.68之间可能有支撑，而在$241.37和$242.09之间可能有阻力。技术指标如RSI接近中性区域，MACD看涨，移动平均线显示长期趋势看涨。技术评分设定为0.5，分析信心度为0.8。\n\n展望层智能体的输出显示，BOA给出看涨信号，BeOA给出看跌信号，而NOA认为市场展望中性。由于BOA和BeOA的信号方向相反，我们优先考虑NOA的建议，即保持中性观望。\n\n综合分析，考虑到技术分析显示的市场中性状态以及展望层智能体的中性展望，我们得出**最终决策：保持不变**。\n\n风险评估方面，分析层的NAA、TAA、FAA结果作为参考，但不是决策主导因素。', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-07'}
2025-07-28 06:49:21,767 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 06:49:21,772 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 06:50:02,097 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 06:50:02,098 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 06:50:25,435 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 06:50:25,435 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 06:50:25,435 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-10, 累计收益=0.0000, 周收益=0.0000
2025-07-28 06:50:31,414 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 06:50:31,414 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 06:50:31,415 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 06:50:31,415 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 06:50:31,415 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 06:50:31,415 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '经过对分析层智能体的输出进行综合分析，以下是展望层智能体的观点和决策理由。\n\n### 展望层智能体信号分析\n- BOA（看涨展望）：尽管存在一些不确定性，但基于当前的市场情绪和关键事件，BOA对AAPL的市场展望持看涨态度。\n- BeOA（看跌展望）：BeOA认为市场存在一些负面因素，如技术指标显示的看跌趋势和财务指标的担忧，因此对AAPL的市场展望持看跌态度。\n- NOA（中性展望）：NOA认为当前市场表现出一定的中性趋势，尽管存在一些积极信号，但不确定性仍然存在。\n\n### 决策理由\n- BOA和BeOA的观点相反，但NOA的建议是中性观望，因此优先考虑NOA的建议。\n- 考虑到NAA、TAA和FAA的分析结果，虽然存在一些正面和负面信号，但整体上没有明显的买入或卖出信号。\n- 技术分析显示AAPL可能存在看跌趋势，但分析信心度不高。\n\n### 风险评估\n- 市场存在不确定性，特别是特朗普关于资金流入欧洲的评论可能对市场产生不确定性。\n- 技术分析显示AAPL可能存在看跌趋势，但分析信心度不高。\n\n### 最终决策声明\n基于以上分析，**最终决策：保持不变**。', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-10'}
2025-07-28 06:50:31,415 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 06:50:31,422 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 06:51:01,076 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 06:51:01,077 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 06:51:25,667 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 06:51:25,667 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 06:51:25,667 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-11, 累计收益=0.0000, 周收益=0.0000
2025-07-28 06:51:35,717 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 06:51:35,717 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 06:51:35,717 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 06:51:35,717 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 06:51:35,717 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 06:51:35,717 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们综合考虑了来自分析层智能体和展望层智能体的输出。NAA提供了对市场情绪的评估，其中情绪评分较低，表明市场情绪谨慎。TAA显示AAPL股票在技术分析中显示出看跌信号，包括价格下跌和RSI接近超卖区域。FAA对AAPL的估值评估为低估，财务健康得分较高，竞争地位领先。展望层智能体中，BOA给出了看涨展望，而BeOA给出了看跌展望，NOA则认为市场展望中性。根据决策规则，由于BOA和BeOA给出了相反的信号，我们优先考虑NOA的中性展望。因此，最终决策为保持不变。**最终决策：保持不变**', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-11'}
2025-07-28 06:51:35,718 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 06:51:35,721 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-28 06:51:35,722 - __main__ - INFO - ============================================================
2025-07-28 06:51:35,722 - __main__ - INFO - 第 10 周性能分析 - 联盟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-28 06:51:35,722 - __main__ - INFO - 周总收益率: 0.0000
2025-07-28 06:51:35,722 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-28 06:51:35,722 - __main__ - INFO - 交易天数: 5
2025-07-28 06:51:35,722 - __main__ - INFO - ============================================================
2025-07-28 07:23:15,812 - __main__ - INFO - ======================================================================
2025-07-28 07:23:15,812 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-28 07:23:15,812 - __main__ - INFO - ======================================================================
2025-07-28 07:23:15,812 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-28 07:23:15,812 - __main__ - INFO - 开始模拟联盟: {'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'}
2025-07-28 07:23:15,812 - __main__ - INFO - 📅 当前周数: 11
2025-07-28 07:23:15,812 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-28 07:23:15,812 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-28 07:23:15,813 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-03-12 (第11周)
2025-07-28 07:23:15,813 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-03-12, end_date=2025-03-19
2025-07-28 07:23:15,813 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-28 07:23:15,890 - __main__ - INFO - 📅 设置当前模拟周数: 11 (来自参数)
2025-07-28 07:23:15,891 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 07:23:38,643 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 07:23:38,645 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 07:24:01,402 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 07:24:01,404 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 07:24:01,404 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-12, 累计收益=0.0000, 周收益=0.0000
2025-07-28 07:24:07,922 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 07:24:07,923 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 07:24:07,923 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 07:24:07,923 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 07:24:07,923 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 07:24:07,923 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们综合考虑了来自分析层和展望层的智能体输出。NAA显示，苹果公司近期面临一些挑战，如Siri的AI升级延迟和关税相关成本问题，但同时也存在一些积极因素，如与Metallica的合作和Vision Pro头显的发布。TAA显示，AAPL的价格呈现波动性下降趋势，技术指标显示市场可能看跌。FAA评估苹果公司为低估，财务健康得分较高，竞争地位领先。展望层智能体中，BOA倾向于看涨，BeOA倾向于看跌，NOA认为市场处于中性状态。根据决策规则，由于BOA和BeOA的信号相反，我们优先考虑NOA的建议，认为应保持中性观望。因此，**最终决策：保持不变**。', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-12'}
2025-07-28 07:24:07,923 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 07:24:07,928 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 07:24:46,763 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 07:24:46,763 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 07:25:27,291 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 07:25:27,291 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 07:25:27,291 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-13, 累计收益=0.0000, 周收益=0.0000
2025-07-28 07:25:32,734 - __main__ - WARNING - 未找到明确的最终决策声明，保持action=hold
2025-07-28 07:25:32,734 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 07:25:32,734 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 07:25:32,734 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 07:25:32,734 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 07:25:32,734 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析过程如下：\n\n展望层智能体BOA给出了看涨展望，主要基于沃伦·巴菲特投资AI股票和阿里巴巴推出AI助手工具等积极因素。然而，BeOA给出了看跌展望，认为市场展现出一定的看跌迹象。NOA则认为市场目前呈现出一定的乐观情绪，但同时也存在不确定性。在决策权重分配中，BOA和BeOA的信号方向相反，因此我们优先考虑NOA的中性展望。\n\n技术分析显示AAPL价格呈现下降趋势，技术指标如RSI接近超卖区域，MACD显示看跌趋势，移动平均线交叉。基本面分析认为AAPL被低估，财务健康得分较高，竞争地位领先。综合分析，市场情绪和技术分析显示一定的看跌迹象，但基本面分析显示公司价值被低估。\n\n最终决策：考虑到市场情绪和技术分析显示的看跌迹象，但基本面分析显示公司价值被低估，我们倾向于保持不变。', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-13'}
2025-07-28 07:25:32,734 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 07:25:32,738 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 07:26:10,339 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 07:26:10,339 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 07:26:36,375 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 07:26:36,375 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 07:26:36,375 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-14, 累计收益=0.0000, 周收益=0.0000
2025-07-28 07:26:52,111 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 07:26:52,111 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 07:26:52,112 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 07:26:52,112 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 07:26:52,112 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 07:26:52,112 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们综合了来自分析层和展望层智能体的输出。NAA显示大部分新闻对AAPL的报道情绪较为中性，但特朗普对Meta和Apple的投资建议以及“Ted Lasso”第四季将在Apple TV+上播出的消息可能对AAPL股价产生积极影响。TAA显示AAPL价格呈现下降趋势，但市场可能接近超卖状态，需要进一步观察。FAA显示AAPL被低估，财务健康得分高，竞争地位领先。展望层智能体BOA和BeOA给出了相反的展望，BOA看涨，BeOA看跌。NOA认为应该中性观望。根据决策规则，由于BOA和BeOA给出了相反的信号，我们优先考虑NOA的建议，即保持中性观望。因此，**最终决策：保持不变**。', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-14'}
2025-07-28 07:26:52,112 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 07:26:52,118 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 07:27:20,309 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 07:27:20,309 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 07:27:54,143 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 07:27:54,143 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 07:27:54,143 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-17, 累计收益=0.0000, 周收益=0.0000
2025-07-28 07:28:02,378 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 07:28:02,379 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 07:28:02,379 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 07:28:02,379 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 07:28:02,379 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 07:28:02,379 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析过程如下：\n\n展望层智能体信号分析：\n- BOA给出看涨信号，但具体分析内容未提供。\n- BeOA给出看跌信号，指出苹果公司股票可能面临下行风险。\n- NOA认为市场应保持中性观望。\n\n决策理由：\n- 虽然BOA和BeOA给出了相反的信号，但NOA的建议倾向于中性观望，这表明市场存在不确定性。\n- TAA分析显示，AAPL价格趋势偏弱，但技术指标显示市场可能接近底部。\n- FAA分析认为AAPL被低估，财务状况良好，处于领先竞争地位。\n\n风险评估：\n- NAA分析显示，苹果公司面临供应链和成本方面的挑战，以及人工智能项目的困境。\n- TAA分析显示，市场信号并不完全一致，分析信心度较低。\n\n最终决策：考虑到市场的不确定性和分析结果，我们倾向于保持不变。\n\n**最终决策：保持不变**', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-17'}
2025-07-28 07:28:02,379 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 07:28:02,384 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 07:28:42,054 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 07:28:42,054 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 07:29:12,548 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 07:29:12,548 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 07:29:12,549 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-18, 累计收益=0.0000, 周收益=0.0000
2025-07-28 07:29:25,688 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 07:29:25,688 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 07:29:25,688 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 07:29:25,688 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 07:29:25,688 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 07:29:25,688 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们综合了来自分析层和展望层智能体的输出。NAA显示，苹果公司（AAPL）的新闻主要围绕其人工智能基础设施的投资和与全球星的合作伙伴关系，投资者对苹果股票的长期表现持乐观态度。TAA显示，AAPL的价格在最近一周内呈现下降趋势，但成交量有所增加，表明动量可能正在减弱。FAA显示，AAPL的估值评估为低估，财务健康分数为8.5，竞争地位领先。展望层智能体中，BOA给出了看涨展望，而BeOA给出了看跌展望，NOA则认为市场展望中性。根据决策规则，由于BOA和BeOA给出了相反的信号，我们优先考虑NOA的中性建议。因此，最终决策为保持不变。**最终决策：保持不变**。', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-18'}
2025-07-28 07:29:25,688 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 07:29:25,692 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-28 07:29:25,693 - __main__ - INFO - ============================================================
2025-07-28 07:29:25,693 - __main__ - INFO - 第 11 周性能分析 - 联盟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-28 07:29:25,693 - __main__ - INFO - 周总收益率: 0.0000
2025-07-28 07:29:25,693 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-28 07:29:25,693 - __main__ - INFO - 交易天数: 5
2025-07-28 07:29:25,693 - __main__ - INFO - ============================================================
2025-07-28 08:01:21,190 - __main__ - INFO - ======================================================================
2025-07-28 08:01:21,190 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-28 08:01:21,190 - __main__ - INFO - ======================================================================
2025-07-28 08:01:21,190 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-28 08:01:21,191 - __main__ - INFO - 开始模拟联盟: {'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'}
2025-07-28 08:01:21,191 - __main__ - INFO - 📅 当前周数: 12
2025-07-28 08:01:21,191 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-28 08:01:21,191 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-28 08:01:21,191 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-03-19 (第12周)
2025-07-28 08:01:21,192 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-03-19, end_date=2025-03-26
2025-07-28 08:01:21,192 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-28 08:01:21,274 - __main__ - INFO - 📅 设置当前模拟周数: 12 (来自参数)
2025-07-28 08:01:21,276 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 08:01:48,429 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 08:01:48,431 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 08:02:18,909 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 08:02:18,910 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 08:02:18,910 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-19, 累计收益=0.0000, 周收益=0.0000
2025-07-28 08:02:26,318 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 08:02:26,318 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 08:02:26,318 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 08:02:26,318 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 08:02:26,318 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 08:02:26,318 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析过程如下：\n\n展望层智能体输出分析：\n- BOA给出了看涨展望，认为苹果公司新密码应用的漏洞已修复，市场对科技行业的信心可能恢复。\n- BeOA给出了看跌展望，指出市场情绪谨慎，对科技行业的担忧依然存在。\n- NOA认为市场展望中性，市场情绪评分较低，但苹果公司的问题已得到解决。\n\n决策理由：\n- 虽然BOA和BeOA给出了相反的信号，但NOA的建议倾向于中性观望。\n- 技术分析显示，AAPL的价格趋势可能看跌，但动量可能正在减弱。\n- 基本面分析认为AAPL被低估，财务健康得分较高，竞争地位领先。\n\n风险评估：\n- 技术分析显示AAPL可能低于其移动平均线，RSI可能接近或低于30，MACD可能显示看跌交叉。\n- 基本面分析显示AAPL的估值可能低估，财务状况良好。\n\n最终决策：考虑到市场情绪的谨慎和技术的看跌信号，以及NOA的中性建议，我们倾向于保持不变。\n\n**最终决策：保持不变**', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-19'}
2025-07-28 08:02:26,318 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 08:02:26,322 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 08:02:52,533 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 08:02:52,533 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 08:03:27,410 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 08:03:27,412 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 08:03:27,412 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-20, 累计收益=0.0000, 周收益=0.0000
2025-07-28 08:03:33,530 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 08:03:33,530 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 08:03:33,530 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 08:03:33,530 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 08:03:33,530 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 08:03:33,530 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '经过对分析层智能体的输出进行综合分析，以下是我们的决策过程：\n\nNAA分析显示，大部分新闻对AAPL持积极态度，尤其是关于其人工智能和科技硬件领域的讨论，这可能会对投资者信心产生积极影响。然而，股价在一天内小幅下跌，表明市场情绪可能存在波动。TAA分析显示，AAPL的股价显示出明显的下跌趋势，技术指标也显示出看跌信号。FAA分析表明，AAPL被评估为低估，财务健康状况良好，在竞争中处于领先地位。展望层智能体中，BOA给出了看涨展望，而BeOA给出了看跌展望，NOA则认为市场展望中性。根据我们的决策规则，当BOA和BeOA给出强烈信号但方向相反时，我们优先考虑NOA的建议。由于NOA认为市场展望中性，我们倾向于保持不变。\n\n最终决策：保持不变', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-20'}
2025-07-28 08:03:33,530 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 08:03:33,533 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 08:04:10,992 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 08:04:10,993 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 08:04:48,166 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 08:04:48,166 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 08:04:48,166 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-21, 累计收益=0.0000, 周收益=0.0000
2025-07-28 08:04:55,368 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 08:04:55,369 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 08:04:55,370 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 08:04:55,370 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 08:04:55,370 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 08:04:55,370 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们综合考虑了来自分析层和展望层的智能体输出。NAA显示市场情绪较为悲观，苹果公司股价下跌，CEO Tim Cook调整AI领导层，Apple TV+亏损等问题被提及。TAA显示AAPL显示出看跌趋势，支撑位和阻力位已确定。FAA评估苹果公司低估，财务健康得分高，竞争地位领先。展望层智能体中，BOA和BeOA都给出了看跌信号，而NOA认为应中性观望。根据决策规则，由于BOA和BeOA都给出了看跌信号，且没有明显强于另一方，我们优先考虑NOA的建议，即保持中性观望。因此，最终决策为保持不变。**最终决策：保持不变**', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-21'}
2025-07-28 08:04:55,370 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 08:04:55,377 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 08:05:28,706 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 08:05:28,708 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 08:05:55,079 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 08:05:55,080 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 08:05:55,080 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-24, 累计收益=0.0000, 周收益=0.0000
2025-07-28 08:06:12,568 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 08:06:12,568 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 08:06:12,568 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 08:06:12,569 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 08:06:12,569 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 08:06:12,569 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析过程如下：\n\n展望层智能体输出分析：\n- BOA（看涨展望）认为尽管当前技术面显示看跌趋势，但其他分析层智能体的输出倾向于看涨AAPL的市场表现。\n- BeOA（看跌展望）指出NAA的情绪评分较低，TAA显示的股价下跌趋势以及FAA的财务健康评分和竞争地位均表明看跌。\n- NOA（中性展望）认为当前市场对AAPL的展望呈现中性。\n\n决策理由：\n- 由于BOA和BeOA给出的信号方向相反，且强度相当，我们优先考虑NOA的建议，即保持中性观望。\n- 分析层的NAA、TAA、FAA结果作为风险评估的参考，但不是决策主导因素。\n\n风险评估：\n- NAA的情绪评分较低，表明市场情绪可能对AAPL不利。\n- TAA显示的股价下跌趋势和MACD的看跌信号表明短期内AAPL可能面临下跌风险。\n- FAA的财务健康评分和竞争地位表明AAPL长期表现良好，但短期风险需关注。\n\n最终决策：保持不变\n\n', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-24'}
2025-07-28 08:06:12,569 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 08:06:12,576 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 08:06:39,861 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 08:06:39,862 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 08:07:02,185 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 08:07:02,187 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 08:07:02,187 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-25, 累计收益=0.0000, 周收益=0.0000
2025-07-28 08:07:20,605 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 08:07:20,606 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 08:07:20,606 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 08:07:20,606 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 08:07:20,606 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 08:07:20,606 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '经过对分析层智能体的输出进行综合分析，以下是我们的决策过程：\n\nNAA的情绪评分显示市场情绪中性，但部分新闻表现出乐观情绪，如苹果即将推出的新功能和市场对关税变化的乐观态度。TAA显示AAPL可能处于看跌趋势，但需要具体的技术指标来确认。FAA显示AAPL被低估，财务健康得分高，竞争地位领先。BOA给出看涨展望，主要基于积极的市场展望和潜在的增长潜力。BeOA给出看跌展望，主要基于情绪评分较低和市场的不确定性。NOA认为市场应中性观望。\n\n根据以上分析，虽然BOA和BeOA给出了相反的信号，但NOA的建议是中性观望。考虑到市场的整体情绪和AAPL的技术分析结果，我们决定保持中立，不进行买入或卖出的操作。\n\n**最终决策：保持不变**', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-25'}
2025-07-28 08:07:20,606 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 08:07:20,609 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-28 08:07:20,609 - __main__ - INFO - ============================================================
2025-07-28 08:07:20,609 - __main__ - INFO - 第 12 周性能分析 - 联盟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-28 08:07:20,610 - __main__ - INFO - 周总收益率: 0.0000
2025-07-28 08:07:20,610 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-28 08:07:20,610 - __main__ - INFO - 交易天数: 5
2025-07-28 08:07:20,610 - __main__ - INFO - ============================================================
2025-07-28 08:39:28,126 - __main__ - INFO - ======================================================================
2025-07-28 08:39:28,126 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-28 08:39:28,126 - __main__ - INFO - ======================================================================
2025-07-28 08:39:28,126 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-28 08:39:28,127 - __main__ - INFO - 开始模拟联盟: {'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'}
2025-07-28 08:39:28,127 - __main__ - INFO - 📅 当前周数: 13
2025-07-28 08:39:28,127 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-28 08:39:28,127 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-28 08:39:28,128 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-03-26 (第13周)
2025-07-28 08:39:28,128 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-03-26, end_date=2025-04-02
2025-07-28 08:39:28,128 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-28 08:39:28,209 - __main__ - INFO - 📅 设置当前模拟周数: 13 (来自参数)
2025-07-28 08:39:28,210 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 08:40:09,530 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 08:40:09,532 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 08:40:47,882 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 08:40:47,882 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 08:40:47,882 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-26, 累计收益=0.0000, 周收益=0.0000
2025-07-28 08:41:06,630 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 08:41:06,630 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 08:41:06,630 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 08:41:06,630 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 08:41:06,630 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 08:41:06,630 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 08:41:06,630 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 08:41:06,630 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '经过对分析层智能体的输出进行综合分析，以下是各个智能体的观点和我的决策理由。\n\nNAA（新闻分析）显示，大部分新闻对AAPL的报道情绪较为中性，但其中一些新闻表现出略微乐观的情绪，这可能对股票产生轻微的积极影响。TAA（技术分析）显示，AAPL的价格显示出一定的下跌趋势，技术指标如RSI接近超卖区域，MACD显示看跌趋势，移动平均线向下，表明趋势向下。FAA（基本面分析）显示，AAPL被评估为低估，财务健康得分较高，竞争地位领先，长期趋势看涨。BOA（看涨展望）认为，尽管技术分析显示AAPL短期内呈现看跌趋势，但综合情绪评分、新闻摘要、影响评估以及财务和竞争地位，看涨展望较为强烈。BeOA（看跌展望）则认为，苹果公司（AAPL）的股票价格显示出下跌趋势，技术分析指标支持这一观点。NOA（中性展望）认为，当前市场对AAPL的展望呈现中性。综合以上分析，虽然技术分析显示看跌趋势，但基本面分析和看涨展望较为积极。因此，我倾向于执行买入操作。\n\n**最终决策：买入**', 'action': 'buy', 'trading_actions': {'AAPL': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-26'}
2025-07-28 08:41:06,630 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'AAPL': 1.0}
2025-07-28 08:41:06,630 - __main__ - INFO - 📈 第1天交易动作: {'AAPL': 1.0}
2025-07-28 08:41:06,632 - __main__ - INFO - 💰 第1天净值变化: 收益率=0.0365, 净值=$1036543.98
2025-07-28 08:41:06,634 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 08:41:36,341 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 08:41:36,342 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 08:41:58,987 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 08:41:58,989 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 08:41:58,989 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-27, 累计收益=0.0365, 周收益=0.0365
2025-07-28 08:42:17,384 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 08:42:17,385 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 08:42:17,385 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 08:42:17,385 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 08:42:17,385 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 08:42:17,385 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们综合考虑了来自分析层和展望层的智能体输出。NAA显示大部分新闻情绪中性，但苹果公司领导层变动和潜在的新投资计划带来略微乐观的情绪。TAA显示AAPL价格呈现一定的下跌趋势，但技术指标需要进一步分析。FAA显示AAPL被低估，财务健康得分高，竞争地位领先。展望层智能体中，BOA看涨，BeOA看跌，NOA认为应中性观望。根据决策规则，由于BOA和BeOA信号相反，我们优先考虑NOA的建议，认为应保持中性观望。因此，**最终决策：保持不变**。', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-27'}
2025-07-28 08:42:17,385 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 08:42:17,390 - __main__ - INFO - 💰 第2天净值变化: 收益率=0.0046, 净值=$1041329.50
2025-07-28 08:42:17,393 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 08:42:56,810 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 08:42:56,811 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 08:43:23,589 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 08:43:23,589 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 08:43:23,589 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-28, 累计收益=0.0413, 周收益=0.0413
2025-07-28 08:43:49,269 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 08:43:49,270 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 08:43:49,270 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 08:43:49,271 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 08:43:49,271 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 08:43:49,271 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析过程如下：\n\n展望层智能体输出分析：\n- BOA给出了看涨展望，认为苹果公司投资AI革命等因素将推动股价上涨。\n- BeOA给出了看跌展望，指出市场对AI的狂热正在降温，可能会对相关公司股价产生负面影响。\n- NOA认为市场展望中性，市场情绪评分0.6表明市场情绪偏向乐观，但并非极度乐观。\n\n决策理由：\n- 虽然BOA和BeOA给出了相反的信号，但NOA的中性展望提供了一个平衡的观点。\n- 根据分析层的NAA、TAA、FAA结果，苹果公司的情绪评分和财务健康状况表明市场对苹果公司的看法是积极的。\n- 技术分析显示AAPL可能处于看跌趋势，但需要具体的技术指标数值来确认。\n\n风险评估：\n- 市场对AI的狂热降温可能对苹果公司股价产生负面影响。\n- 苹果公司的投资AI革命可能对其股价产生积极影响。\n\n最终决策：考虑到BOA和BeOA的相反信号以及NOA的中性展望，以及分析层的风险评估，我们倾向于保持不变。\n\n**最终决策：保持不变**', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-28'}
2025-07-28 08:43:49,271 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 08:43:49,274 - __main__ - INFO - 💰 第3天净值变化: 收益率=-0.0074, 净值=$1033629.17
2025-07-28 08:43:49,276 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 08:44:40,190 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 08:44:40,191 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 08:45:17,204 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 08:45:17,206 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 08:45:17,206 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-31, 累计收益=0.0336, 周收益=0.0336
2025-07-28 08:45:44,429 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 08:45:44,430 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 08:45:44,430 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 08:45:44,430 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 08:45:44,430 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 08:45:44,430 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析过程如下：\n\n展望层智能体输出分析：\n- BOA（看涨展望）给出了看涨信号，认为苹果公司（AAPL）的文化和投资策略，以及市场估值和投资建议对市场情绪有正面影响。\n- BeOA（看跌展望）给出了看跌信号，尽管技术分析显示AAPL近期有上升的趋势，但考虑到市场情绪、估值、财务健康和竞争地位等因素，认为存在看跌的风险。\n- NOA（中性展望）认为市场展望中性，根据NAA的情绪评分和新闻摘要，市场情绪较为中性偏乐观。TAA的技术分析显示AAPL近期有上升的趋势，但市场波动性较大。\n\n决策理由：\n- 由于BOA和BeOA给出了相反的信号，我们优先考虑NOA的建议，认为市场展望中性。\n- TAA的技术分析显示AAPL近期有上升的趋势，但市场波动性较大，这增加了风险。\n- FAA的基本面分析显示AAPL的估值合理，财务健康，竞争地位领先，这些都是积极的因素。\n\n风险评估：\n- 市场情绪中性偏乐观，但存在一定的波动性风险。\n- 技术分析显示上升趋势，但需要关注市场波动。\n- 基本面分析显示积极的财务和竞争地位。\n\n最终决策：保持不变\n\n', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-03-31'}
2025-07-28 08:45:44,430 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 08:45:44,432 - __main__ - INFO - 💰 第4天净值变化: 收益率=-0.0067, 净值=$1026711.91
2025-07-28 08:45:44,434 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 08:46:19,467 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 08:46:19,468 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 08:46:46,710 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 08:46:46,711 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 08:46:46,711 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-01, 累计收益=0.0267, 周收益=0.0267
2025-07-28 08:47:05,440 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 08:47:05,441 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 08:47:05,441 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 08:47:05,442 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 08:47:05,442 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 08:47:05,442 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析过程如下：\n\n展望层智能体BOA和BeOA给出了相反的信号，BOA看涨，BeOA看跌。在这种情况下，我们优先考虑NOA的建议，因为它认为应该中性观望。NAA的情绪评分表明市场情绪较为中性，TAA的技术分析显示AAPL可能处于中性状态，FAA的基本面分析也显示出合理的估值和良好的财务健康状况。综合这些信息，我们得出结论，目前没有足够的证据支持买入或卖出的决策，因此倾向于保持不变。\n\n最终决策：保持不变', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-01'}
2025-07-28 08:47:05,442 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 08:47:05,445 - __main__ - INFO - 💰 第5天净值变化: 收益率=-0.0339, 净值=$991951.62
2025-07-28 08:47:05,445 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-28 08:47:05,446 - __main__ - INFO - ============================================================
2025-07-28 08:47:05,446 - __main__ - INFO - 第 13 周性能分析 - 联盟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-28 08:47:05,446 - __main__ - INFO - 周总收益率: -0.0068
2025-07-28 08:47:05,446 - __main__ - INFO - 周夏普比率: -1.0008
2025-07-28 08:47:05,446 - __main__ - INFO - 交易天数: 5
2025-07-28 08:47:05,446 - __main__ - INFO - ============================================================

2025-07-28 09:18:24,027 - __main__ - INFO - ======================================================================
2025-07-28 09:18:24,028 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-28 09:18:24,028 - __main__ - INFO - ======================================================================
2025-07-28 09:18:24,028 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-28 09:18:24,028 - __main__ - INFO - 开始模拟联盟: {'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'}
2025-07-28 09:18:24,028 - __main__ - INFO - 📅 当前周数: 14
2025-07-28 09:18:24,028 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-28 09:18:24,028 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-28 09:18:24,029 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-04-02 (第14周)
2025-07-28 09:18:24,029 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-04-02, end_date=2025-04-09
2025-07-28 09:18:24,029 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-28 09:18:24,108 - __main__ - INFO - 📅 设置当前模拟周数: 14 (来自参数)
2025-07-28 09:18:24,109 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 09:18:48,832 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 09:18:48,833 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 09:18:58,818 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 09:18:58,818 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 09:18:58,818 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-02, 累计收益=0.0000, 周收益=0.0000
2025-07-28 09:19:15,498 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 09:19:15,498 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 09:19:15,498 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 09:19:15,498 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 09:19:15,498 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 09:19:15,498 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 09:19:15,498 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 09:19:15,499 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '经过对分析层智能体的输出进行综合分析，以下是展望层智能体的观点和决策理由。\n\n- NAA分析显示，市场情绪较为稳定，但Meta Platforms的智能眼镜计划可能对苹果公司的增强现实业务构成竞争，而苹果卡网络的保障可能有助于苹果公司在支付领域保持竞争力。\n- TAA技术分析显示，AAPL价格呈现上升趋势，支撑位在$220.00-$225.00，阻力位在$230.00-$235.00。技术指标如RSI显示中性，但MACD和移动平均线均显示看涨趋势。\n- FAA基本面分析表明，苹果公司的估值合理，财务健康得分高，市场竞争力领先。\n- BOA看涨展望认为，市场展望为看涨，主要基于积极的市场动态和因素。\n- BeOA看跌展望则指出，尽管技术分析显示AAPL短期内看涨，但市场情绪和外部因素可能对股价产生下行压力。\n- NOA中性展望认为，市场情绪较为稳定，应保持中性观望。\n\n综合以上分析，尽管BOA和BeOA给出了相反的信号，但NOA的中性展望提供了平衡的观点。考虑到技术分析和基本面分析的支持，以及市场情绪的稳定性，最终决策倾向于买入。\n**最终决策：买入**', 'action': 'buy', 'trading_actions': {'AAPL': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-02'}
2025-07-28 09:19:15,499 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'AAPL': 1.0}
2025-07-28 09:19:15,499 - __main__ - INFO - 📈 第1天交易动作: {'AAPL': 1.0}
2025-07-28 09:19:15,501 - __main__ - INFO - 💰 第1天净值变化: 收益率=0.0210, 净值=$1021007.85
2025-07-28 09:19:15,504 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 09:19:35,609 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 09:19:35,609 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 09:20:01,625 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 09:20:01,625 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 09:20:01,625 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-03, 累计收益=0.0210, 周收益=0.0210
2025-07-28 09:20:15,631 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 09:20:15,631 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 09:20:15,631 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 09:20:15,631 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 09:20:15,631 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 09:20:15,631 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '经过对分析层智能体的输出进行综合分析，以下是各个智能体的主要观点和我的决策理由。\n\nNAA分析显示，市场情绪偏向中性，尽管有部分乐观情绪，但特朗普关税政策对市场情绪造成了一定影响。TAA分析表明，AAPL的价格呈现下降趋势，动量指标显示价格下降动力有所减弱，技术指标如RSI、MACD和移动平均线均显示出中性或看跌的趋势。FAA分析认为AAPL的估值合理，财务健康得分较高，在竞争中处于领先地位。\n\n展望层智能体中，BOA给出了看涨展望，认为尽管存在一些负面因素，但整体市场展望为看涨。BeOA则给出了看跌展望，指出市场展望看跌，主要基于分析层智能体的情绪评分、新闻摘要和关键事件列表。NOA认为市场展望中性，建议保持观望。\n\n根据决策规则，如果BOA和BeOA都给出强烈信号但方向相反，优先考虑NOA的建议。由于BOA和BeOA的信号方向相反，且NOA建议保持中性观望，因此我倾向于遵循NOA的建议。\n\n最终决策：保持不变。', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-03'}
2025-07-28 09:20:15,632 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 09:20:15,636 - __main__ - INFO - 💰 第2天净值变化: 收益率=-0.0014, 净值=$1019560.55
2025-07-28 09:20:15,639 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 09:20:37,040 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 09:20:37,041 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 09:21:00,768 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 09:21:00,768 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 09:21:00,768 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-04, 累计收益=0.0196, 周收益=0.0196
2025-07-28 09:21:14,181 - __main__ - INFO - 🎯 找到最终决策声明: sell
2025-07-28 09:21:14,182 - __main__ - WARNING - 修正决策不一致: 声明sell vs 字段hold
2025-07-28 09:21:14,182 - __main__ - INFO - 一致性修正：hold -> sell
2025-07-28 09:21:14,182 - __main__ - INFO - 🤖 TRA 输出: action=sell, position_size=1.0
2025-07-28 09:21:14,182 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: sell (信心度: 0.00)
2025-07-28 09:21:14,182 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 09:21:14,182 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 09:21:14,182 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析过程如下：\n\n展望层智能体输出分析：\n- BOA（看涨展望）认为存在看涨潜力，但具体分析内容未提供。\n- BeOA（看跌展望）指出特朗普关税政策对科技股的负面影响，投资者情绪悲观。\n- NOA（中性展望）认为市场情绪复杂，既有看跌因素也有看涨因素。\n\n决策理由：\n- 虽然BOA给出了看涨信号，但BeOA的看跌信号更为强烈，且市场情绪整体偏向悲观。\n- TAA（技术分析）显示AAPL价格下跌趋势明显，技术指标也支持看跌观点。\n- FAA（基本面分析）显示AAPL的估值合理，财务健康，竞争地位领先，但长期趋势分析未提供。\n\n风险评估：\n- 特朗普关税政策对全球市场的影响存在不确定性。\n- 科技行业可能面临的经济灾难对相关股票产生负面影响。\n\n最终决策：考虑到BeOA的强烈看跌信号和TAA的技术分析结果，以及市场整体情绪，倾向于卖出AAPL。\n\n**最终决策：卖出**', 'action': 'sell', 'trading_actions': {'AAPL': -1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-04'}
2025-07-28 09:21:14,183 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'AAPL': -1.0}
2025-07-28 09:21:14,183 - __main__ - INFO - 📈 第3天交易动作: {'AAPL': -1.0}
2025-07-28 09:21:14,187 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 09:21:43,182 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 09:21:43,182 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 09:21:58,759 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 09:21:58,760 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 09:21:58,761 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-07, 累计收益=0.0196, 周收益=0.0196
2025-07-28 09:22:17,296 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 09:22:17,296 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 09:22:17,296 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 09:22:17,296 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 09:22:17,296 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 09:22:17,296 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '经过对分析层智能体的输出进行综合分析，以下是我们的决策过程：\n\nNAA显示大部分新闻对AAPL的报道呈现中性或略悲观情绪，尤其是关于关税和半导体供应链的担忧。TAA显示AAPL的价格从2025-03-31到2025-04-07显示出明显的下跌趋势，技术指标如RSI和MACD均显示出看跌信号。FAA显示AAPL的估值合理，财务健康得分较高，竞争地位领先。展望层智能体BOA给出了看涨展望，认为存在看涨潜力，而BeOA给出了看跌展望，认为市场对AAPL的展望悲观。NOA认为当前市场情绪偏向悲观，建议中性观望。\n\n根据决策规则，如果BOA和BeOA都给出强烈信号但方向相反，优先考虑NOA的建议。由于BOA和BeOA的信号方向相反，我们优先考虑NOA的建议，即保持中性观望。因此，我们决定保持当前持仓不变。\n\n**最终决策：保持不变**', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-07'}
2025-07-28 09:22:17,296 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 09:22:17,302 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 09:22:57,277 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 09:22:57,279 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 09:23:22,707 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 09:23:22,709 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 09:23:22,709 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-08, 累计收益=0.0196, 周收益=0.0196
2025-07-28 09:23:44,461 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 09:23:44,462 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 09:23:44,462 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 09:23:44,462 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 09:23:44,462 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 09:23:44,462 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析过程如下：\n\n展望层智能体信号分析：\n- BOA给出中性展望，认为市场情绪较为悲观，但未给出强烈看涨信号。\n- BeOA给出看跌展望，认为市场受苹果公司市值蒸发、特朗普关税政策等因素影响，市场展望为看跌。\n- NOA给出中性展望，认为市场情绪轻微看跌，但股票市场在特朗普关税引发的抛售后可能出现反弹。\n\n决策理由：\n- 虽然BeOA给出看跌信号，但BOA和NOA均给出中性展望，且市场情绪评分显示轻微看跌。\n- 技术分析显示AAPL处于明显的下跌趋势，且技术指标信号看跌。\n- 基本面分析显示AAPL估值合理，财务状况良好，竞争地位领先。\n\n风险评估：\n- 苹果公司市值蒸发和关税问题可能对苹果股价产生负面影响。\n- 股票市场在特朗普关税引发的抛售后可能出现反弹，可能对市场情绪产生一定程度的提振。\n- 纳斯达克指数进入熊市可能对科技股产生负面影响。\n\n最终决策：考虑到市场情绪的轻微看跌、技术面的看跌趋势以及基本面分析的结果，倾向于保持不变。\n\n**最终决策：保持不变**', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-08'}
2025-07-28 09:23:44,463 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 09:23:44,466 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-28 09:23:44,467 - __main__ - INFO - ============================================================
2025-07-28 09:23:44,467 - __main__ - INFO - 第 14 周性能分析 - 联盟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-28 09:23:44,467 - __main__ - INFO - 周总收益率: 0.0196
2025-07-28 09:23:44,467 - __main__ - INFO - 周夏普比率: 7.1168
2025-07-28 09:23:44,467 - __main__ - INFO - 交易天数: 5
2025-07-28 09:23:44,467 - __main__ - INFO - ============================================================

2025-07-28 09:55:48,424 - __main__ - INFO - ======================================================================
2025-07-28 09:55:48,424 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-28 09:55:48,424 - __main__ - INFO - ======================================================================
2025-07-28 09:55:48,424 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-28 09:55:48,424 - __main__ - INFO - 开始模拟联盟: {'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'}
2025-07-28 09:55:48,424 - __main__ - INFO - 📅 当前周数: 15
2025-07-28 09:55:48,424 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-28 09:55:48,424 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-28 09:55:48,426 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-04-09 (第15周)
2025-07-28 09:55:48,426 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-04-09, end_date=2025-04-16
2025-07-28 09:55:48,426 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-28 09:55:48,514 - __main__ - INFO - 📅 设置当前模拟周数: 15 (来自参数)
2025-07-28 09:55:48,515 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 09:56:26,398 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 09:56:26,400 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 09:56:52,627 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 09:56:52,628 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 09:56:52,628 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-09, 累计收益=0.0000, 周收益=0.0000
2025-07-28 09:57:09,670 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 09:57:09,670 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 09:57:09,671 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 09:57:09,671 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 09:57:09,671 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 09:57:09,671 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '经过对分析层智能体的输出进行综合分析，以下是我们的决策过程：\n\nNAA显示市场情绪偏向悲观，但特朗普宣布对非报复性国家暂停关税后，市场情绪有所回升。TAA显示AAPL股价在短期内显示出一定的上涨趋势，技术指标也显示出看涨信号。FAA评估AAPL的估值合理，财务健康得分高，竞争地位领先。BOA给出看涨展望，认为尽管存在贸易紧张局势的担忧，但市场情绪有所回升。BeOA给出看跌展望，主要基于情绪评分和新闻摘要中的负面信息。NOA认为市场展望中性，主要由于市场存在不确定性。\n\n根据以上分析，尽管存在看涨和看跌的信号，但NOA的建议更为中性，且在BOA和BeOA信号相反时，我们倾向于遵循NOA的建议。因此，我们决定保持当前持仓不变。\n\n**最终决策：保持不变**', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-09'}
2025-07-28 09:57:09,671 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 09:57:09,676 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 09:57:38,202 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 09:57:38,202 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 09:57:56,114 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 09:57:56,115 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 09:57:56,115 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-10, 累计收益=0.0000, 周收益=0.0000
2025-07-28 09:58:01,280 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 09:58:01,281 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 09:58:01,281 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 09:58:01,281 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 09:58:01,281 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 09:58:01,281 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们综合了来自分析层和展望层的智能体输出。NAA显示大部分新闻对AAPL的报道呈现中性到略微乐观的情绪，尽管存在一些负面报道，但整体情绪偏向正面。TAA显示AAPL在2025-04-10的价格显示出一定的下跌趋势，技术指标如RSI和MACD可能显示超卖和看跌信号。FAA评估AAPL的估值合理，财务健康得分高，竞争地位领先。展望层智能体中，BOA给出看涨展望，BeOA给出看跌展望，而NOA认为市场对AAPL的展望呈现中性。考虑到BOA和BeOA的信号方向相反，我们优先考虑NOA的中性建议。因此，综合分析后，我们得出**最终决策：保持不变**。', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-10'}
2025-07-28 09:58:01,281 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 09:58:01,284 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 09:58:38,582 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 09:58:38,582 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 09:59:02,500 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 09:59:02,501 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 09:59:02,502 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-11, 累计收益=0.0000, 周收益=0.0000
2025-07-28 09:59:15,824 - __main__ - WARNING - 未找到明确的最终决策声明，保持action=hold
2025-07-28 09:59:15,824 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 09:59:15,824 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 09:59:15,824 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 09:59:15,824 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 09:59:15,824 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们综合了来自分析层和展望层的智能体输出。NAA显示市场情绪偏向悲观，主要由于特朗普关税政策引发的市场波动和科技公司可能受到的冲击。TAA显示AAPL股价呈现下跌趋势，技术指标如RSI接近超卖区域，但MACD和移动平均线显示短期内可能存在下跌趋势。FAA评估AAPL的估值合理，财务健康得分较高，竞争地位领先。展望层智能体中，BOA给出了看涨展望，而BeOA给出了看跌展望，NOA则认为市场展望中性。根据决策规则，由于BOA和BeOA给出了相反的信号，我们优先考虑NOA的中性建议。因此，最终决策为保持不变。', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-11'}
2025-07-28 09:59:15,824 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 09:59:15,828 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 09:59:52,100 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 09:59:52,100 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 10:00:10,818 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 10:00:10,819 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 10:00:10,819 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-14, 累计收益=0.0000, 周收益=0.0000
2025-07-28 10:00:22,792 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 10:00:22,793 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 10:00:22,794 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 10:00:22,794 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 10:00:22,794 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 10:00:22,794 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '经过对分析层智能体的输出进行综合分析，以下是我们的决策过程：\n\nNAA显示大部分新闻对AAPL持中性至略微乐观态度，尤其是关于关税豁免和供应链的报道，这为AAPL带来正面影响。TAA显示AAPL的价格呈现下降趋势，技术指标如RSI、MACD和移动平均线均指向看跌趋势。FAA评估AAPL的估值合理，财务健康得分高，竞争地位领先，长期趋势看好。BOA给出看涨展望，认为尽管技术分析显示短期内可能看跌，但其他因素支持AAPL的市场表现。BeOA给出看跌展望，列举了多个看跌因素。NOA认为市场对AAPL的展望呈现中性。综合以上分析，尽管存在看跌信号，但看涨和看中性展望的信心度较高，因此我们倾向于保持不变。\n\n**最终决策：保持不变**', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-14'}
2025-07-28 10:00:22,794 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 10:00:22,801 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 10:00:55,671 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 10:00:55,672 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 10:01:18,145 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 10:01:18,146 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 10:01:18,146 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-15, 累计收益=0.0000, 周收益=0.0000
2025-07-28 10:01:23,859 - __main__ - INFO - 🎯 找到最终决策声明: buy
2025-07-28 10:01:23,860 - __main__ - WARNING - 修正决策不一致: 声明buy vs 字段hold
2025-07-28 10:01:23,861 - __main__ - INFO - 一致性修正：hold -> buy
2025-07-28 10:01:23,861 - __main__ - INFO - 🤖 TRA 输出: action=buy, position_size=1.0
2025-07-28 10:01:23,862 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: buy (信心度: 0.00)
2025-07-28 10:01:23,862 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 10:01:23,862 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 10:01:23,862 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '经过对分析层智能体的输出进行综合分析，以下是我们的决策过程：\n\nNAA的情绪评分显示市场情绪偏向中性，但苹果公司在AI领域的投资和关税豁免等正面新闻可能对股价产生积极影响。TAA的技术分析显示市场趋势看跌，但RSI接近超卖区域，MACD看跌，移动平均线看跌，表明短期内可能继续下跌。FAA的基本面分析认为苹果公司的估值合理，财务健康得分高，竞争地位领先，长期趋势看好。\n\n展望层智能体中，BOA给出看涨信号，认为苹果公司在AI领域的投资和关税豁免等积极因素将推动股价上涨。BeOA给出看跌信号，认为市场情绪偏向中性，但苹果公司的正面新闻可能对股价产生有限影响。NOA认为市场展望中性，建议保持观望。\n\n根据以上分析，尽管技术分析显示市场趋势看跌，但基本面和展望层智能体的看涨信号值得我们关注。考虑到BOA的看涨信号和NOA的中性建议，我们决定采取买入操作。\n\n**最终决策：买入**', 'action': 'buy', 'trading_actions': {'AAPL': 1.0}, 'position_size': 1.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-15'}
2025-07-28 10:01:23,863 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'AAPL': 1.0}
2025-07-28 10:01:23,863 - __main__ - INFO - 📈 第5天交易动作: {'AAPL': 1.0}
2025-07-28 10:01:23,865 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-28 10:01:23,865 - __main__ - INFO - ============================================================
2025-07-28 10:01:23,865 - __main__ - INFO - 第 15 周性能分析 - 联盟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-28 10:01:23,865 - __main__ - INFO - 周总收益率: -0.0005
2025-07-28 10:01:23,865 - __main__ - INFO - 周夏普比率: -13.8635
2025-07-28 10:01:23,865 - __main__ - INFO - 交易天数: 5
2025-07-28 10:01:23,866 - __main__ - INFO - ============================================================
2025-07-28 11:13:57,830 - __main__ - INFO - ======================================================================
2025-07-28 11:13:57,830 - __main__ - INFO - 🔍 阶段1: 完整联盟详细分析
2025-07-28 11:13:57,830 - __main__ - INFO - ======================================================================
2025-07-28 11:13:57,830 - __main__ - INFO - 📝 启用详细日志模式 - 将显示完整的LLM输入输出
2025-07-28 11:13:57,830 - __main__ - INFO - 开始模拟联盟: {'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'}
2025-07-28 11:13:57,830 - __main__ - INFO - 📅 当前周数: 16
2025-07-28 11:13:57,830 - __main__ - INFO - 📊 联盟类型判断: 完整联盟 (7/7 智能体)
2025-07-28 11:13:57,830 - __main__ - INFO - 开始联盟交易模拟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-28 11:13:57,831 - __main__ - INFO - 📅 调整交易起始日期: 从 2025-01-01 到 2025-04-16 (第16周)
2025-07-28 11:13:57,831 - __main__ - INFO - 🔒 保护调整后的日期: start_date=2025-04-16, end_date=2025-04-23
2025-07-28 11:13:57,831 - __main__ - INFO - 📋 创建交易环境: 跟踪器=True, 继承状态=True
2025-07-28 11:13:57,920 - __main__ - INFO - 📅 设置当前模拟周数: 16 (来自参数)
2025-07-28 11:13:57,921 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 11:14:30,012 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 11:14:30,012 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 11:14:54,196 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 11:14:54,198 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 11:14:54,198 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-16, 累计收益=0.0000, 周收益=0.0000
2025-07-28 11:15:09,633 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 11:15:09,634 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 11:15:09,634 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 11:15:09,634 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 11:15:09,634 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 11:15:09,634 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析过程如下：\n\n展望层智能体输出显示，BOA给出看涨信号，BeOA给出看跌信号，NOA认为市场展望中性。根据核心决策逻辑，当BOA和BeOA信号相反时，优先考虑NOA的建议。由于NOA认为市场展望中性，我们倾向于保持当前持仓不变。\n\n分析层智能体NAA的情绪评分显示大部分新闻对AAPL持积极态度，但也有一些中性新闻，需要密切关注市场动态和潜在的风险。TAA显示AAPL的价格趋势可能看跌，但需要具体的技术指标分析来确认。FAA显示AAPL的估值合理，财务健康，竞争地位领先，长期趋势向好。\n\n综合以上分析，尽管存在不同的展望，但NOA的中性展望为我们提供了保持当前持仓的依据。因此，**最终决策：保持不变**。', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-16'}
2025-07-28 11:15:09,635 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 11:15:09,638 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 11:15:51,641 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 11:15:51,641 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 11:16:27,536 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 11:16:27,537 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 11:16:27,537 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-17, 累计收益=0.0000, 周收益=0.0000
2025-07-28 11:16:40,760 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 11:16:40,760 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 11:16:40,760 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 11:16:40,760 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 11:16:40,760 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 11:16:40,760 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析过程如下：\n\n展望层智能体输出分析：\n- BOA（看涨展望）给出了看涨信号，表明市场存在积极因素。\n- BeOA（看跌展望）给出了看跌信号，表明市场存在潜在风险。\n- NOA（中性展望）认为市场整体趋势尚不明确。\n\n决策理由：\n- 由于BOA和BeOA给出了相反的信号，优先考虑NOA的建议，即保持中性观望。\n- 分析层智能体NAA、TAA、FAA的结果显示市场存在一定的波动性和不确定性，支持保持中性的决策。\n\n风险评估：\n- 市场存在一定的风险，包括Google面临诉讼、台积电关税影响等。\n- 技术分析显示市场处于中性状态，存在反弹机会。\n- 基本面分析显示苹果公司处于领先地位，但市场整体估值合理。\n\n最终决策声明：**最终决策：保持不变**', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-17'}
2025-07-28 11:16:40,760 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 11:16:40,764 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 11:16:40,764 - __main__ - INFO - TAA检测到无价格数据，建议跳过当前日期: 2025-04-18
2025-07-28 11:17:02,217 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 11:17:02,217 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 11:17:32,978 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 11:17:32,978 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 11:17:32,978 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-18, 累计收益=0.0000, 周收益=0.0000
2025-07-28 11:17:38,900 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 11:17:38,900 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 11:17:38,900 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 11:17:38,900 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 11:17:38,900 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 11:17:38,900 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '在本次分析中，我们首先考虑了新闻分析智能体NAA的输出。NAA指出，苹果公司的供应链稳定，且公司从特朗普的关税中获得了一些缓解，这可能对其股价产生积极影响。同时，市场分析人士对沃伦·巴菲特推荐的股票的讨论可能对相关股票的投资者情绪产生影响。然而，科技股的下跌可能对整个科技板块产生负面影响。展望层智能体BOA给出了看涨信号，认为市场展望为看涨，而BeOA则给出了看跌信号，预计短期内市场将呈现看跌趋势。NOA认为市场表现出一定的波动性，但整体趋势尚不明朗。考虑到BOA和BeOA的信号方向相反，我们优先考虑NOA的建议，认为应保持中性观望。技术分析智能体TAA无法进行技术分析，因此我们跳过其输出。基本面分析智能体FAA给出了合理的估值评估和良好的财务健康分数，但具体分析结果未提供。综合以上分析，我们得出**最终决策：保持不变**。', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-18'}
2025-07-28 11:17:38,901 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 11:17:38,908 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 11:18:25,765 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 11:18:25,766 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 11:19:02,192 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 11:19:02,193 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 11:19:02,193 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-21, 累计收益=0.0000, 周收益=0.0000
2025-07-28 11:19:08,511 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 11:19:08,511 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 11:19:08,511 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 11:19:08,511 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 11:19:08,511 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 11:19:08,512 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '分析过程如下：\n\n展望层智能体输出显示，BOA给出看涨信号，BeOA给出看跌信号，NOA认为市场中性。根据核心决策逻辑，当BOA和BeOA信号相反时，优先考虑NOA的建议。由于NOA认为市场中性，我们倾向于保持不变。\n\n分析层智能体NAA指出，苹果公司CEO对国家公园的关注可能对公司的品牌形象产生积极影响，而苹果股票的下跌可能对公司的股价产生负面影响。TAA显示AAPL价格趋势看跌，技术指标也支持这一观点。FAA评估苹果公司的估值合理，财务健康得分高，竞争地位领先。\n\n综合以上分析，尽管存在看涨和看跌的因素，但市场整体趋势难以明确判断。因此，最终决策为保持不变。\n\n**最终决策：保持不变**', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-21'}
2025-07-28 11:19:08,512 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 11:19:08,514 - __main__ - INFO - 🚀 并行执行分析层智能体: ['NAA', 'TAA', 'FAA']
2025-07-28 11:19:50,601 - __main__ - INFO - ✅ 分析层并行完成: 3 个智能体
2025-07-28 11:19:50,602 - __main__ - INFO - 🚀 并行执行展望层智能体: ['BOA', 'BeOA', 'NOA']
2025-07-28 11:20:31,038 - __main__ - INFO - ✅ 展望层并行完成: 3 个智能体
2025-07-28 11:20:31,040 - __main__ - INFO - 🤖 执行决策层智能体 TRA...
2025-07-28 11:20:31,040 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-22, 累计收益=0.0000, 周收益=0.0000
2025-07-28 11:20:47,724 - __main__ - INFO - 🎯 找到最终决策声明: hold
2025-07-28 11:20:47,724 - __main__ - INFO - 🤖 TRA 输出: action=hold, position_size=0.0
2025-07-28 11:20:47,724 - __main__ - INFO - ✅ 决策层智能体 TRA 完成: hold (信心度: 0.00)
2025-07-28 11:20:47,724 - __main__ - INFO - 📝 决策理由: 无理由
2025-07-28 11:20:47,724 - __main__ - INFO - ✅ 联盟实时分析完成: 7 个智能体
2025-07-28 11:20:47,724 - __main__ - INFO - 🔍 调试: TRA输出类型=<class 'dict'>, 内容={'analysis': '经过对分析层智能体的输出进行综合分析，以下是我们的决策过程：\n\nNAA显示市场情绪稳定，尽管存在一些不确定性因素，如特朗普的贸易政策和苹果公司的内部管理问题。TAA的技术分析结果显示，AAPL股价呈现下降趋势，技术指标显示短期内可能存在下跌风险。FAA的基本面分析表明，AAPL的估值合理，财务健康得分较高，竞争地位领先，长期增长前景良好。\n\n展望层智能体中，BOA给出了看涨展望，认为尽管短期内存在技术指标看跌，但长期来看AAPL的市场展望是看涨的。BeOA则给出了看跌展望，基于技术分析结果和市场情绪，认为存在下跌风险。NOA认为市场展望中性，市场情绪稳定，但存在不确定性。\n\n根据决策规则，由于BOA和BeOA给出了相反的信号，我们优先考虑NOA的中性建议。综合考虑分析层和展望层智能体的输出，我们得出结论，AAPL的长期增长前景良好，但短期内存在不确定性。因此，我们决定保持当前持仓不变。\n\n**最终决策：保持不变**', 'action': 'hold', 'trading_actions': {'__HOLD__': 1.0}, 'position_size': 0.0, 'source': 'real_time_analysis', 'timestamp': '2025-04-22'}
2025-07-28 11:20:47,724 - __main__ - INFO - 🔍 调试: 找到trading_actions字段={'__HOLD__': 1.0}
2025-07-28 11:20:47,727 - __main__ - INFO - 📅 检测到周末: 交易日=4, 周数=1, 周内交易日=5/5
2025-07-28 11:20:47,728 - __main__ - INFO - ============================================================
2025-07-28 11:20:47,728 - __main__ - INFO - 第 16 周性能分析 - 联盟: frozenset({'TAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA', 'NAA'})
2025-07-28 11:20:47,728 - __main__ - INFO - 周总收益率: 0.0000
2025-07-28 11:20:47,728 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-28 11:20:47,728 - __main__ - INFO - 交易天数: 5
2025-07-28 11:20:47,728 - __main__ - INFO - ============================================================