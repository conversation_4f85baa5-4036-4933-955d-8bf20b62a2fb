# Design Document

## Overview

The cyclical trading system will implement a continuous workflow that alternates between trading periods and optimization phases. The system will be built as an extension to the existing OPRO system, adding a new `CyclicalTradingManager` class that orchestrates the different phases while leveraging existing components.

## Architecture

### Core Components

1. **CyclicalTradingManager**: Main orchestrator that manages the cycle workflow
2. **PhaseExecutor**: Abstract base class for different phase implementations
3. **TradingPhaseExecutor**: Handles 5-day trading periods
4. **ShapleyPhaseExecutor**: Manages Shapley value calculations
5. **OptimizationPhaseExecutor**: Handles OPRO optimization
6. **CycleStateManager**: Manages cycle state persistence and recovery
7. **CycleReporter**: Generates reports and tracks metrics

### System Flow

```mermaid
stateDiagram-v2
    [*] --> Initialize
    Initialize --> TradingPhase
    TradingPhase --> ShapleyPhase : 5 days complete
    ShapleyPhase --> OptimizationPhase : Shapley calculated
    OptimizationPhase --> TradingPhase : Optimization complete
    TradingPhase --> [*] : Max cycles reached
    
    state TradingPhase {
        [*] --> RunTrading
        RunTrading --> SaveResults
        SaveResults --> [*]
    }
    
    state ShapleyPhase {
        [*] --> CalculateShapley
        CalculateShapley --> IdentifyWorstAgent
        IdentifyWorstAgent --> [*]
    }
    
    state OptimizationPhase {
        [*] --> OptimizeAgent
        OptimizeAgent --> ValidateImprovement
        ValidateImprovement --> [*]
    }
```

## Components and Interfaces

### CyclicalTradingManager

```python
class CyclicalTradingManager:
    def __init__(self, config: Dict[str, Any], logger: logging.Logger):
        self.config = config
        self.logger = logger
        self.state_manager = CycleStateManager(config)
        self.reporter = CycleReporter(config)
        self.phase_executors = self._initialize_phase_executors()
        
    def run_cyclical_system(self) -> Dict[str, Any]:
        """Main entry point for cyclical trading system"""
        
    def _execute_cycle(self, cycle_number: int) -> Dict[str, Any]:
        """Execute a single complete cycle"""
        
    def _transition_to_next_phase(self, current_phase: str) -> str:
        """Determine next phase based on current phase"""
```

### PhaseExecutor (Abstract Base)

```python
from abc import ABC, abstractmethod

class PhaseExecutor(ABC):
    def __init__(self, config: Dict[str, Any], logger: logging.Logger):
        self.config = config
        self.logger = logger
        
    @abstractmethod
    def execute(self, cycle_context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the phase and return results"""
        
    @abstractmethod
    def get_phase_name(self) -> str:
        """Return the name of this phase"""
        
    def validate_prerequisites(self, cycle_context: Dict[str, Any]) -> bool:
        """Validate that prerequisites for this phase are met"""
        return True
```

### TradingPhaseExecutor

```python
class TradingPhaseExecutor(PhaseExecutor):
    def __init__(self, config: Dict[str, Any], logger: logging.Logger):
        super().__init__(config, logger)
        self.assessor = ContributionAssessor(config, logger)
        
    def execute(self, cycle_context: Dict[str, Any]) -> Dict[str, Any]:
        """Run trading simulation for specified number of days"""
        
    def _calculate_date_range(self, cycle_number: int) -> Tuple[str, str]:
        """Calculate start and end dates for this trading period"""
```

### ShapleyPhaseExecutor

```python
class ShapleyPhaseExecutor(PhaseExecutor):
    def __init__(self, config: Dict[str, Any], logger: logging.Logger):
        super().__init__(config, logger)
        self.assessor = ContributionAssessor(config, logger)
        
    def execute(self, cycle_context: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate Shapley values and identify worst performing agent"""
        
    def _identify_worst_agent(self, shapley_values: Dict[str, float]) -> str:
        """Identify the agent with lowest Shapley value"""
```

### OptimizationPhaseExecutor

```python
class OptimizationPhaseExecutor(PhaseExecutor):
    def __init__(self, config: Dict[str, Any], logger: logging.Logger):
        super().__init__(config, logger)
        self.assessor = ContributionAssessor(config, logger, enable_opro=True)
        
    def execute(self, cycle_context: Dict[str, Any]) -> Dict[str, Any]:
        """Run OPRO optimization for the worst performing agent"""
        
    def _validate_optimization_improvement(self, before_score: float, after_score: float) -> bool:
        """Validate that optimization actually improved performance"""
```

## Data Models

### CycleContext

```python
@dataclass
class CycleContext:
    cycle_number: int
    current_phase: str
    start_date: str
    end_date: str
    trading_results: Optional[Dict[str, Any]] = None
    shapley_values: Optional[Dict[str, float]] = None
    worst_agent: Optional[str] = None
    optimization_results: Optional[Dict[str, Any]] = None
    phase_start_time: Optional[datetime] = None
    phase_metrics: Dict[str, Any] = field(default_factory=dict)
```

### CycleConfiguration

```python
@dataclass
class CycleConfiguration:
    trading_days_per_cycle: int = 5
    max_cycles: Optional[int] = None
    target_agents: Optional[List[str]] = None
    enable_trading_phase: bool = True
    enable_shapley_phase: bool = True
    enable_optimization_phase: bool = True
    pause_between_cycles: int = 0  # seconds
    auto_recovery: bool = True
    export_intermediate_results: bool = True
```

## Error Handling

### Recovery Mechanisms

1. **Phase Failure Recovery**: If a phase fails, log the error and attempt to continue to the next phase
2. **State Persistence**: Save cycle state after each phase completion to enable recovery
3. **Graceful Degradation**: If optimization fails, continue with next cycle using current agent configurations
4. **Resource Management**: Monitor memory and CPU usage, pause if resources are constrained

### Error Categories

1. **Recoverable Errors**: Network timeouts, temporary API failures
2. **Phase Errors**: Shapley calculation failures, optimization convergence issues
3. **System Errors**: Out of memory, disk space issues
4. **Configuration Errors**: Invalid parameters, missing data

## Testing Strategy

### Unit Tests

1. **CyclicalTradingManager**: Test cycle orchestration logic
2. **Phase Executors**: Test individual phase execution
3. **State Management**: Test state persistence and recovery
4. **Configuration**: Test parameter validation and defaults

### Integration Tests

1. **End-to-End Cycles**: Test complete cycle execution
2. **Phase Transitions**: Test smooth transitions between phases
3. **Error Recovery**: Test recovery from various failure scenarios
4. **Performance**: Test system performance under continuous operation

### Test Data

1. **Mock Trading Data**: Synthetic market data for consistent testing
2. **Agent Configurations**: Test agents with known performance characteristics
3. **Failure Scenarios**: Simulated failures for recovery testing

## Configuration Integration

### Command Line Interface

Add new mode to existing `run_opro_system.py`:

```bash
# Run cyclical trading system
python run_opro_system.py --mode cyclical --cycles 10 --trading-days 5

# Run with specific agents
python run_opro_system.py --mode cyclical --agents "TRA,FAA,NAA" --cycles 5

# Run with custom configuration
python run_opro_system.py --mode cyclical --config cyclical_config.json
```

### Configuration File Extensions

```json
{
  "cyclical_mode": {
    "trading_days_per_cycle": 5,
    "max_cycles": 10,
    "pause_between_cycles": 30,
    "auto_recovery": true,
    "export_intermediate_results": true,
    "phase_timeouts": {
      "trading": 3600,
      "shapley": 1800,
      "optimization": 7200
    }
  }
}
```

## Performance Considerations

1. **Memory Management**: Clear intermediate results between cycles to prevent memory leaks
2. **Database Optimization**: Use batch operations for storing cycle results
3. **Concurrent Execution**: Maintain existing concurrent execution capabilities within phases
4. **Resource Monitoring**: Track CPU, memory, and disk usage throughout cycles

## Monitoring and Observability

1. **Cycle Metrics**: Track cycle duration, success rates, improvement trends
2. **Phase Metrics**: Monitor individual phase performance and bottlenecks
3. **Agent Performance**: Track agent improvement over multiple cycles
4. **System Health**: Monitor resource usage and system stability