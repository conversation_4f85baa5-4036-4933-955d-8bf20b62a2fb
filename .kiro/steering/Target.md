<!------------------------------------------------------------------------------------
   Add Rules to this file or a short description and have <PERSON><PERSON> refine them for you:   
-------------------------------------------------------------------------------------> 
这是一个用于科研实验的代码,而不是工程代码,所以保持简洁,逻辑简单,低耦合,每一个 py 文件尽量不要超过 400 行
如果要创建一个新的类或者新的函数,先检查当前代码库是否已经存在类似的代码,减少冗余不要重复造轮子,如果不得不创建新方法,在测试完新方法能够运行之后删除老方法