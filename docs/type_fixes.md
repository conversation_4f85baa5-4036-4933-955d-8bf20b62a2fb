# 类型错误修复文档

## 问题描述

在 `contribution_assessment/trading_simulator.py` 文件中发现了类型注解错误：

```
类型"dict[str, float | int | list[Unknown]]"不可分配给返回类型"float"
```

## 问题分析

`TradingSimulator.run_simulation_for_coalition()` 方法的返回类型注解声明为 `-> float`，但实际返回的是一个包含详细模拟结果的字典。

## 修复内容

### 1. 更新返回类型注解

**修复前:**
```python
def run_simulation_for_coalition(self, 
                               coalition: Union[Set[str], List[str]], 
                               agents: Dict[str, Any],
                               simulation_days: Optional[int] = None) -> float:
```

**修复后:**
```python
def run_simulation_for_coalition(self, 
                               coalition: Union[Set[str], List[str]], 
                               agents: Dict[str, Any],
                               simulation_days: Optional[int] = None) -> Dict[str, Any]:
```

### 2. 修复无效联盟的返回值

**修复前:**
```python
if not self._validate_coalition(coalition_set):
    self.logger.warning(f"无效联盟: {coalition_set}")
    return 0.0
```

**修复后:**
```python
if not self._validate_coalition(coalition_set):
    self.logger.warning(f"无效联盟: {coalition_set}")
    return {
        "sharpe_ratio": 0.0,
        "daily_returns": [],
        "weekly_data": [],
        "total_days": 0,
        "simulation_time": 0.0,
        "error": "Invalid coalition"
    }
```

### 3. 更新文档字符串

更新了方法的文档字符串，明确说明返回值是一个包含以下字段的字典：
- `sharpe_ratio`: 联盟的夏普比率（特征函数值v(S)）
- `daily_returns`: 每日收益率列表
- `weekly_data`: 周级数据列表
- `total_days`: 总模拟天数
- `simulation_time`: 模拟耗时（秒）
- `error`: 错误信息（如果有）

### 4. 更新使用示例

更新了 `usage_guide.py` 中的使用示例，展示如何正确处理新的返回格式：

**修复前:**
```python
result = simulator.run_simulation_for_coalition(
    coalition=coalition,
    analysis_cache=analysis_cache,
    agents=your_agents_dict,
    simulation_days=30
)
print(f"最终结果: {result}")
```

**修复后:**
```python
result = simulator.run_simulation_for_coalition(
    coalition=coalition,
    agents=your_agents_dict,
    simulation_days=30
)
print(f"夏普比率: {result['sharpe_ratio']}")
print(f"总模拟天数: {result['total_days']}")
print(f"模拟耗时: {result['simulation_time']:.2f}秒")
```

## 兼容性

现有的调用代码已经具备处理新返回格式的能力。在 `assessor.py` 中，代码使用 `isinstance(simulation_result, dict)` 来检查返回值类型，并相应地提取所需的字段：

```python
if isinstance(simulation_result, dict):
    coalition_values[coalition] = simulation_result["sharpe_ratio"]
    coalition_daily_returns[coalition] = simulation_result["daily_returns"]
else:
    # 向后兼容：如果返回的是单个值
    coalition_values[coalition] = simulation_result
    coalition_daily_returns[coalition] = []
```

## 验证结果

通过测试验证了修复的正确性：
- ✅ 返回类型注解正确: `Dict[str, Any]`
- ✅ 无效联盟返回正确的字典格式
- ✅ 所有必需字段都存在且类型正确
- ✅ 类型检查器错误已解决
- ✅ 现有代码兼容性保持良好

## 影响范围

此修复主要影响：
1. `TradingSimulator.run_simulation_for_coalition()` 方法的类型注解
2. 该方法在无效输入情况下的返回值格式
3. 相关的文档和使用示例

所有现有的调用代码都能正常工作，无需额外修改。
