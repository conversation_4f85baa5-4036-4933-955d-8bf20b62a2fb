<!------------------------------------------------------------------------------------
   Add Rules to this file or a short description and have <PERSON><PERSON> refine them for you:   
-------------------------------------------------------------------------------------> 
\section{abstract}

Accurately assessing agent contributions to optimize team collaboration in dynamic LLM-based multi-agent systems (MAS) remains a critical challenge. We introduce a self-adaptive framework that addresses this by integrating two key innovations. At its core is DAG-Shapley, a novel contribution attribution algorithm that exploits the inherent Directed Acyclic Graph (DAG) structure of agent workflows. This allows it to evaluate only viable agent coalitions, dramatically reducing the computational complexity of classical Shapley values. These attribution scores then guide a Contribution-Guided Online Prompt Optimization (CG-OPO) process, automatically refining the prompts of underperforming agents to create a closed loop for autonomous self-improvement. Evaluated within a multi-agent stock trading framework, our system demonstrates superior performance over static baselines. Crucially, in this environment, DAG-Shapley achieves attribution accuracy comparable to the full Shapley value while reducing computational overhead (token consumption) by nearly 50\%. This establishes a new standard for efficient credit assignment and translates directly into significant performance gains in our complex trading environment.

\section{System Architecture and Workflow Formalization}

This section details the foundational architecture of our self-adaptive optimization framework for LLM-based multi-agent systems. We first provide an overview of the system's iterative operational cycle, followed by a formalization of the agent workflow as a Directed Acyclic Graph (DAG), which is crucial for enabling structured credit attribution and targeted optimization.

\subsection{Overall Framework Overview}

The proposed framework operates as a closed-loop, periodic optimization system, designed to continuously improve the collective performance of an LLM-based multi-agent system. Its core functionality revolves around an iterative cycle: performance evaluation $\rightarrow$ contribution attribution $\rightarrow$ bottleneck identification $\rightarrow$ prompt optimization $\rightarrow$ system update. A central controller orchestrates the execution and coordination of these distinct phases, ensuring a seamless and autonomous optimization process. Figure~\ref{fig:system_architecture} provides a high-level overview of the system architecture, illustrating the primary components and their data flow within this continuous optimization loop. Each iteration of this cycle aims to refine the behavior of individual agents based on their measured contribution to the overall system's performance, thereby fostering continuous adaptation and improvement in dynamic environments.

\begin{figure}[t]
\centering
\includegraphics[width=0.9\columnwidth]{system_architecture}
\caption{Overall system architecture showing the closed-loop optimization cycle. The central controller orchestrates five key phases: performance evaluation, contribution attribution via DAG-Shapley, bottleneck identification, prompt optimization through the Meta-Optimizer Agent, and system update with A/B testing validation.}
\label{fig:system_architecture}
\end{figure}

\subsection{Formalizing the Agent Workflow as a Directed Acyclic Graph (DAG)}

To effectively manage and optimize the interactions within a multi-agent system, we formalize its collaborative structure as a Directed Acyclic Graph (DAG) $G = (V, E)$. This formalization is fundamental for leveraging the inherent workflow dependencies in our credit assignment mechanism.

\subsubsection{Agent Definition and Roles}

Each vertex $a_i \in V$ in the DAG corresponds to an autonomous LLM-based agent. The behavior of each agent is parameterized by a natural language prompt $p_i$, which dictates its operational logic and response generation. Agents within our system are categorized into distinct types, each fulfilling a specialized role in the overall workflow. For instance, Analyst Agents are responsible for processing raw data and extracting insights, Outlook Agents synthesize information from multiple sources to form higher-level perspectives, and Trader Agents make final decisions based on the synthesized intelligence. The interactions between agents are governed by well-defined input/output interfaces, ensuring that the output of an upstream agent can serve as a valid input for a downstream agent, thereby maintaining data flow compatibility across the DAG.

\subsubsection{DAG Construction and Representation}

The topological structure of the multi-agent workflow, including the specific agents (nodes) and their inter-dependencies (edges), is defined through an external configuration mechanism. This configuration specifies the directed relationships, indicating which agent's output is consumed by another. The system parses this configuration to construct an in-memory graph representation of the DAG. This approach offers significant advantages in terms of modularity and reconfigurability, allowing for flexible adaptation to diverse tasks and rapid re-composition of the workflow without requiring modifications to the core system logic. This formal representation of the workflow is then utilized by subsequent components of our framework for structured analysis and optimization.

The DAG representation enables us to capture the inherent structure and dependencies within the multi-agent workflow, which is essential for our subsequent credit attribution mechanism. By explicitly modeling the information flow and dependencies between agents, we can identify which subsets of agents form functionally viable coalitions and compute their respective contributions to the overall system performance in a computationally efficient manner.

\section{Optimization Framework: DAG-Shapley and CG-OPO}

This section delves into the core algorithmic contributions of our framework: DAG-Shapley for structure-aware credit attribution and Contribution-Guided Online Prompt Optimization (CG-OPO) for continuous, adaptive system improvement. These two components form the algorithmic backbone of our self-adaptive multi-agent system.

\subsection{DAG-Shapley: Structure-Aware Credit Attribution}

A fundamental challenge in multi-agent systems is the fair and efficient attribution of collective outcomes to individual agent contributions. While the classical Shapley value offers axiomatic fairness, its exponential computational complexity ($O(2^N)$ for $N$ agents) renders it impractical for real-time optimization, especially in LLM-intensive workflows where each evaluation incurs significant computational cost. Our DAG-Shapley method addresses this by exploiting the workflow's DAG structure to significantly prune the computational space. Unlike traditional approaches that consider all $2^N$ possible coalitions, DAG-Shapley focuses solely on functionally viable subsets, a key distinction from existing graph-based attribution methods.

\subsubsection{Defining and Identifying Functional Coalitions}

The core insight of DAG-Shapley is that only subsets of agents capable of forming a complete, meaningful workflow path contribute to the final system utility. We formally define a \textbf{Functional Coalition} $C \subseteq V$ as a subset of agents such that there exists at least one complete path $P = \langle s, \dots, t \rangle \subseteq C$ from a designated source node $s \in V_{\text{source}}$ to a sink node $t \in V_{\text{sink}}$. Coalitions that do not satisfy this criterion are inherently non-functional and yield a predefined baseline utility (e.g., zero or a penalty value). This crucial pruning principle underpins the efficiency of DAG-Shapley.

The identification of functional coalitions involves a graph traversal algorithm (e.g., Breadth-First Search or Depth-First Search) applied to the DAG. This process systematically explores all paths from source nodes to sink nodes, and from these paths, constructs the set of all unique functional coalitions $C_F$. This method significantly reduces the search space from $2^N$ (all possible coalitions) to $|C_F|$, which can be orders of magnitude smaller in structured workflows.

\subsubsection{Coalition Utility Evaluation}

For each identified functional coalition $C \in C_F$, its utility $v(C)$ is quantitatively assessed. This utility represents the performance achieved by the specific set of agents when operating collaboratively. In our experimental setup, which focuses on financial trading, the utility function $v(C)$ is defined as the Sharpe Ratio, a standard metric for risk-adjusted return.

The process of calculating $v(C)$ involves instantiating and running the specific functional coalition within a high-fidelity financial market simulation environment. A dedicated simulation execution module coordinates this process, interacting with the simulation environment to mimic the trading activities of the coalition over a defined evaluation period (e.g., one week). Upon completion of the simulation, a performance analysis module processes the simulated results to compute the Sharpe Ratio, which serves as the objective utility value $v(C)$ for that specific coalition. This robust and objective utility measurement forms the bedrock for accurate contribution attribution.

\subsubsection{DAG-Shapley Value Calculation}

With the set of functional coalitions $C_F$ and their corresponding utilities $v(C)$ established, the DAG-Shapley value $\varphi_i$ for each agent $a_i$ is computed. This calculation adheres to the principles of the classical Shapley value but is applied exclusively over the significantly reduced set of functional coalitions. The formula for $\varphi_i$ is given by:

\begin{equation}
\varphi_i = \sum_{\substack{C \subseteq V \setminus \{a_i\} \\ C \cup \{a_i\} \in C_F}} \frac{|C|! \, (|V| - |C| - 1)!}{|V|!} \, [v(C \cup \{a_i\}) - v(C)]
\end{equation}

Here, the summation is explicitly restricted to coalitions $C$ such that $C \cup \{a_i\}$ forms a functional coalition. This ensures that only marginal contributions that lead to or enhance a functional workflow are considered. A dedicated contribution calculation module implements this algorithm, utilizing the pre-filtered list of functional coalitions and their evaluated utilities to compute each agent's contribution score.

\textbf{Computational Complexity Analysis:} The computational advantage of DAG-Shapley stems from the reduction in the number of utility evaluations. While the classical Shapley value requires $O(2^N)$ evaluations, DAG-Shapley reduces this to $O(|C_F|)$. The cardinality $|C_F|$ is highly dependent on the DAG's structure. For instance, in a layered DAG with depth $d$ and maximum in-degree $k$, an upper bound on $|C_F|$ can be derived, which is exponentially smaller than $2^{|V|}$ when $k \ll |V|$. However, in the worst-case scenario (e.g., a complete graph), $|C_F|$ can still approach $2^N$. Our empirical results demonstrate that in typical workflow DAGs, this structural pruning leads to a substantial reduction in computational overhead. The detailed procedure for DAG-Shapley calculation is presented in Algorithm~\ref{alg:dag_shapley}.

\begin{algorithm}[tb]
\caption{DAG-Shapley Calculation}
\label{alg:dag_shapley}
\textbf{Input}: DAG $G = (V, E)$, Utility function $v$\\
\textbf{Output}: Shapley values $\varphi$ for all agents $a \in V$
\begin{algorithmic}[1]
\STATE Initialize $\varphi_i = 0$ for all $a_i \in V$
\STATE Identify $C_F$, the set of all functional coalitions in $G$ (e.g., using graph traversal from source to sink nodes)
\FOR{each agent $a_i$ in $V$}
    \FOR{each coalition $C \subseteq V \setminus \{a_i\}$}
        \IF{$C \cup \{a_i\} \in C_F$}
            \STATE $marginal\_contribution = v(C \cup \{a_i\}) - v(C)$
            \STATE $weight = \frac{|C|! \, (|V| - |C| - 1)!}{|V|!}$
            \STATE $\varphi_i = \varphi_i + weight \times marginal\_contribution$
        \ENDIF
    \ENDFOR
\ENDFOR
\STATE \textbf{return} $\varphi$
\end{algorithmic}
\end{algorithm}

\subsection{Contribution-Guided Online Prompt Optimization (CG-OPO)}

The DAG-Shapley values serve as the critical feedback signal for our Contribution-Guided Online Prompt Optimization (CG-OPO) framework, a closed-loop mechanism enabling continuous self-adaptation of the multi-agent system. This iterative refinement process operates over discrete cycles (e.g., weekly) and is orchestrated by a central system controller.

\subsubsection{Overall CG-OPO Framework}

The CG-OPO framework leverages the insights from DAG-Shapley to drive continuous improvement in agent performance. It operates in an iterative manner, where each cycle involves evaluating the system, attributing contributions, identifying bottlenecks, and refining the prompts of underperforming agents. This closed-loop design allows the system to autonomously adapt and evolve its strategies over time. The overall procedure for the CG-OPO framework is presented in Algorithm~\ref{alg:cg_opo}.

\begin{algorithm}[tb]
\caption{Contribution-Guided Online Prompt Optimization (CG-OPO) Framework}
\label{alg:cg_opo}
\textbf{Input}: DAG $G = (V, E)$, Initial prompts $P_0$, Meta-Optimizer Agent $M$\\
\textbf{Output}: Optimized prompts $P$
\begin{algorithmic}[1]
\STATE $P = P_0$
\FOR{each cycle $t = 1, 2, \dots, T_{\text{max}}$}
    \STATE // Step 1: Performance Evaluation
    \STATE $v_t = \text{EvaluatePerformance}(G, P, T_t)$
    \STATE
    \STATE // Step 2: Contribution Attribution
    \STATE $\varphi_t = \text{DAG\_Shapley\_Calculation}(G, v_t)$ // Using Algorithm~\ref{alg:dag_shapley}
    \STATE
    \STATE // Step 3: Bottleneck Identification
    \STATE $a_{\text{worst}} = \text{argmin}_{a_i \in V}(\varphi_{i,t})$
    \STATE $p_{\text{worst}} = P[a_{\text{worst}}]$
    \STATE
    \STATE // Step 4: Prompt Refinement
    \STATE $H_{\text{worst}} = \text{GetFailureCases}(a_{\text{worst}}, t)$
    \STATE $p'_{\text{worst}} = M(p_{\text{worst}}, \varphi_{\text{worst},t}, H_{\text{worst}})$
    \STATE
    \STATE // Step 5: System Update
    \STATE $P[a_{\text{worst}}] = p'_{\text{worst}}$
\ENDFOR
\STATE \textbf{return} $P$
\end{algorithmic}
\end{algorithm}

\subsubsection{Bottleneck Identification}

At the end of each operational cycle, after the DAG-Shapley values $\varphi_i$ for all agents have been computed, the system identifies the primary bottleneck. This is achieved by pinpointing the agent $a_{\text{worst}}$ with the minimum contribution score: $a_{\text{worst}} = \text{argmin}_{a_i \in V} \varphi_i$. This identification logic is handled by a dedicated assessment module, which processes the output from the contribution calculation module to determine the weakest link in the workflow.

\subsubsection{Meta-Optimizer Agent and Prompt Refinement}

Once $a_{\text{worst}}$ is identified, a specialized \textbf{Meta-Optimizer Agent} $M$ is invoked to generate an improved prompt for this underperforming agent. The Meta-Optimizer Agent itself is an LLM, tasked with the specific function of refining prompts. It receives several crucial inputs: the identity of $a_{\text{worst}}$, its current prompt $p_{\text{worst}}$, its low contribution score $\varphi_{\text{worst}}$, and potentially historical logs detailing specific failure cases or suboptimal decisions made by $a_{\text{worst}}$. Leveraging its own LLM capabilities, the Meta-Optimizer Agent synthesizes this information to construct a refined prompt $p'_{\text{worst}}$. This new prompt is designed to address the identified weaknesses, guiding $a_{\text{worst}}$ towards more effective behavior in subsequent cycles.

\subsubsection{Adaptive System Update and Safety Mechanisms}

The newly generated prompt $p'_{\text{worst}}$ replaces $p_{\text{worst}}$ for the next operational cycle. To mitigate the risk of prompt oscillation or performance degradation, our framework incorporates a robust A/B testing system. After a prompt optimization cycle, both the newly optimized agent system and the original system (with the pre-optimization prompt) are run in parallel over a subsequent evaluation period. Their performances are then rigorously compared using the same utility function (e.g., Sharpe Ratio). The system automatically selects the superior-performing configuration for the next operational cycle. This dynamic system selection acts as a crucial safety net: if the performance of an agent with a newly optimized prompt significantly degrades, the system effectively "rolls back" by retaining the original, better-performing prompt. This iterative refinement process, guided by empirical validation through A/B testing, allows the system to progressively enhance its performance by focusing its optimization efforts on the most critical weaknesses, enabling robust self-adaptation in dynamic environments.