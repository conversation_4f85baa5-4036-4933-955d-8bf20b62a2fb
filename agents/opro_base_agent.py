"""
OPRO优化基础智能体类 (OPRO Base Agent)

扩展原有的BaseAgent类，增加对OPRO动态提示词优化的支持。
主要功能：
1. 管理动态提示词的更新和版本控制
2. 与OPRO优化器集成
3. 跟踪提示词性能变化
4. 支持A/B测试和回滚机制
"""

import json
import logging
import numpy as np
from typing import Dict, Any, Optional, List
from datetime import datetime
from abc import abstractmethod

from .base_agent import BaseAgent

class OPROBaseAgent(BaseAgent):
    """
    支持OPRO优化的基础智能体类
    
    继承自BaseAgent，添加动态提示词管理功能，
    支持提示词的优化、版本控制和性能跟踪。
    """
    
    def __init__(self, 
                 agent_id: str, 
                 llm_interface=None, 
                 logger: Optional[logging.Logger] = None,
                 opro_enabled: bool = False,
                 opro_optimizer=None,
                 max_prompt_history: int = 50):
        """
        初始化OPRO基础智能体
        
        参数:
            agent_id: 智能体唯一标识符
            llm_interface: LLM接口实例
            logger: 日志记录器
            opro_enabled: 是否启用OPRO优化
            opro_optimizer: OPRO优化器实例
            max_prompt_history: 最大提示词历史记录数
        """
        super().__init__(agent_id, llm_interface, logger)
        
        # OPRO相关配置
        self.opro_enabled = opro_enabled
        self.opro_optimizer = opro_optimizer
        self.max_prompt_history = max_prompt_history
        
        # 动态提示词管理
        self.current_prompt = self.get_default_prompt_template()
        self.prompt_history = []
        self.performance_history = []
        
        # 版本控制
        self.prompt_version = "1.0.0"
        self.last_optimization_time = None
        
        # A/B测试支持
        self.ab_test_active = False
        self.ab_test_prompts = {}
        self.ab_test_results = {}
        
        # 统计信息
        self._opro_stats = {
            "total_optimizations": 0,
            "successful_optimizations": 0,
            "performance_improvements": 0,
            "rollbacks": 0,
            "ab_tests_conducted": 0
        }
        
        self.logger.info(f"OPRO智能体 {self.agent_id} 初始化完成 (OPRO: {'启用' if opro_enabled else '禁用'})")
    
    def get_prompt_template(self) -> str:
        """
        获取当前的提示词模板
        
        如果启用了OPRO优化，返回当前的动态提示词；
        否则返回默认的静态提示词。
        
        返回:
            当前提示词模板字符串
        """
        if self.opro_enabled and self.current_prompt:
            # self.logger.debug(f"使用OPRO优化提示词 (版本: {self.prompt_version})")
            return self.current_prompt
        else:
            return self.get_default_prompt_template()
    
    @abstractmethod
    def get_default_prompt_template(self) -> str:
        """
        获取默认的静态提示词模板
        
        子类必须实现此方法，提供智能体的默认提示词。
        
        返回:
            默认提示词模板字符串
        """
        pass
    
    def update_prompt(self, 
                     new_prompt: str, 
                     version: Optional[str] = None,
                     source: str = "manual",
                     metadata: Optional[Dict] = None) -> bool:
        """
        更新智能体的提示词
        
        参数:
            new_prompt: 新的提示词内容
            version: 版本号，如果为None则自动生成
            source: 更新来源 (manual, opro, ab_test等)
            metadata: 额外的元数据
            
        返回:
            是否更新成功
        """
        if not new_prompt or len(new_prompt.strip()) < 10:
            self.logger.warning(f"提示词更新失败: 内容过短或为空")
            return False
        
        try:
            # 保存当前提示词到历史记录
            self._save_to_history(
                prompt=self.current_prompt,
                version=self.prompt_version,
                source="previous",
                timestamp=datetime.now()
            )
            
            # 更新提示词
            old_prompt = self.current_prompt
            self.current_prompt = new_prompt.strip()
            
            # 更新版本号
            if version:
                self.prompt_version = version
            else:
                self.prompt_version = self._generate_next_version()
            
            # 记录更新信息
            update_info = {
                "old_prompt_preview": old_prompt[:100] + "..." if len(old_prompt) > 100 else old_prompt,
                "new_prompt_preview": new_prompt[:100] + "..." if len(new_prompt) > 100 else new_prompt,
                "version": self.prompt_version,
                "source": source,
                "metadata": metadata or {},
                "timestamp": datetime.now().isoformat()
            }
            
            self.prompt_history.append(update_info)
            
            # 限制历史记录大小
            if len(self.prompt_history) > self.max_prompt_history:
                self.prompt_history = self.prompt_history[-self.max_prompt_history:]
            
            self.logger.info(f"提示词更新成功: {self.agent_id} v{self.prompt_version} (来源: {source})")
            return True
            
        except Exception as e:
            self.logger.error(f"提示词更新失败: {e}")
            return False
    
    def _save_to_history(self, prompt: str, version: str, source: str, timestamp: datetime):
        """保存提示词到历史记录"""
        history_entry = {
            "prompt": prompt,
            "version": version,
            "source": source,
            "timestamp": timestamp.isoformat(),
            "performance_data": None  # 将在性能评估后填充
        }
        
        # 避免重复保存相同的提示词
        if (not self.prompt_history or 
            self.prompt_history[-1].get("prompt") != prompt):
            self.prompt_history.append(history_entry)
    
    def _generate_next_version(self) -> str:
        """生成下一个版本号"""
        try:
            # 解析当前版本号
            parts = self.prompt_version.split('.')
            if len(parts) >= 3:
                major, minor, patch = int(parts[0]), int(parts[1]), int(parts[2])
                # 递增补丁版本号
                return f"{major}.{minor}.{patch + 1}"
            else:
                # 如果版本格式不正确，从1.0.1开始
                return "1.0.1"
        except ValueError:
            # 如果解析失败，使用时间戳
            timestamp = datetime.now().strftime("%Y%m%d%H%M")
            return f"1.0.{timestamp}"
    
    def optimize_with_opro(self, force: bool = False) -> Dict[str, Any]:
        """
        使用OPRO优化当前提示词
        
        参数:
            force: 是否强制优化，忽略时间间隔限制
            
        返回:
            优化结果字典
        """
        if not self.opro_enabled:
            return {
                "success": False,
                "error": "OPRO优化未启用",
                "agent_id": self.agent_id
            }
        
        if not self.opro_optimizer:
            return {
                "success": False,
                "error": "OPRO优化器未配置",
                "agent_id": self.agent_id
            }
        
        # 检查优化间隔（避免过于频繁的优化）
        if not force and self.last_optimization_time:
            time_since_last = datetime.now() - self.last_optimization_time
            if time_since_last.total_seconds() < 3600:  # 1小时间隔
                return {
                    "success": False,
                    "error": "优化间隔太短，请稍后再试",
                    "agent_id": self.agent_id,
                    "time_since_last": time_since_last.total_seconds()
                }
        
        try:
            self.logger.info(f"开始OPRO优化: {self.agent_id}")
            
            # 调用OPRO优化器
            optimization_result = self.opro_optimizer.optimize_agent_prompt(
                agent_id=self.agent_id,
                current_prompt=self.current_prompt
            )
            
            if optimization_result.get("success", False):
                # 获取优化后的提示词
                optimized_prompt = optimization_result.get("optimized_prompt", "")
                estimated_improvement = optimization_result.get("improvement", 0)
                
                if estimated_improvement > 0:
                    # 更新提示词
                    success = self.update_prompt(
                        new_prompt=optimized_prompt,
                        source="opro",
                        metadata={
                            "estimated_improvement": estimated_improvement,
                            "estimated_score": optimization_result.get("estimated_score", 0),
                            "optimization_details": optimization_result
                        }
                    )
                    
                    if success:
                        self._opro_stats["successful_optimizations"] += 1
                        if estimated_improvement > 0:
                            self._opro_stats["performance_improvements"] += 1
                        
                        self.last_optimization_time = datetime.now()
                        
                        self.logger.info(f"OPRO优化成功: {self.agent_id}, "
                                       f"预期改进: {estimated_improvement:.6f}")
                    
                    optimization_result["prompt_updated"] = success
                else:
                    self.logger.info(f"OPRO优化完成但无改进: {self.agent_id}")
                    optimization_result["prompt_updated"] = False
            
            self._opro_stats["total_optimizations"] += 1
            return optimization_result
            
        except Exception as e:
            self.logger.error(f"OPRO优化失败: {self.agent_id} - {e}")
            return {
                "success": False,
                "error": str(e),
                "agent_id": self.agent_id
            }
    
    def start_ab_test(self, 
                     candidate_prompts: List[str], 
                     test_name: str = None) -> Dict[str, Any]:
        """
        启动A/B测试
        
        参数:
            candidate_prompts: 候选提示词列表
            test_name: 测试名称
            
        返回:
            A/B测试启动结果
        """
        if self.ab_test_active:
            return {
                "success": False,
                "error": "已有A/B测试在进行中",
                "agent_id": self.agent_id
            }
        
        if len(candidate_prompts) < 2:
            return {
                "success": False,
                "error": "A/B测试至少需要2个候选提示词",
                "agent_id": self.agent_id
            }
        
        try:
            test_name = test_name or f"ab_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # 设置A/B测试
            self.ab_test_active = True
            self.ab_test_prompts = {
                "control": self.current_prompt,
                **{f"variant_{i}": prompt for i, prompt in enumerate(candidate_prompts)}
            }
            self.ab_test_results = {name: [] for name in self.ab_test_prompts.keys()}
            
            self._opro_stats["ab_tests_conducted"] += 1
            
            self.logger.info(f"A/B测试启动: {self.agent_id} - {test_name} "
                           f"({len(self.ab_test_prompts)}个变体)")
            
            return {
                "success": True,
                "test_name": test_name,
                "agent_id": self.agent_id,
                "variants": list(self.ab_test_prompts.keys()),
                "total_variants": len(self.ab_test_prompts)
            }
            
        except Exception as e:
            self.logger.error(f"A/B测试启动失败: {self.agent_id} - {e}")
            return {
                "success": False,
                "error": str(e),
                "agent_id": self.agent_id
            }
    
    def get_ab_test_prompt(self, variant_name: str = None) -> str:
        """
        获取A/B测试中的提示词
        
        参数:
            variant_name: 变体名称，如果为None则随机选择
            
        返回:
            测试提示词
        """
        if not self.ab_test_active or not self.ab_test_prompts:
            return self.get_prompt_template()
        
        if variant_name and variant_name in self.ab_test_prompts:
            return self.ab_test_prompts[variant_name]
        
        # 随机选择一个变体
        import random
        variant_name = random.choice(list(self.ab_test_prompts.keys()))
        return self.ab_test_prompts[variant_name]
    
    def record_ab_test_result(self, 
                            variant_name: str, 
                            performance_score: float,
                            metadata: Optional[Dict] = None):
        """
        记录A/B测试结果
        
        参数:
            variant_name: 变体名称
            performance_score: 性能得分
            metadata: 额外的元数据
        """
        if not self.ab_test_active or variant_name not in self.ab_test_results:
            self.logger.warning(f"无效的A/B测试结果记录: {variant_name}")
            return
        
        result_entry = {
            "score": performance_score,
            "timestamp": datetime.now().isoformat(),
            "metadata": metadata or {}
        }

        self.ab_test_results[variant_name].append(result_entry)
        # self.logger.debug(f"A/B测试结果记录: {variant_name} -> {performance_score:.6f}")
    
    def analyze_ab_test_results(self) -> Dict[str, Any]:
        """
        分析A/B测试结果
        
        返回:
            A/B测试分析结果
        """
        if not self.ab_test_active or not self.ab_test_results:
            return {
                "success": False,
                "error": "没有活跃的A/B测试",
                "agent_id": self.agent_id
            }
        
        try:
            analysis = {}
            
            for variant_name, results in self.ab_test_results.items():
                if not results:
                    analysis[variant_name] = {
                        "sample_size": 0,
                        "average_score": 0.0,
                        "std_dev": 0.0,
                        "min_score": 0.0,
                        "max_score": 0.0
                    }
                    continue
                
                scores = [r["score"] for r in results]
                analysis[variant_name] = {
                    "sample_size": len(scores),
                    "average_score": np.mean(scores),
                    "std_dev": np.std(scores),
                    "min_score": min(scores),
                    "max_score": max(scores),
                    "prompt_preview": self.ab_test_prompts[variant_name][:100] + "..."
                }
            
            # 找出最佳变体
            valid_variants = {k: v for k, v in analysis.items() if v["sample_size"] > 0}
            if valid_variants:
                best_variant = max(valid_variants.items(), key=lambda x: x[1]["average_score"])
                worst_variant = min(valid_variants.items(), key=lambda x: x[1]["average_score"])
                
                return {
                    "success": True,
                    "agent_id": self.agent_id,
                    "variant_analysis": analysis,
                    "best_variant": {
                        "name": best_variant[0],
                        "average_score": best_variant[1]["average_score"],
                        "sample_size": best_variant[1]["sample_size"]
                    },
                    "worst_variant": {
                        "name": worst_variant[0],
                        "average_score": worst_variant[1]["average_score"],
                        "sample_size": worst_variant[1]["sample_size"]
                    },
                    "improvement": best_variant[1]["average_score"] - worst_variant[1]["average_score"]
                }
            else:
                return {
                    "success": False,
                    "error": "没有足够的测试数据",
                    "agent_id": self.agent_id
                }
                
        except Exception as e:
            self.logger.error(f"A/B测试分析失败: {self.agent_id} - {e}")
            return {
                "success": False,
                "error": str(e),
                "agent_id": self.agent_id
            }
    
    def finish_ab_test(self, adopt_best: bool = True) -> Dict[str, Any]:
        """
        结束A/B测试
        
        参数:
            adopt_best: 是否采用最佳变体作为新的提示词
            
        返回:
            测试结束结果
        """
        if not self.ab_test_active:
            return {
                "success": False,
                "error": "没有活跃的A/B测试",
                "agent_id": self.agent_id
            }
        
        try:
            # 分析测试结果
            analysis = self.analyze_ab_test_results()
            
            result = {
                "success": True,
                "agent_id": self.agent_id,
                "test_analysis": analysis,
                "prompt_updated": False
            }
            
            if adopt_best and analysis.get("success"):
                best_variant = analysis["best_variant"]
                best_prompt = self.ab_test_prompts[best_variant["name"]]
                
                # 只有当最佳变体确实比当前版本更好时才更新
                if best_variant["name"] != "control" and best_variant["average_score"] > 0:
                    success = self.update_prompt(
                        new_prompt=best_prompt,
                        source="ab_test",
                        metadata={
                            "ab_test_analysis": analysis,
                            "adopted_variant": best_variant["name"],
                            "improvement": best_variant["average_score"]
                        }
                    )
                    result["prompt_updated"] = success
                    
                    if success:
                        self.logger.info(f"A/B测试完成，采用最佳变体: {self.agent_id} "
                                       f"({best_variant['name']}, 得分: {best_variant['average_score']:.6f})")
            
            # 清理A/B测试状态
            self.ab_test_active = False
            self.ab_test_prompts = {}
            self.ab_test_results = {}
            
            return result
            
        except Exception as e:
            self.logger.error(f"A/B测试结束失败: {self.agent_id} - {e}")
            return {
                "success": False,
                "error": str(e),
                "agent_id": self.agent_id
            }
    
    def rollback_prompt(self, target_version: str = None) -> Dict[str, Any]:
        """
        回滚到之前的提示词版本
        
        参数:
            target_version: 目标版本号，如果为None则回滚到上一个版本
            
        返回:
            回滚结果
        """
        if not self.prompt_history:
            return {
                "success": False,
                "error": "没有可回滚的版本",
                "agent_id": self.agent_id
            }
        
        try:
            # 查找目标版本
            target_entry = None
            
            if target_version:
                # 查找指定版本
                for entry in reversed(self.prompt_history):
                    if entry.get("version") == target_version:
                        target_entry = entry
                        break
            else:
                # 回滚到上一个版本
                if len(self.prompt_history) >= 1:
                    target_entry = self.prompt_history[-1]
            
            if not target_entry:
                return {
                    "success": False,
                    "error": f"未找到目标版本: {target_version}",
                    "agent_id": self.agent_id
                }
            
            # 执行回滚
            old_prompt = self.current_prompt
            old_version = self.prompt_version
            
            success = self.update_prompt(
                new_prompt=target_entry["prompt"],
                version=target_entry["version"] + ".rollback",
                source="rollback",
                metadata={
                    "rollback_from_version": old_version,
                    "rollback_to_version": target_entry["version"],
                    "rollback_reason": "manual_rollback"
                }
            )
            
            if success:
                self._opro_stats["rollbacks"] += 1
                self.logger.info(f"提示词回滚成功: {self.agent_id} "
                               f"{old_version} -> {target_entry['version']}")
            
            return {
                "success": success,
                "agent_id": self.agent_id,
                "rollback_from": old_version,
                "rollback_to": target_entry["version"],
                "target_entry": target_entry
            }
            
        except Exception as e:
            self.logger.error(f"提示词回滚失败: {self.agent_id} - {e}")
            return {
                "success": False,
                "error": str(e),
                "agent_id": self.agent_id
            }
    
    def record_performance(self, score: float, context: str = "", metadata: Optional[Dict] = None):
        """
        记录智能体性能
        
        参数:
            score: 性能得分
            context: 性能上下文描述
            metadata: 额外的元数据
        """
        performance_entry = {
            "score": score,
            "context": context,
            "prompt_version": self.prompt_version,
            "timestamp": datetime.now().isoformat(),
            "metadata": metadata or {}
        }
        
        self.performance_history.append(performance_entry)
        
        # 限制性能历史记录大小
        if len(self.performance_history) > self.max_prompt_history * 2:
            self.performance_history = self.performance_history[-self.max_prompt_history:]

        # self.logger.debug(f"性能记录: {self.agent_id} v{self.prompt_version} -> {score:.6f}")
    
    def get_performance_trends(self, window_size: int = 10) -> Dict[str, Any]:
        """
        获取性能趋势分析
        
        参数:
            window_size: 分析窗口大小
            
        返回:
            性能趋势分析结果
        """
        if len(self.performance_history) < 2:
            return {
                "trend": "insufficient_data",
                "current_score": 0.0,
                "average_score": 0.0,
                "data_points": len(self.performance_history)
            }
        
        recent_scores = [entry["score"] for entry in self.performance_history[-window_size:]]
        all_scores = [entry["score"] for entry in self.performance_history]
        
        return {
            "trend": "improving" if recent_scores[-1] > recent_scores[0] else "declining",
            "current_score": recent_scores[-1],
            "average_score": np.mean(all_scores),
            "recent_average": np.mean(recent_scores),
            "score_variance": np.var(all_scores),
            "data_points": len(self.performance_history),
            "version_performance": self._analyze_version_performance()
        }
    
    def _analyze_version_performance(self) -> Dict[str, Any]:
        """分析不同版本的性能"""
        version_scores = {}
        
        for entry in self.performance_history:
            version = entry["prompt_version"]
            score = entry["score"]
            
            if version not in version_scores:
                version_scores[version] = []
            version_scores[version].append(score)
        
        version_analysis = {}
        for version, scores in version_scores.items():
            version_analysis[version] = {
                "average_score": np.mean(scores),
                "sample_size": len(scores),
                "best_score": max(scores),
                "worst_score": min(scores)
            }
        
        return version_analysis
    
    def get_opro_stats(self) -> Dict[str, Any]:
        """获取OPRO统计信息"""
        stats = self._opro_stats.copy()
        
        # 计算成功率
        if stats["total_optimizations"] > 0:
            stats["optimization_success_rate"] = (
                stats["successful_optimizations"] / stats["total_optimizations"]
            ) * 100
            stats["improvement_rate"] = (
                stats["performance_improvements"] / stats["total_optimizations"]
            ) * 100
        else:
            stats["optimization_success_rate"] = 0.0
            stats["improvement_rate"] = 0.0
        
        # 添加其他信息
        stats.update({
            "agent_id": self.agent_id,
            "opro_enabled": self.opro_enabled,
            "current_version": self.prompt_version,
            "prompt_history_length": len(self.prompt_history),
            "performance_history_length": len(self.performance_history),
            "ab_test_active": self.ab_test_active,
            "last_optimization": self.last_optimization_time.isoformat() if self.last_optimization_time else None
        })
        
        return stats
    
    def record_weekly_io(self, input_state: Dict[str, Any], llm_raw_response: Dict[str, Any], timestamp: Optional[str] = None,
                        is_full_coalition: bool = True) -> None:
        """
        记录本周的完整输入状态和LLM原始响应
        
        参数:
            input_state: 完整的输入状态字典（从env.get_state()获取）
            llm_raw_response: LLM返回的完整原始响应
            timestamp: 时间戳，如果为None则使用当前时间
            is_full_coalition: 是否为完整联盟的IO数据，只有为True时才记录
        """
        if not is_full_coalition:
            self.logger.debug(f"{self.agent_id} 跳过记录子集IO数据.")
            return
        try:
            # 确保有weekly_io_data属性
            if not hasattr(self, 'weekly_io_data'):
                self.weekly_io_data = []
            
            # 使用提供的时间戳或生成新的
            if timestamp is None:
                timestamp = datetime.now().isoformat()
            
            # 创建记录条目
            record_entry = {
                "timestamp": timestamp,
                "agent_id": self.agent_id,
                "input_state": input_state.copy(),  # 完整的输入状态
                "llm_raw_response": llm_raw_response.copy(),  # LLM的完整原始响应
                "prompt_version": self.prompt_version,
                "previous_day_return": input_state.get("previous_day_return", 0.0)  # 重点记录上一日收益率
            }
            
            # 添加到记录列表
            self.weekly_io_data.append(record_entry)
            
            # 限制记录数量（保留最近100条记录）
            if len(self.weekly_io_data) > 100:
                self.weekly_io_data = self.weekly_io_data[-100:]
            
            # 记录到日志（简要信息）
            previous_return = input_state.get("previous_day_return", 0.0)
            self.logger.debug(f"{self.agent_id} 记录IO数据: 上日收益率={previous_return:.6f}, 记录总数={len(self.weekly_io_data)}")
            
        except Exception as e:
            self.logger.error(f"{self.agent_id} 记录周IO数据失败: {e}")
    
    def get_weekly_io_data(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        获取周IO数据记录
        
        参数:
            limit: 返回记录数量限制，None表示返回所有记录
            
        返回:
            IO数据记录列表
        """
        if not hasattr(self, 'weekly_io_data'):
            return []
        
        if limit is None:
            return self.weekly_io_data.copy()
        else:
            return self.weekly_io_data[-limit:] if limit > 0 else []
    
    def clear_weekly_io_data(self) -> None:
        """清空周IO数据记录"""
        if hasattr(self, 'weekly_io_data'):
            self.weekly_io_data.clear()
            self.logger.info(f"{self.agent_id} 已清空周IO数据记录")
    
    def get_prompt_summary(self, detailed: bool = True) -> Dict[str, Any]:
        """
        获取智能体提示词状态摘要
        
        参数:
            detailed: 是否返回详细信息
            
        返回:
            提示词状态摘要字典
        """
        current_prompt = self.current_prompt or self.get_default_prompt_template()
        
        # 基础信息
        summary = {
            "agent_id": self.agent_id,
            "prompt_length": len(current_prompt),
            "prompt_version": self.prompt_version,
            "opro_enabled": self.opro_enabled,
            "optimization_count": self._opro_stats.get("total_optimizations", 0),
            "successful_optimizations": self._opro_stats.get("successful_optimizations", 0),
            "last_optimization": self.last_optimization_time.isoformat() if self.last_optimization_time else "从未优化"
        }
        
        if detailed:
            # 详细信息
            prompt_preview = current_prompt[:200] + "..." if len(current_prompt) > 200 else current_prompt
            
            summary.update({
                "prompt_preview": prompt_preview,
                "history_count": len(self.prompt_history),
                "performance_records": len(self.performance_history),
                "ab_test_active": self.ab_test_active,
                "rollback_count": self._opro_stats.get("rollbacks", 0),
                "improvement_count": self._opro_stats.get("performance_improvements", 0)
            })
            
            # 性能趋势
            if self.performance_history:
                recent_scores = [entry["score"] for entry in self.performance_history[-5:]]
                summary["recent_performance"] = {
                    "latest_score": recent_scores[-1] if recent_scores else 0.0,
                    "average_recent": np.mean(recent_scores) if recent_scores else 0.0,
                    "trend": "improving" if len(recent_scores) >= 2 and recent_scores[-1] > recent_scores[0] else "stable/declining"
                }
        
        return summary
    
    def format_prompt_log(self, include_preview: bool = True) -> str:
        """
        格式化提示词信息用于日志输出
        
        参数:
            include_preview: 是否包含提示词预览
            
        返回:
            格式化的日志字符串
        """
        summary = self.get_prompt_summary(detailed=True)
        
        # 基础状态行
        status_line = f"📝 {summary['agent_id']}: v{summary['prompt_version']} ({summary['prompt_length']}字符)"
        
        # OPRO状态
        if summary['opro_enabled']:
            opro_status = f"🧠 OPRO: {summary['successful_optimizations']}/{summary['optimization_count']}次优化"
            if summary['last_optimization'] != "从未优化":
                opro_status += f", 最后优化: {summary['last_optimization'][:10]}"
        else:
            opro_status = "🔒 OPRO: 禁用"
        
        # 性能状态
        perf_status = ""
        if summary.get('recent_performance'):
            perf = summary['recent_performance']
            perf_status = f"📊 性能: {perf['latest_score']:.4f} ({perf['trend']})"
        
        # 组合输出
        log_lines = [status_line, f"   {opro_status}"]
        if perf_status:
            log_lines.append(f"   {perf_status}")
        
        # 提示词预览
        if include_preview and summary.get('prompt_preview'):
            preview_lines = summary['prompt_preview'].split('\n')
            log_lines.append(f"   💬 预览: {preview_lines[0][:100]}...")
        
        return '\n'.join(log_lines)
    
    def process(self, state: Dict[str, Any], is_full_coalition: bool = True) -> Dict[str, Any]:
        """
        处理输入状态并生成分析结果
        
        参数:
            state: 包含市场数据、历史状态等信息的状态字典
            is_full_coalition: 是否为完整联盟的评估
        返回:
            分析结果字典
        """
        return self.call_llm(self.get_prompt_template(), state, is_full_coalition=is_full_coalition)
    
    def export_prompt_data(self) -> Dict[str, Any]:
        """导出提示词相关数据"""
        return {
            "agent_id": self.agent_id,
            "current_prompt": self.current_prompt,
            "current_version": self.prompt_version,
            "opro_enabled": self.opro_enabled,
            "prompt_history": self.prompt_history,
            "performance_history": self.performance_history,
            "opro_stats": self._opro_stats,
            "export_timestamp": datetime.now().isoformat()
        }