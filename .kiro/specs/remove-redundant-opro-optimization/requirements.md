# 需求文档

## 介绍

当前系统存在两个独立的优化流程：常规OPRO优化和基于贡献度评估的CG-OPRO优化。根据日志分析，常规OPRO优化缺乏具体的性能反馈数据，导致优化效果不佳，且与CG-OPRO形成重复优化。本功能旨在移除冗余的常规OPRO优化流程，只保留基于DAG-Shapley值评估驱动的CG-OPRO优化机制，实现更精准和高效的系统优化。

## 需求

### 需求 1

**用户故事：** 作为系统架构师，我希望移除常规的OPRO优化流程，以避免与CG-OPRO产生重复优化和资源浪费。

#### 验收标准

1. 当系统启动时，系统应该跳过常规OPRO优化阶段
2. 当系统运行时，系统不应该执行任何预设的、非反馈驱动的优化任务
3. 当系统完成一个评估周期时，系统应该只触发基于Shapley值评估结果的CG-OPRO优化
4. 当查看系统日志时，应该不再出现"OPRO优化阶段完成"等常规优化相关的日志信息

### 需求 2

**用户故事：** 作为系统开发者，我希望保留并优化CG-OPRO机制，确保基于贡献度评估的优化流程正常工作。

#### 验收标准

1. 当Shapley值计算完成时，系统应该自动触发针对性的CG-OPRO优化
2. 当识别出表现最差的智能体时，系统应该对该智能体执行CG-OPRO提示词优化


