[{"line_number": "16", "timestamp": "2025-07-28 21:18:59,588", "date": "2025-01-02", "cumulative_return": "0.0000", "weekly_return": "0.0000", "full_line": "2025-07-28 21:18:59,588 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-02, 累计收益=0.0000, 周收益=0.0000"}, {"line_number": "33", "timestamp": "2025-07-28 21:19:57,562", "date": "2025-01-03", "cumulative_return": "0.0284", "weekly_return": "0.0284", "full_line": "2025-07-28 21:19:57,562 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-03, 累计收益=0.0284, 周收益=0.0284"}, {"line_number": "50", "timestamp": "2025-07-28 21:20:50,561", "date": "2025-01-06", "cumulative_return": "0.0703", "weekly_return": "0.0703", "full_line": "2025-07-28 21:20:50,561 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-06, 累计收益=0.0703, 周收益=0.0703"}, {"line_number": "67", "timestamp": "2025-07-28 21:22:04,140", "date": "2025-01-07", "cumulative_return": "0.0943", "weekly_return": "0.0943", "full_line": "2025-07-28 21:22:04,140 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-07, 累计收益=0.0943, 周收益=0.0943"}, {"line_number": "82", "timestamp": "2025-07-28 21:23:10,782", "date": "2025-01-08", "cumulative_return": "0.0851", "weekly_return": "0.0851", "full_line": "2025-07-28 21:23:10,782 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-08, 累计收益=0.0851, 周收益=0.0851"}, {"line_number": "118", "timestamp": "2025-07-28 21:56:39,103", "date": "2025-01-08", "cumulative_return": "0.0000", "weekly_return": "0.0000", "full_line": "2025-07-28 21:56:39,103 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-08, 累计收益=0.0000, 周收益=0.0000"}, {"line_number": "136", "timestamp": "2025-07-28 21:57:27,664", "date": "2025-01-09", "cumulative_return": "0.0209", "weekly_return": "0.0209", "full_line": "2025-07-28 21:57:27,664 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-09, 累计收益=0.0209, 周收益=0.0209"}, {"line_number": "151", "timestamp": "2025-07-28 21:58:46,214", "date": "2025-01-10", "cumulative_return": "0.0070", "weekly_return": "0.0070", "full_line": "2025-07-28 21:58:46,214 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-10, 累计收益=0.0070, 周收益=0.0070"}, {"line_number": "167", "timestamp": "2025-07-28 21:59:51,695", "date": "2025-01-13", "cumulative_return": "0.0070", "weekly_return": "0.0070", "full_line": "2025-07-28 21:59:51,695 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-13, 累计收益=0.0070, 周收益=0.0070"}, {"line_number": "181", "timestamp": "2025-07-28 22:00:30,685", "date": "2025-01-14", "cumulative_return": "0.0070", "weekly_return": "0.0070", "full_line": "2025-07-28 22:00:30,685 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-14, 累计收益=0.0070, 周收益=0.0070"}, {"line_number": "214", "timestamp": "2025-07-28 22:33:09,715", "date": "2025-01-15", "cumulative_return": "0.0000", "weekly_return": "0.0000", "full_line": "2025-07-28 22:33:09,715 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-15, 累计收益=0.0000, 周收益=0.0000"}, {"line_number": "231", "timestamp": "2025-07-28 22:34:13,312", "date": "2025-01-16", "cumulative_return": "0.0489", "weekly_return": "0.0489", "full_line": "2025-07-28 22:34:13,312 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-16, 累计收益=0.0489, 周收益=0.0489"}, {"line_number": "248", "timestamp": "2025-07-28 22:35:24,179", "date": "2025-01-17", "cumulative_return": "0.0410", "weekly_return": "0.0410", "full_line": "2025-07-28 22:35:24,179 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-17, 累计收益=0.0410, 周收益=0.0410"}, {"line_number": "269", "timestamp": "2025-07-28 22:36:11,238", "date": "2025-01-20", "cumulative_return": "0.0465", "weekly_return": "0.0465", "full_line": "2025-07-28 22:36:11,238 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-20, 累计收益=0.0465, 周收益=0.0465"}, {"line_number": "287", "timestamp": "2025-07-28 22:37:09,510", "date": "2025-01-21", "cumulative_return": "0.0128", "weekly_return": "0.0128", "full_line": "2025-07-28 22:37:09,510 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-21, 累计收益=0.0128, 周收益=0.0128"}, {"line_number": "323", "timestamp": "2025-07-28 23:09:57,004", "date": "2025-01-22", "cumulative_return": "0.0000", "weekly_return": "0.0000", "full_line": "2025-07-28 23:09:57,004 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-22, 累计收益=0.0000, 周收益=0.0000"}, {"line_number": "340", "timestamp": "2025-07-28 23:10:49,020", "date": "2025-01-23", "cumulative_return": "0.0066", "weekly_return": "0.0066", "full_line": "2025-07-28 23:10:49,020 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-23, 累计收益=0.0066, 周收益=0.0066"}, {"line_number": "357", "timestamp": "2025-07-28 23:12:59,030", "date": "2025-01-24", "cumulative_return": "-0.0050", "weekly_return": "-0.0050", "full_line": "2025-07-28 23:12:59,030 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-24, 累计收益=-0.0050, 周收益=-0.0050"}, {"line_number": "374", "timestamp": "2025-07-28 23:14:22,285", "date": "2025-01-27", "cumulative_return": "0.0164", "weekly_return": "0.0164", "full_line": "2025-07-28 23:14:22,285 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-27, 累计收益=0.0164, 周收益=0.0164"}, {"line_number": "391", "timestamp": "2025-07-28 23:15:33,552", "date": "2025-01-28", "cumulative_return": "0.0192", "weekly_return": "0.0192", "full_line": "2025-07-28 23:15:33,552 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-28, 累计收益=0.0192, 周收益=0.0192"}, {"line_number": "425", "timestamp": "2025-07-28 23:49:56,436", "date": "2025-01-29", "cumulative_return": "0.0000", "weekly_return": "0.0000", "full_line": "2025-07-28 23:49:56,436 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-29, 累计收益=0.0000, 周收益=0.0000"}, {"line_number": "439", "timestamp": "2025-07-28 23:51:04,802", "date": "2025-01-30", "cumulative_return": "0.0000", "weekly_return": "0.0000", "full_line": "2025-07-28 23:51:04,802 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-30, 累计收益=0.0000, 周收益=0.0000"}, {"line_number": "454", "timestamp": "2025-07-28 23:52:19,360", "date": "2025-01-31", "cumulative_return": "0.0348", "weekly_return": "0.0348", "full_line": "2025-07-28 23:52:19,360 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-01-31, 累计收益=0.0348, 周收益=0.0348"}, {"line_number": "470", "timestamp": "2025-07-28 23:53:32,455", "date": "2025-02-03", "cumulative_return": "0.0342", "weekly_return": "0.0342", "full_line": "2025-07-28 23:53:32,455 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-03, 累计收益=0.0342, 周收益=0.0342"}, {"line_number": "486", "timestamp": "2025-07-28 23:54:49,687", "date": "2025-02-04", "cumulative_return": "0.0342", "weekly_return": "0.0342", "full_line": "2025-07-28 23:54:49,687 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-04, 累计收益=0.0342, 周收益=0.0342"}, {"line_number": "522", "timestamp": "2025-07-29 00:28:04,489", "date": "2025-02-05", "cumulative_return": "0.0000", "weekly_return": "0.0000", "full_line": "2025-07-29 00:28:04,489 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-05, 累计收益=0.0000, 周收益=0.0000"}, {"line_number": "539", "timestamp": "2025-07-29 00:28:48,968", "date": "2025-02-06", "cumulative_return": "-0.0269", "weekly_return": "-0.0269", "full_line": "2025-07-29 00:28:48,968 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-06, 累计收益=-0.0269, 周收益=-0.0269"}, {"line_number": "556", "timestamp": "2025-07-29 00:29:47,371", "date": "2025-02-07", "cumulative_return": "0.0036", "weekly_return": "0.0036", "full_line": "2025-07-29 00:29:47,371 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-07, 累计收益=0.0036, 周收益=0.0036"}, {"line_number": "573", "timestamp": "2025-07-29 00:30:45,740", "date": "2025-02-10", "cumulative_return": "-0.0106", "weekly_return": "-0.0106", "full_line": "2025-07-29 00:30:45,740 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-10, 累计收益=-0.0106, 周收益=-0.0106"}, {"line_number": "590", "timestamp": "2025-07-29 00:31:45,321", "date": "2025-02-11", "cumulative_return": "-0.0329", "weekly_return": "-0.0329", "full_line": "2025-07-29 00:31:45,321 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-11, 累计收益=-0.0329, 周收益=-0.0329"}, {"line_number": "626", "timestamp": "2025-07-29 01:05:15,219", "date": "2025-02-12", "cumulative_return": "0.0000", "weekly_return": "0.0000", "full_line": "2025-07-29 01:05:15,219 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-12, 累计收益=0.0000, 周收益=0.0000"}, {"line_number": "643", "timestamp": "2025-07-29 01:06:12,744", "date": "2025-02-13", "cumulative_return": "-0.0122", "weekly_return": "-0.0122", "full_line": "2025-07-29 01:06:12,744 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-13, 累计收益=-0.0122, 周收益=-0.0122"}, {"line_number": "660", "timestamp": "2025-07-29 01:07:14,937", "date": "2025-02-14", "cumulative_return": "-0.0234", "weekly_return": "-0.0234", "full_line": "2025-07-29 01:07:14,937 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-14, 累计收益=-0.0234, 周收益=-0.0234"}, {"line_number": "678", "timestamp": "2025-07-29 01:08:04,828", "date": "2025-02-17", "cumulative_return": "-0.0100", "weekly_return": "-0.0100", "full_line": "2025-07-29 01:08:04,828 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-17, 累计收益=-0.0100, 周收益=-0.0100"}, {"line_number": "695", "timestamp": "2025-07-29 01:09:39,382", "date": "2025-02-18", "cumulative_return": "0.0205", "weekly_return": "0.0205", "full_line": "2025-07-29 01:09:39,382 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-18, 累计收益=0.0205, 周收益=0.0205"}, {"line_number": "731", "timestamp": "2025-07-29 01:43:39,460", "date": "2025-02-19", "cumulative_return": "0.0000", "weekly_return": "0.0000", "full_line": "2025-07-29 01:43:39,460 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-19, 累计收益=0.0000, 周收益=0.0000"}, {"line_number": "748", "timestamp": "2025-07-29 01:45:48,379", "date": "2025-02-20", "cumulative_return": "0.0039", "weekly_return": "0.0039", "full_line": "2025-07-29 01:45:48,379 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-20, 累计收益=0.0039, 周收益=0.0039"}, {"line_number": "765", "timestamp": "2025-07-29 01:46:50,702", "date": "2025-02-21", "cumulative_return": "0.0019", "weekly_return": "0.0019", "full_line": "2025-07-29 01:46:50,702 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-21, 累计收益=0.0019, 周收益=0.0019"}, {"line_number": "782", "timestamp": "2025-07-29 01:47:59,129", "date": "2025-02-24", "cumulative_return": "-0.0190", "weekly_return": "-0.0190", "full_line": "2025-07-29 01:47:59,129 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-24, 累计收益=-0.0190, 周收益=-0.0190"}, {"line_number": "799", "timestamp": "2025-07-29 01:49:16,949", "date": "2025-02-25", "cumulative_return": "-0.0156", "weekly_return": "-0.0156", "full_line": "2025-07-29 01:49:16,949 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-25, 累计收益=-0.0156, 周收益=-0.0156"}, {"line_number": "835", "timestamp": "2025-07-29 02:21:41,492", "date": "2025-02-26", "cumulative_return": "0.0000", "weekly_return": "0.0000", "full_line": "2025-07-29 02:21:41,492 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-26, 累计收益=0.0000, 周收益=0.0000"}, {"line_number": "852", "timestamp": "2025-07-29 02:22:58,285", "date": "2025-02-27", "cumulative_return": "-0.0233", "weekly_return": "-0.0233", "full_line": "2025-07-29 02:22:58,285 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-27, 累计收益=-0.0233, 周收益=-0.0233"}, {"line_number": "867", "timestamp": "2025-07-29 02:24:19,823", "date": "2025-02-28", "cumulative_return": "0.0060", "weekly_return": "0.0060", "full_line": "2025-07-29 02:24:19,823 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-02-28, 累计收益=0.0060, 周收益=0.0060"}, {"line_number": "884", "timestamp": "2025-07-29 02:25:14,300", "date": "2025-03-03", "cumulative_return": "0.0508", "weekly_return": "0.0508", "full_line": "2025-07-29 02:25:14,300 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-03, 累计收益=0.0508, 周收益=0.0508"}, {"line_number": "899", "timestamp": "2025-07-29 02:26:13,878", "date": "2025-03-04", "cumulative_return": "0.0868", "weekly_return": "0.0868", "full_line": "2025-07-29 02:26:13,878 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-04, 累计收益=0.0868, 周收益=0.0868"}, {"line_number": "935", "timestamp": "2025-07-29 02:59:34,279", "date": "2025-03-05", "cumulative_return": "0.0000", "weekly_return": "0.0000", "full_line": "2025-07-29 02:59:34,279 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-05, 累计收益=0.0000, 周收益=0.0000"}, {"line_number": "952", "timestamp": "2025-07-29 03:00:30,266", "date": "2025-03-06", "cumulative_return": "-0.0622", "weekly_return": "-0.0622", "full_line": "2025-07-29 03:00:30,266 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-06, 累计收益=-0.0622, 周收益=-0.0622"}, {"line_number": "968", "timestamp": "2025-07-29 03:01:23,388", "date": "2025-03-07", "cumulative_return": "-0.0624", "weekly_return": "-0.0624", "full_line": "2025-07-29 03:01:23,388 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-07, 累计收益=-0.0624, 周收益=-0.0624"}, {"line_number": "985", "timestamp": "2025-07-29 03:02:12,595", "date": "2025-03-10", "cumulative_return": "-0.0905", "weekly_return": "-0.0905", "full_line": "2025-07-29 03:02:12,595 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-10, 累计收益=-0.0905, 周收益=-0.0905"}, {"line_number": "1002", "timestamp": "2025-07-29 03:03:16,354", "date": "2025-03-11", "cumulative_return": "-0.1084", "weekly_return": "-0.1084", "full_line": "2025-07-29 03:03:16,354 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-11, 累计收益=-0.1084, 周收益=-0.1084"}, {"line_number": "1038", "timestamp": "2025-07-29 03:36:06,407", "date": "2025-03-12", "cumulative_return": "0.0000", "weekly_return": "0.0000", "full_line": "2025-07-29 03:36:06,407 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-12, 累计收益=0.0000, 周收益=0.0000"}, {"line_number": "1055", "timestamp": "2025-07-29 03:37:04,340", "date": "2025-03-13", "cumulative_return": "-0.0110", "weekly_return": "-0.0110", "full_line": "2025-07-29 03:37:04,340 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-13, 累计收益=-0.0110, 周收益=-0.0110"}, {"line_number": "1072", "timestamp": "2025-07-29 03:37:43,513", "date": "2025-03-14", "cumulative_return": "0.0226", "weekly_return": "0.0226", "full_line": "2025-07-29 03:37:43,513 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-14, 累计收益=0.0226, 周收益=0.0226"}, {"line_number": "1089", "timestamp": "2025-07-29 03:38:45,697", "date": "2025-03-17", "cumulative_return": "0.0026", "weekly_return": "0.0026", "full_line": "2025-07-29 03:38:45,697 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-17, 累计收益=0.0026, 周收益=0.0026"}, {"line_number": "1106", "timestamp": "2025-07-29 03:39:46,155", "date": "2025-03-18", "cumulative_return": "0.0336", "weekly_return": "0.0336", "full_line": "2025-07-29 03:39:46,155 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-18, 累计收益=0.0336, 周收益=0.0336"}, {"line_number": "1140", "timestamp": "2025-07-29 04:12:57,544", "date": "2025-03-19", "cumulative_return": "0.0000", "weekly_return": "0.0000", "full_line": "2025-07-29 04:12:57,544 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-19, 累计收益=0.0000, 周收益=0.0000"}, {"line_number": "1157", "timestamp": "2025-07-29 04:14:06,752", "date": "2025-03-20", "cumulative_return": "0.0443", "weekly_return": "0.0443", "full_line": "2025-07-29 04:14:06,752 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-20, 累计收益=0.0443, 周收益=0.0443"}, {"line_number": "1174", "timestamp": "2025-07-29 04:15:45,587", "date": "2025-03-21", "cumulative_return": "0.0454", "weekly_return": "0.0454", "full_line": "2025-07-29 04:15:45,587 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-21, 累计收益=0.0454, 周收益=0.0454"}, {"line_number": "1191", "timestamp": "2025-07-29 04:17:01,214", "date": "2025-03-24", "cumulative_return": "0.0127", "weekly_return": "0.0127", "full_line": "2025-07-29 04:17:01,214 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-24, 累计收益=0.0127, 周收益=0.0127"}, {"line_number": "1208", "timestamp": "2025-07-29 04:18:09,993", "date": "2025-03-25", "cumulative_return": "-0.1591", "weekly_return": "-0.1591", "full_line": "2025-07-29 04:18:09,993 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-25, 累计收益=-0.1591, 周收益=-0.1591"}, {"line_number": "1244", "timestamp": "2025-07-29 04:51:03,411", "date": "2025-03-26", "cumulative_return": "0.0000", "weekly_return": "0.0000", "full_line": "2025-07-29 04:51:03,411 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-26, 累计收益=0.0000, 周收益=0.0000"}, {"line_number": "1264", "timestamp": "2025-07-29 04:55:05,706", "date": "2025-03-27", "cumulative_return": "0.0893", "weekly_return": "0.0893", "full_line": "2025-07-29 04:55:05,706 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-27, 累计收益=0.0893, 周收益=0.0893"}, {"line_number": "1279", "timestamp": "2025-07-29 04:56:00,808", "date": "2025-03-28", "cumulative_return": "0.0446", "weekly_return": "0.0446", "full_line": "2025-07-29 04:56:00,808 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-28, 累计收益=0.0446, 周收益=0.0446"}, {"line_number": "1294", "timestamp": "2025-07-29 04:57:13,939", "date": "2025-03-31", "cumulative_return": "0.0526", "weekly_return": "0.0526", "full_line": "2025-07-29 04:57:13,939 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-03-31, 累计收益=0.0526, 周收益=0.0526"}, {"line_number": "1308", "timestamp": "2025-07-29 04:58:31,070", "date": "2025-04-01", "cumulative_return": "0.0526", "weekly_return": "0.0526", "full_line": "2025-07-29 04:58:31,070 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-01, 累计收益=0.0526, 周收益=0.0526"}, {"line_number": "1344", "timestamp": "2025-07-29 05:31:44,062", "date": "2025-04-02", "cumulative_return": "0.0000", "weekly_return": "0.0000", "full_line": "2025-07-29 05:31:44,062 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-02, 累计收益=0.0000, 周收益=0.0000"}, {"line_number": "1361", "timestamp": "2025-07-29 05:32:53,204", "date": "2025-04-03", "cumulative_return": "0.0171", "weekly_return": "0.0171", "full_line": "2025-07-29 05:32:53,204 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-03, 累计收益=0.0171, 周收益=0.0171"}, {"line_number": "1378", "timestamp": "2025-07-29 05:33:47,140", "date": "2025-04-04", "cumulative_return": "0.0700", "weekly_return": "0.0700", "full_line": "2025-07-29 05:33:47,140 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-04, 累计收益=0.0700, 周收益=0.0700"}, {"line_number": "1394", "timestamp": "2025-07-29 05:35:11,744", "date": "2025-04-07", "cumulative_return": "0.0700", "weekly_return": "0.0700", "full_line": "2025-07-29 05:35:11,744 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-07, 累计收益=0.0700, 周收益=0.0700"}, {"line_number": "1411", "timestamp": "2025-07-29 05:36:33,849", "date": "2025-04-08", "cumulative_return": "0.0797", "weekly_return": "0.0797", "full_line": "2025-07-29 05:36:33,849 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-08, 累计收益=0.0797, 周收益=0.0797"}, {"line_number": "1445", "timestamp": "2025-07-29 06:09:47,541", "date": "2025-04-09", "cumulative_return": "0.0000", "weekly_return": "0.0000", "full_line": "2025-07-29 06:09:47,541 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-09, 累计收益=0.0000, 周收益=0.0000"}, {"line_number": "1462", "timestamp": "2025-07-29 06:11:01,665", "date": "2025-04-10", "cumulative_return": "-0.0058", "weekly_return": "-0.0058", "full_line": "2025-07-29 06:11:01,665 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-10, 累计收益=-0.0058, 周收益=-0.0058"}, {"line_number": "1479", "timestamp": "2025-07-29 06:12:04,900", "date": "2025-04-11", "cumulative_return": "-0.0182", "weekly_return": "-0.0182", "full_line": "2025-07-29 06:12:04,900 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-11, 累计收益=-0.0182, 周收益=-0.0182"}, {"line_number": "1496", "timestamp": "2025-07-29 06:13:15,269", "date": "2025-04-14", "cumulative_return": "0.0129", "weekly_return": "0.0129", "full_line": "2025-07-29 06:13:15,269 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-14, 累计收益=0.0129, 周收益=0.0129"}, {"line_number": "1513", "timestamp": "2025-07-29 06:14:42,185", "date": "2025-04-15", "cumulative_return": "0.0395", "weekly_return": "0.0395", "full_line": "2025-07-29 06:14:42,185 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-15, 累计收益=0.0395, 周收益=0.0395"}, {"line_number": "1549", "timestamp": "2025-07-29 06:48:20,480", "date": "2025-04-16", "cumulative_return": "0.0000", "weekly_return": "0.0000", "full_line": "2025-07-29 06:48:20,480 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-16, 累计收益=0.0000, 周收益=0.0000"}, {"line_number": "1565", "timestamp": "2025-07-29 06:49:44,229", "date": "2025-04-17", "cumulative_return": "0.0000", "weekly_return": "0.0000", "full_line": "2025-07-29 06:49:44,229 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-17, 累计收益=0.0000, 周收益=0.0000"}, {"line_number": "1583", "timestamp": "2025-07-29 06:51:16,860", "date": "2025-04-18", "cumulative_return": "0.0063", "weekly_return": "0.0063", "full_line": "2025-07-29 06:51:16,860 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-18, 累计收益=0.0063, 周收益=0.0063"}, {"line_number": "1598", "timestamp": "2025-07-29 06:52:41,065", "date": "2025-04-21", "cumulative_return": "-0.0345", "weekly_return": "-0.0345", "full_line": "2025-07-29 06:52:41,065 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-21, 累计收益=-0.0345, 周收益=-0.0345"}, {"line_number": "1614", "timestamp": "2025-07-29 06:54:14,533", "date": "2025-04-22", "cumulative_return": "-0.0345", "weekly_return": "-0.0345", "full_line": "2025-07-29 06:54:14,533 - __main__ - INFO - 🤖 TRA 输入: 日期=2025-04-22, 累计收益=-0.0345, 周收益=-0.0345"}]