# 实施计划

- [ ] 1. 修改ContributionAssessor移除常规OPRO优化
  - 在`contribution_assessment/assessor.py`中修改`run`方法的第4阶段
  - 移除对`_run_opro_optimization_phase`的调用
  - 直接跳过第4阶段或用占位符替代
  - _需求: 1.1, 1.2_

- [ ] 2. 修改`_run_opro_optimization_phase`方法实现CG-OPRO逻辑
  - 在现有的`_run_opro_optimization_phase`方法中集成CG-OPRO逻辑
  - 基于第3阶段的Shapley计算结果识别最差智能体
  - 只对表现最差的智能体执行优化
  - _需求: 2.1, 2.2_

- [ ] 3. 移除WeeklyOptimizationManager中的重复优化触发
  - 修改`WeeklyOptimizationManager`，移除独立的优化触发逻辑
  - 保留评估功能，但不再单独触发优化
  - 确保不会产生重复的优化调用
  - _需求: 1.3, 1.4_

- [ ] 4. 改进失败案例数据收集
  - 修改现有的失败案例收集逻辑，提供更好的数据给CG-OPRO
  - 当无法获取失败案例时，提供替代的优化指导信息
  - 解决"智能体BOA无法获取任何真实的失败案例数据"的问题
  - _需求: 4.1, 4.2, 4.3_

- [ ] 5. 更新日志信息区分优化类型
  - 修改优化相关的日志输出，明确标识CG-OPRO优化
  - 移除常规OPRO相关的日志信息
  - 添加基于贡献度评估触发优化的日志记录
  - _需求: 3.1, 3.2, 3.3_

- [ ] 6. 测试验证修改效果
  - 运行系统验证只有一次CG-OPRO优化被触发
  - 确认常规OPRO优化不再执行
  - 验证CG-OPRO能够正确识别和优化最差智能体
  - _需求: 2.3, 2.4_