"""
阶段协调器服务

负责协调六个执行阶段的顺序执行和数据传递：
1. 联盟生成阶段 (Coalition Generation) - 包含子集运算
2. 交易模拟阶段 (Trading Simulation)
3. Shapley值计算阶段 (Shapley Calculation)
4. 性能评估阶段 (Performance Evaluation) - 问题识别
5. OPRO优化阶段 (OPRO Optimization)
6. 结果固化阶段 (Result Consolidation) - 提示词更新

Features:
- 每周6阶段完整工作流
- 阶段顺序执行和数据传递
- 统一的错误处理和恢复机制
- 事件发布和状态管理
- 执行时间统计和性能监控
- 依赖注入和服务解耦
"""

import logging
import time
from typing import Dict, List, Optional, Any
from datetime import datetime
from enum import Enum

from ..interfaces.coalition_service import ICoalitionService
from ..interfaces.simulation_service import ISimulationService
from ..interfaces.shapley_service import IShapleyService
from ..interfaces.opro_service import IOPROService
from ..interfaces.state_service import IStateManager, StateType
from ..infrastructure.event_bus import IEventBus
from ..dto.assessment_dto import AssessmentRequest, AssessmentResult
from ..dto.phase_results_dto import CoalitionResult, SimulationResult, ShapleyResult, OPROResult
from dataclasses import dataclass


class WeeklyPhase(Enum):
    """每周6阶段工作流枚举"""
    COALITION_GENERATION = "coalition_generation"        # 联盟生成与子集运算
    TRADING_SIMULATION = "trading_simulation"           # 交易模拟
    SHAPLEY_CALCULATION = "shapley_calculation"         # Shapley值计算  
    PERFORMANCE_EVALUATION = "performance_evaluation"   # 性能评估与问题识别
    OPRO_OPTIMIZATION = "opro_optimization"            # OPRO优化
    RESULT_CONSOLIDATION = "result_consolidation"      # 结果固化与提示词更新


@dataclass
class WeeklyResult:
    """每周6阶段执行结果"""
    week_number: int
    success: bool
    execution_time: float
    coalition_result: Optional[CoalitionResult] = None
    simulation_result: Optional[SimulationResult] = None
    shapley_result: Optional[ShapleyResult] = None
    evaluation_result: Optional[Dict[str, Any]] = None
    opro_result: Optional[OPROResult] = None
    consolidation_result: Optional[Dict[str, Any]] = None
    optimized_agents: Optional[List[str]] = None
    performance_metrics: Optional[Dict[str, float]] = None
    timestamp: Optional[str] = None


class PhaseCoordinatorError(Exception):
    """阶段协调器异常"""
    pass


class PhaseExecutionError(PhaseCoordinatorError):
    """阶段执行异常"""
    pass


class PhaseCoordinator:
    """
    阶段协调器
    
    负责协调四个评估阶段的执行，管理阶段间的数据传递，
    处理执行过程中的错误和异常，并发布相关事件。
    """
    
    def __init__(self,
                 coalition_service: ICoalitionService,
                 simulation_service: ISimulationService,
                 shapley_service: IShapleyService,
                 opro_service: IOPROService,
                 state_manager: IStateManager,
                 event_bus: IEventBus,
                 logger: Optional[logging.Logger] = None):
        """
        初始化阶段协调器
        
        Args:
            coalition_service: 联盟生成服务
            simulation_service: 交易模拟服务
            shapley_service: Shapley值计算服务
            opro_service: OPRO优化服务
            state_manager: 状态管理服务
            event_bus: 事件总线
            logger: 日志记录器
        """
        self.coalition_service = coalition_service
        self.simulation_service = simulation_service
        self.shapley_service = shapley_service
        self.opro_service = opro_service
        self.state_manager = state_manager
        self.event_bus = event_bus
        self.logger = logger or logging.getLogger(__name__)
        
        # 执行统计
        self._execution_stats = {
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "average_execution_time": 0.0,
            "phase_execution_times": {
                "coalition_generation": [],
                "trading_simulation": [],
                "shapley_calculation": [],
                "performance_evaluation": [],
                "opro_optimization": [],
                "result_consolidation": []
            }
        }
        
        # 阶段配置
        self._phase_config = {
            "coalition_generation": {
                "required": True,
                "retry_count": 2,
                "timeout": 300  # 5分钟
            },
            "trading_simulation": {
                "required": True,
                "retry_count": 1,
                "timeout": 600  # 10分钟
            },
            "shapley_calculation": {
                "required": True,
                "retry_count": 1,
                "timeout": 180  # 3分钟
            },
            "performance_evaluation": {
                "required": True,
                "retry_count": 1,
                "timeout": 60   # 1分钟
            },
            "opro_optimization": {
                "required": False,
                "retry_count": 1,
                "timeout": 120  # 2分钟
            },
            "result_consolidation": {
                "required": True,
                "retry_count": 1,
                "timeout": 60   # 1分钟
            }
        }
    
    def execute_assessment_workflow(self, request: AssessmentRequest) -> AssessmentResult:
        """
        执行完整的四阶段评估工作流
        
        Args:
            request: 评估请求
            
        Returns:
            AssessmentResult: 评估结果
            
        Raises:
            PhaseCoordinatorError: 协调器执行失败
        """
        execution_start_time = time.time()
        workflow_id = f"workflow_{request.request_id}"
        
        self.logger.info(f"Starting assessment workflow {workflow_id}")
        self.event_bus.publish(
            "workflow_started",
            {
                "workflow_id": workflow_id,
                "request_id": request.request_id,
                "timestamp": datetime.now().isoformat()
            },
            source="phase_coordinator"
        )
        
        # 初始化结果对象
        result = AssessmentResult(
            success=False,
            execution_time=0.0,
            request_id=request.request_id
        )
        
        try:
            # 保存工作流状态
            self._save_workflow_state(workflow_id, "started", request)
            
            # 执行四个阶段
            coalition_result = self._execute_coalition_generation_phase(request, workflow_id)
            result.add_phase_result("coalition_generation", coalition_result)
            
            simulation_result = self._execute_trading_simulation_phase(request, coalition_result, workflow_id)
            result.add_phase_result("trading_simulation", simulation_result)
            
            shapley_result = self._execute_shapley_calculation_phase(request, simulation_result, workflow_id)
            result.add_phase_result("shapley_calculation", shapley_result)
            
            opro_result = self._execute_opro_optimization_phase(request, shapley_result, simulation_result, workflow_id)
            result.add_phase_result("opro_optimization", opro_result)
            
            # 汇总结果
            self._aggregate_final_results(result, coalition_result, simulation_result, shapley_result, opro_result)
            
            # 标记成功
            result.success = True
            execution_time = time.time() - execution_start_time
            result.execution_time = execution_time
            
            # 更新统计
            self._update_execution_stats(execution_time, True)
            
            # 保存最终状态
            self._save_workflow_state(workflow_id, "completed", request, result)
            
            self.logger.info(f"Assessment workflow {workflow_id} completed successfully in {execution_time:.2f}s")
            self.event_bus.publish(
                "workflow_completed",
                {
                    "workflow_id": workflow_id,
                    "request_id": request.request_id,
                    "execution_time": execution_time,
                    "success": True,
                    "timestamp": datetime.now().isoformat()
                },
                source="phase_coordinator"
            )
            
            return result
            
        except Exception as e:
            execution_time = time.time() - execution_start_time
            result.execution_time = execution_time
            result.set_error(e, {"workflow_id": workflow_id, "phase": "coordination"})
            
            # 更新统计
            self._update_execution_stats(execution_time, False)
            
            # 保存失败状态
            self._save_workflow_state(workflow_id, "failed", request, result)
            
            self.logger.error(f"Assessment workflow {workflow_id} failed: {str(e)}")
            self.event_bus.publish(
                "workflow_failed",
                {
                    "workflow_id": workflow_id,
                    "request_id": request.request_id,
                    "execution_time": execution_time,
                    "error": str(e),
                    "error_type": type(e).__name__,
                    "timestamp": datetime.now().isoformat()
                },
                source="phase_coordinator"
            )
            
            return result
    
    def _execute_coalition_generation_phase(self, request: AssessmentRequest, workflow_id: str) -> CoalitionResult:
        """执行联盟生成阶段"""
        phase_name = "coalition_generation"
        self.logger.info(f"Starting {phase_name} phase for workflow {workflow_id}")
        
        start_time = time.time()
        self.event_bus.publish(
            "phase_started",
            {
                "workflow_id": workflow_id,
                "phase": phase_name,
                "timestamp": datetime.now().isoformat()
            },
            source="phase_coordinator"
        )
        
        try:
            # 执行联盟生成
            result = self.coalition_service.generate_coalitions(
                agents=request.agents or {},
                max_coalitions=request.max_coalitions,
                pruning_enabled=True
            )
            
            execution_time = time.time() - start_time
            self._record_phase_execution_time(phase_name, execution_time)
            
            self.logger.info(f"Coalition generation completed in {execution_time:.2f}s, generated {len(result.valid_coalitions)} coalitions")
            self.event_bus.publish(
                "phase_completed",
                {
                    "workflow_id": workflow_id,
                    "phase": phase_name,
                    "execution_time": execution_time,
                    "success": result.success,
                    "coalitions_generated": len(result.valid_coalitions),
                    "timestamp": datetime.now().isoformat()
                },
                source="phase_coordinator"
            )
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            self._record_phase_execution_time(phase_name, execution_time)
            
            self.logger.error(f"Coalition generation phase failed: {str(e)}")
            self.event_bus.publish(
                "phase_failed",
                {
                    "workflow_id": workflow_id,
                    "phase": phase_name,
                    "execution_time": execution_time,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                },
                source="phase_coordinator"
            )
            
            raise PhaseExecutionError(f"Coalition generation failed: {str(e)}") from e
    
    def _execute_trading_simulation_phase(self, request: AssessmentRequest, 
                                        coalition_result: CoalitionResult, 
                                        workflow_id: str) -> SimulationResult:
        """执行交易模拟阶段"""
        phase_name = "trading_simulation"
        self.logger.info(f"Starting {phase_name} phase for workflow {workflow_id}")
        
        start_time = time.time()
        self.event_bus.publish(
            "phase_started",
            {
                "workflow_id": workflow_id,
                "phase": phase_name,
                "coalitions_to_simulate": len(coalition_result.valid_coalitions),
                "timestamp": datetime.now().isoformat()
            },
            source="phase_coordinator"
        )
        
        try:
            # 执行交易模拟
            result = self.simulation_service.simulate_coalitions_batch(
                coalitions=coalition_result.valid_coalitions,
                agents=request.agents or {},
                config=request.config,
                max_concurrent=request.max_concurrent_api_calls
            )
            
            execution_time = time.time() - start_time
            self._record_phase_execution_time(phase_name, execution_time)
            
            self.logger.info(f"Trading simulation completed in {execution_time:.2f}s, simulated {len(result.coalition_values)} coalitions")
            self.event_bus.publish(
                "phase_completed",
                {
                    "workflow_id": workflow_id,
                    "phase": phase_name,
                    "execution_time": execution_time,
                    "success": result.success,
                    "coalitions_simulated": len(result.coalition_values),
                    "timestamp": datetime.now().isoformat()
                },
                source="phase_coordinator"
            )
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            self._record_phase_execution_time(phase_name, execution_time)
            
            self.logger.error(f"Trading simulation phase failed: {str(e)}")
            self.event_bus.publish(
                "phase_failed",
                {
                    "workflow_id": workflow_id,
                    "phase": phase_name,
                    "execution_time": execution_time,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                },
                source="phase_coordinator"
            )
            
            raise PhaseExecutionError(f"Trading simulation failed: {str(e)}") from e
    
    def _execute_shapley_calculation_phase(self, request: AssessmentRequest,
                                         simulation_result: SimulationResult,
                                         workflow_id: str) -> ShapleyResult:
        """执行Shapley值计算阶段"""
        phase_name = "shapley_calculation"
        self.logger.info(f"Starting {phase_name} phase for workflow {workflow_id}")
        
        start_time = time.time()
        self.event_bus.publish(
            "phase_started",
            {
                "workflow_id": workflow_id,
                "phase": phase_name,
                "coalitions_to_calculate": len(simulation_result.coalition_values),
                "timestamp": datetime.now().isoformat()
            },
            source="phase_coordinator"
        )
        
        try:
            # 执行Shapley值计算
            result = self.shapley_service.calculate_periodic_shapley(
                target_agents=request.target_agents or [],
                coalition_daily_returns=simulation_result.coalition_daily_returns,
                system_config=request.config
            )
            
            execution_time = time.time() - start_time
            self._record_phase_execution_time(phase_name, execution_time)
            
            self.logger.info(f"Shapley calculation completed in {execution_time:.2f}s, processed {result.total_weeks} weeks")
            self.event_bus.publish(
                "phase_completed",
                {
                    "workflow_id": workflow_id,
                    "phase": phase_name,
                    "execution_time": execution_time,
                    "success": result.success,
                    "weeks_processed": result.total_weeks,
                    "timestamp": datetime.now().isoformat()
                },
                source="phase_coordinator"
            )
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            self._record_phase_execution_time(phase_name, execution_time)
            
            self.logger.error(f"Shapley calculation phase failed: {str(e)}")
            self.event_bus.publish(
                "phase_failed",
                {
                    "workflow_id": workflow_id,
                    "phase": phase_name,
                    "execution_time": execution_time,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                },
                source="phase_coordinator"
            )
            
            raise PhaseExecutionError(f"Shapley calculation failed: {str(e)}") from e
    
    def _execute_opro_optimization_phase(self, request: AssessmentRequest,
                                       shapley_result: ShapleyResult,
                                       simulation_result: SimulationResult,
                                       workflow_id: str) -> OPROResult:
        """执行OPRO优化阶段"""
        phase_name = "opro_optimization"
        self.logger.info(f"Starting {phase_name} phase for workflow {workflow_id}")
        
        start_time = time.time()
        self.event_bus.publish(
            "phase_started",
            {
                "workflow_id": workflow_id,
                "phase": phase_name,
                "opro_enabled": request.enable_opro,
                "timestamp": datetime.now().isoformat()
            },
            source="phase_coordinator"
        )
        
        try:
            # 从Shapley结果中提取智能体得分
            shapley_values = {}
            if shapley_result.success and shapley_result.weekly_results:
                # 从最新的周结果中提取Shapley值
                latest_week = shapley_result.weekly_results[-1] if shapley_result.weekly_results else {}
                shapley_values = latest_week.get("shapley_values", {})
            
            # 执行OPRO优化，使用运行时智能体实例而非初始配置
            active_agents = simulation_result.active_agents if simulation_result and simulation_result.success else {}
            result = self.opro_service.optimize_agents(
                shapley_values=shapley_values,
                agents=active_agents or request.agents or {},
                config=request.opro_config
            )
            
            execution_time = time.time() - start_time
            self._record_phase_execution_time(phase_name, execution_time)
            
            self.logger.info(f"OPRO optimization completed in {execution_time:.2f}s, enabled: {result.enabled}")
            self.event_bus.publish(
                "phase_completed",
                {
                    "workflow_id": workflow_id,
                    "phase": phase_name,
                    "execution_time": execution_time,
                    "success": result.success,
                    "enabled": result.enabled,
                    "agents_updated": len(result.updated_agents),
                    "timestamp": datetime.now().isoformat()
                },
                source="phase_coordinator"
            )
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            self._record_phase_execution_time(phase_name, execution_time)
            
            self.logger.error(f"OPRO optimization phase failed: {str(e)}")
            self.event_bus.publish(
                "phase_failed",
                {
                    "workflow_id": workflow_id,
                    "phase": phase_name,
                    "execution_time": execution_time,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                },
                source="phase_coordinator"
            )
            
            # OPRO阶段失败不应该中断整个工作流
            return OPROResult(
                success=False,
                execution_time=execution_time,
                enabled=request.enable_opro,
                error=str(e),
                error_context={"workflow_id": workflow_id, "phase": phase_name}
            )
    
    def _aggregate_final_results(self, result: AssessmentResult, 
                               coalition_result: CoalitionResult,
                               simulation_result: SimulationResult,
                               shapley_result: ShapleyResult,
                               opro_result: OPROResult) -> None:
        """汇总最终结果"""
        # 汇总Shapley值
        if shapley_result.success and shapley_result.weekly_results:
            latest_week = shapley_result.weekly_results[-1]
            result.shapley_values = latest_week.get("shapley_values", {})
            result.shapley_analysis = latest_week.get("analysis", {})
        
        # 汇总周期性结果
        if shapley_result.success:
            result.periodic_shapley_results = {
                "total_weeks": shapley_result.total_weeks,
                "weekly_results": shapley_result.weekly_results,
                "periodic_data": shapley_result.periodic_data,
                "compilation_mode": shapley_result.compilation_mode
            }
        
        # 汇总OPRO结果
        if opro_result.success and opro_result.enabled:
            result.opro_optimization_results = {
                "enabled": opro_result.enabled,
                "optimization_result": opro_result.optimization_result,
                "updated_agents": opro_result.updated_agents,
                "optimization_stats": opro_result.optimization_stats
            }
        
        # 汇总执行统计
        result.summary = {
            "total_execution_time": result.execution_time,
            "coalition_generation": {
                "success": coalition_result.success,
                "execution_time": coalition_result.execution_time,
                "coalitions_generated": len(coalition_result.valid_coalitions)
            },
            "trading_simulation": {
                "success": simulation_result.success,
                "execution_time": simulation_result.execution_time,
                "coalitions_simulated": len(simulation_result.coalition_values)
            },
            "shapley_calculation": {
                "success": shapley_result.success,
                "execution_time": shapley_result.execution_time,
                "weeks_processed": shapley_result.total_weeks
            },
            "opro_optimization": {
                "success": opro_result.success,
                "execution_time": opro_result.execution_time,
                "enabled": opro_result.enabled,
                "agents_updated": len(opro_result.updated_agents)
            }
        }
    
    def _save_workflow_state(self, workflow_id: str, status: str, 
                           request: AssessmentRequest, result: Optional[AssessmentResult] = None) -> None:
        """保存工作流状态"""
        try:
            state_data = {
                "workflow_id": workflow_id,
                "status": status,
                "request_id": request.request_id,
                "timestamp": datetime.now().isoformat(),
                "request_data": {
                    "agents": request.agents,
                    "target_agents": request.target_agents,
                    "max_coalitions": request.max_coalitions,
                    "enable_opro": request.enable_opro
                }
            }
            
            if result:
                state_data["result_data"] = {
                    "success": result.success,
                    "execution_time": result.execution_time,
                    "error": result.error,
                    "phases_completed": len(result.phase_results)
                }
            
            self.state_manager.save_state(
                state_type=StateType.ASSESSMENT,
                state_id=workflow_id,
                state_data=state_data
            )
            
        except Exception as e:
            self.logger.warning(f"Failed to save workflow state for {workflow_id}: {str(e)}")
    
    def _record_phase_execution_time(self, phase_name: str, execution_time: float) -> None:
        """记录阶段执行时间"""
        if phase_name in self._execution_stats["phase_execution_times"]:
            self._execution_stats["phase_execution_times"][phase_name].append(execution_time)
    
    def _update_execution_stats(self, execution_time: float, success: bool) -> None:
        """更新执行统计"""
        self._execution_stats["total_executions"] += 1
        
        if success:
            self._execution_stats["successful_executions"] += 1
        else:
            self._execution_stats["failed_executions"] += 1
        
        # 更新平均执行时间
        total_time = (self._execution_stats["average_execution_time"] * 
                     (self._execution_stats["total_executions"] - 1) + execution_time)
        self._execution_stats["average_execution_time"] = total_time / self._execution_stats["total_executions"]
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """获取执行统计信息"""
        stats = self._execution_stats.copy()
        
        # 计算各阶段的平均执行时间
        phase_averages = {}
        for phase, times in stats["phase_execution_times"].items():
            phase_averages[phase] = {
                "average_time": sum(times) / len(times) if times else 0.0,
                "total_executions": len(times),
                "min_time": min(times) if times else 0.0,
                "max_time": max(times) if times else 0.0
            }
        
        stats["phase_averages"] = phase_averages
        return stats
    
    def get_phase_config(self) -> Dict[str, Any]:
        """获取阶段配置"""
        return self._phase_config.copy()
    
    def update_phase_config(self, phase_name: str, config: Dict[str, Any]) -> bool:
        """更新阶段配置"""
        if phase_name in self._phase_config:
            self._phase_config[phase_name].update(config)
            self.logger.info(f"Updated configuration for phase {phase_name}")
            return True
        return False
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return {
            "status": "healthy",
            "services": {
                "coalition_service": self.coalition_service is not None,
                "simulation_service": self.simulation_service is not None,
                "shapley_service": self.shapley_service is not None,
                "opro_service": self.opro_service is not None,
                "state_manager": self.state_manager is not None,
                "event_bus": self.event_bus is not None
            },
            "execution_stats": self._execution_stats,
            "timestamp": datetime.now().isoformat()
        }
    
    def execute_weekly_assessment_cycle(self, request: AssessmentRequest, week_num: int) -> WeeklyResult:
        """
        执行完整的周评估周期（6阶段工作流）
        
        Args:
            request: 评估请求
            week_num: 周数
            
        Returns:
            WeeklyResult: 周评估结果
            
        Raises:
            PhaseCoordinatorError: 协调器执行失败
        """
        execution_start_time = time.time()
        workflow_id = f"weekly_workflow_{request.request_id}_week_{week_num}"
        
        self.logger.info(f"🔄 开始第 {week_num} 周评估周期 (6阶段工作流)")
        
        # 记录智能体状态概览（简要版本）
        self._log_agent_status_summary(request.agents, week_num)
        
        self.event_bus.publish(
            "weekly_workflow_started",
            {
                "workflow_id": workflow_id,
                "request_id": request.request_id,
                "week_number": week_num,
                "timestamp": datetime.now().isoformat()
            },
            source="phase_coordinator"
        )
        
        # 阶段结果传递
        coalition_result = None
        simulation_result = None
        shapley_result = None
        evaluation_result = None
        opro_result = None
        consolidation_result = None
        
        try:
            # Phase 1: 联盟生成与子集运算
            self.logger.info(f"📊 Phase 1: 联盟生成与子集运算")
            coalition_result = self._execute_coalition_generation_phase(request, workflow_id)
            
            # Phase 2: 交易模拟
            self.logger.info(f"💹 Phase 2: 交易模拟")
            # 确保 current_week_number 正确传递到请求配置中（修复日期设置问题）
            request.config["current_week_number"] = week_num
            
            simulation_result = self._execute_trading_simulation_phase(request, coalition_result, workflow_id)
            
            # Phase 3: Shapley值计算
            self.logger.info(f"🔢 Phase 3: Shapley值计算")
            shapley_result = self._execute_shapley_calculation_phase(request, simulation_result, workflow_id)
            
            # Phase 4: 性能评估与问题识别  
            self.logger.info(f"📈 Phase 4: 性能评估与问题识别")
            evaluation_result = self._execute_performance_evaluation_phase(request, shapley_result, workflow_id)
            
            # Phase 5: OPRO优化
            self.logger.info(f"🧠 Phase 5: OPRO优化")
            opro_result = self._execute_opro_optimization_phase(request, shapley_result, simulation_result, workflow_id)
            
            # Phase 6: 结果固化与提示词更新
            self.logger.info(f"💾 Phase 6: 结果固化与提示词更新")
            consolidation_result = self._execute_result_consolidation_phase(request, opro_result, week_num, workflow_id)
            
            # 构建周结果
            execution_time = time.time() - execution_start_time
            weekly_result = WeeklyResult(
                week_number=week_num,
                success=True,
                execution_time=execution_time,
                coalition_result=coalition_result,
                simulation_result=simulation_result,
                shapley_result=shapley_result,
                evaluation_result=evaluation_result,
                opro_result=opro_result,
                consolidation_result=consolidation_result,
                optimized_agents=opro_result.updated_agents if opro_result else [],
                performance_metrics=shapley_result.weekly_results[-1].get("shapley_values", {}) if shapley_result and shapley_result.weekly_results else {},
                timestamp=datetime.now().isoformat()
            )
            
            # 更新统计
            self._update_execution_stats(execution_time, True)
            
            self.logger.info(f"✅ 第 {week_num} 周评估周期完成，耗时 {execution_time:.2f}s")
            self.event_bus.publish(
                "weekly_workflow_completed",
                {
                    "workflow_id": workflow_id,
                    "week_number": week_num,
                    "execution_time": execution_time,
                    "success": True,
                    "timestamp": datetime.now().isoformat()
                },
                source="phase_coordinator"
            )
            
            return weekly_result
            
        except Exception as e:
            execution_time = time.time() - execution_start_time
            self._update_execution_stats(execution_time, False)
            
            self.logger.error(f"❌ 第 {week_num} 周评估周期失败: {str(e)}")
            self.event_bus.publish(
                "weekly_workflow_failed",
                {
                    "workflow_id": workflow_id,
                    "week_number": week_num,
                    "execution_time": execution_time,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                },
                source="phase_coordinator"
            )
            
            # 返回失败结果
            return WeeklyResult(
                week_number=week_num,
                success=False,
                execution_time=execution_time,
                coalition_result=coalition_result,
                simulation_result=simulation_result,
                shapley_result=shapley_result,
                evaluation_result=evaluation_result,
                opro_result=opro_result,
                consolidation_result=consolidation_result,
                timestamp=datetime.now().isoformat()
            )
    
    def _execute_performance_evaluation_phase(self, request: AssessmentRequest, 
                                            shapley_result: ShapleyResult, 
                                            workflow_id: str) -> Dict[str, Any]:
        """执行Phase 4: 性能评估与问题识别"""
        phase_name = "performance_evaluation"
        self.logger.info(f"Starting {phase_name} phase for workflow {workflow_id}")
        
        start_time = time.time()
        self.event_bus.publish(
            "phase_started",
            {
                "workflow_id": workflow_id,
                "phase": phase_name,
                "timestamp": datetime.now().isoformat()
            },
            source="phase_coordinator"
        )
        
        try:
            # 分析Shapley值，识别表现最差的智能体
            shapley_values = {}
            if shapley_result.success and shapley_result.weekly_results:
                latest_week = shapley_result.weekly_results[-1] if shapley_result.weekly_results else {}
                shapley_values = latest_week.get("shapley_values", {})
            
            # 识别需要优化的智能体（Shapley值最低的）
            underperforming_agents = []
            performance_threshold = 0.1  # 可配置的性能阈值
            
            if shapley_values:
                sorted_agents = sorted(shapley_values.items(), key=lambda x: x[1])
                for agent_id, shapley_value in sorted_agents:
                    if shapley_value < performance_threshold:
                        underperforming_agents.append(agent_id)
                
                # 至少选择表现最差的一个智能体进行优化
                if not underperforming_agents and sorted_agents:
                    underperforming_agents.append(sorted_agents[0][0])
            
            evaluation_result = {
                "shapley_values": shapley_values,
                "underperforming_agents": underperforming_agents,
                "performance_threshold": performance_threshold,
                "analysis": {
                    "total_agents": len(shapley_values),
                    "agents_below_threshold": len(underperforming_agents),
                    "avg_performance": sum(shapley_values.values()) / len(shapley_values) if shapley_values else 0
                }
            }
            
            execution_time = time.time() - start_time
            self._record_phase_execution_time(phase_name, execution_time)
            
            self.logger.info(f"Performance evaluation completed in {execution_time:.2f}s, identified {len(underperforming_agents)} underperforming agents")
            self.event_bus.publish(
                "phase_completed",
                {
                    "workflow_id": workflow_id,
                    "phase": phase_name,
                    "execution_time": execution_time,
                    "success": True,
                    "underperforming_agents": len(underperforming_agents),
                    "timestamp": datetime.now().isoformat()
                },
                source="phase_coordinator"
            )
            
            return evaluation_result
            
        except Exception as e:
            execution_time = time.time() - start_time
            self._record_phase_execution_time(phase_name, execution_time)
            
            self.logger.error(f"Performance evaluation phase failed: {str(e)}")
            self.event_bus.publish(
                "phase_failed",
                {
                    "workflow_id": workflow_id,
                    "phase": phase_name,
                    "execution_time": execution_time,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                },
                source="phase_coordinator"
            )
            
            raise PhaseExecutionError(f"Performance evaluation failed: {str(e)}") from e
    
    def _execute_result_consolidation_phase(self, request: AssessmentRequest, 
                                          opro_result: OPROResult, 
                                          week_num: int, 
                                          workflow_id: str) -> Dict[str, Any]:
        """执行Phase 6: 结果固化与提示词更新"""
        phase_name = "result_consolidation"
        self.logger.info(f"Starting {phase_name} phase for workflow {workflow_id}")
        
        start_time = time.time()
        self.event_bus.publish(
            "phase_started",
            {
                "workflow_id": workflow_id,
                "phase": phase_name,
                "week_number": week_num,
                "timestamp": datetime.now().isoformat()
            },
            source="phase_coordinator"
        )
        
        try:
            updated_agents = []
            consolidation_data = {
                "week_number": week_num,
                "opro_enabled": request.enable_opro,
                "timestamp": datetime.now().isoformat()
            }
            
            # 如果OPRO优化成功，更新智能体提示词
            if opro_result and opro_result.success and opro_result.enabled:
                updated_agents = opro_result.updated_agents
                consolidation_data.update({
                    "optimization_applied": True,
                    "updated_agents": updated_agents,
                    "optimization_summary": getattr(opro_result, 'optimization_result', {})
                })
                
                self.logger.info(f"Applied OPRO optimization to {len(updated_agents)} agents")
            else:
                consolidation_data.update({
                    "optimization_applied": False,
                    "reason": "OPRO disabled or failed"
                })
            
            # 保存周期结果到状态管理器
            try:
                week_state_id = f"week_{week_num}_{request.request_id}"
                self.state_manager.save_state(
                    state_type=StateType.ASSESSMENT,
                    state_id=week_state_id,
                    state_data=consolidation_data
                )
                consolidation_data["state_saved"] = True
            except Exception as e:
                self.logger.warning(f"Failed to save week state: {e}")
                consolidation_data["state_saved"] = False
            
            execution_time = time.time() - start_time
            self._record_phase_execution_time(phase_name, execution_time)
            
            self.logger.info(f"Result consolidation completed in {execution_time:.2f}s, week {week_num} results consolidated")
            self.event_bus.publish(
                "phase_completed",
                {
                    "workflow_id": workflow_id,
                    "phase": phase_name,
                    "execution_time": execution_time,
                    "success": True,
                    "week_number": week_num,
                    "agents_updated": len(updated_agents),
                    "timestamp": datetime.now().isoformat()
                },
                source="phase_coordinator"
            )
            
            return consolidation_data
            
        except Exception as e:
            execution_time = time.time() - start_time
            self._record_phase_execution_time(phase_name, execution_time)
            
            self.logger.error(f"Result consolidation phase failed: {str(e)}")
            self.event_bus.publish(
                "phase_failed",
                {
                    "workflow_id": workflow_id,
                    "phase": phase_name,
                    "execution_time": execution_time,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                },
                source="phase_coordinator"
            )
            
            # 结果固化阶段失败不应该中断整个工作流
            return {
                "success": False,
                "error": str(e),
                "week_number": week_num,
                "timestamp": datetime.now().isoformat()
            }
    
    def _log_agent_status_summary(self, agents: Dict[str, Any], week_num: int) -> None:
        """
        记录智能体状态简要概览
        
        Args:
            agents: 智能体字典
            week_num: 周数
        """
        try:
            if not agents:
                self.logger.info(f"   🤖 第{week_num}周智能体状态: 无智能体实例")
                return
            
            total_agents = len(agents)
            opro_enabled_count = 0
            optimized_count = 0
            
            for agent_id, agent_instance in agents.items():
                # 检查OPRO支持
                if hasattr(agent_instance, 'opro_enabled') and agent_instance.opro_enabled:
                    opro_enabled_count += 1
                    
                # 检查是否已优化
                if hasattr(agent_instance, '_opro_stats'):
                    opro_stats = agent_instance._opro_stats
                    if opro_stats.get("successful_optimizations", 0) > 0:
                        optimized_count += 1
            
            self.logger.info(f"   🤖 第{week_num}周智能体状态: {total_agents}个智能体, OPRO启用{opro_enabled_count}个, 已优化{optimized_count}个")
            
            # 如果有优化过的智能体，列出它们
            if optimized_count > 0:
                optimized_agents = []
                for agent_id, agent_instance in agents.items():
                    if hasattr(agent_instance, '_opro_stats'):
                        opro_stats = agent_instance._opro_stats
                        if opro_stats.get("successful_optimizations", 0) > 0:
                            optimized_agents.append(f"{agent_id}(v{getattr(agent_instance, 'prompt_version', 'unknown')})")
                
                self.logger.info(f"   ✅ 已优化智能体: {', '.join(optimized_agents)}")
            
        except Exception as e:
            self.logger.error(f"记录智能体状态概览失败: {e}")