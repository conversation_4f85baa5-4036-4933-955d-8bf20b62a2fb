
## OPRO 优化重复触发问题

### 问题描述
OPRO（提示词优化）在系统中被触发了两次，并且在第二次触发时，优化了多个智能体，而不是预期的每次只优化一个。

### 原因分析

1.  **第一次 OPRO 触发：**
    -   `ContributionAssessor` 类中的 `run` 方法直接调用了 `_run_opro_optimization_phase`。
    -   这个阶段会识别出当前表现最差的智能体并对其进行优化。

2.  **第二次 OPRO 触发：**
    -   `ContributionAssessor` 类中的 `_check_and_trigger_weekly_optimization` 方法负责检查是否满足每周优化的条件。
    -   如果满足条件（例如，有新的 Shapley 值），它会调用 `WeeklyOptimizationManager` 的 `execute_weekly_optimization` 方法。
    -   `WeeklyOptimizationManager.execute_weekly_optimization` 方法会再次选择要优化的智能体（通过 `_select_optimization_targets` 方法），然后调用 `opro_optimizer.optimize_agent_prompt` 来执行实际的 OPRO 优化。
    -   尽管 `WeeklyOptimizationConfig` 中的 `max_agents_per_cycle` 默认设置为 1，但在实际运行中，日志显示优化了多个智能体（例如 BOA 和 BeOA）。这可能意味着 `_select_optimization_targets` 在某些情况下选择了多个智能体，或者 `max_agents_per_cycle` 在运行时被覆盖或修改。

### 解决方案（待实施）

1.  **禁用 `ContributionAssessor` 中的直接 OPRO 触发：** 修改 `ContributionAssessor` 的 `run` 方法，使其不再直接调用 `_run_opro_optimization_phase`。
2.  **确保 `WeeklyOptimizationManager` 每次只优化一个智能体：** 检查 `WeeklyOptimizationManager` 的 `_select_optimization_targets` 方法，确保它严格遵守 `max_agents_per_cycle` 的限制，并且只选择一个智能体进行优化。如果配置被覆盖，需要找到覆盖的地方并进行修正。
3.  **验证 `opro_optimizer.optimize_agent_prompt` 的行为：** 确保它确实只优化一个智能体。
