# 需求文档

## 介绍

本功能旨在解决重构后服务架构的"最后一公里"连接问题。虽然重构后的服务架构已经完成，但存在默认导入配置错误、ServiceFactory硬编码Mock服务、基础设施层服务注册缺失等问题，导致真实服务无法被激活使用。本spec将系统性地解决这些问题，确保重构版本成为默认版本，真实服务被正确使用。

## 需求

### 需求 1

**用户故事：** 作为系统开发者，我希望重构版本成为默认导入版本，这样系统能够自动使用新的架构而不是旧版本

#### 验收标准

1. 当导入contribution_assessment模块时，系统应该默认导入RefactoredContributionAssessor
2. 当设置USE_REFACTORED环境变量为false时，系统应该能够回退到原始版本
3. 当未设置环境变量时，系统应该默认使用重构版本
4. 当导入模块时，系统应该保留对原始版本的别名访问以确保向后兼容性

### 需求 2

**用户故事：** 作为系统架构师，我希望ServiceFactory能够创建真实服务实例而不是Mock服务，这样系统能够在生产环境中正常工作

#### 验收标准

1. 当调用create_phase_coordinator方法时，系统应该创建真实的CoalitionService实例
2. 当调用create_phase_coordinator方法时，系统应该创建真实的SimulationService实例
3. 当调用create_phase_coordinator方法时，系统应该创建真实的ShapleyService实例
4. 当调用create_phase_coordinator方法时，系统应该创建真实的OPROService实例
5. 当use_real_services参数为false时，系统应该能够创建Mock服务用于测试
6. 当创建服务时，系统应该正确配置服务间的依赖关系

### 需求 3

**用户故事：** 作为基础设施开发者，我希望基础设施层能够正确注册和管理真实服务，这样依赖注入容器能够正常工作

#### 验收标准

1. 当调用configure_services方法时，系统应该注册所有真实服务到服务注册表
2. 当请求服务实例时，系统应该能够从服务注册表中获取正确的服务实例
3. 当服务注册失败时，系统应该提供清晰的错误信息
4. 当服务配置更新时，系统应该能够重新注册服务
5. 当查询服务状态时，系统应该能够提供服务健康检查信息

### 需求 4

**用户故事：** 作为系统运维人员，我希望系统提供配置驱动的服务切换机制，这样可以在不同环境中灵活选择服务实现

#### 验收标准

1. 当设置配置文件时，系统应该能够根据配置选择使用真实服务或Mock服务
2. 当环境变量发生变化时，系统应该能够动态切换服务实现
3. 当服务切换失败时，系统应该能够回退到安全的默认配置
4. 当查询当前配置时，系统应该能够报告当前使用的服务类型
5. 当配置验证失败时，系统应该提供详细的验证错误信息

### 需求 5

**用户故事：** 作为质量保证工程师，我希望系统提供完整的错误处理和回退机制，这样在服务激活过程中出现问题时系统能够保持稳定

#### 验收标准

1. 当真实服务初始化失败时，系统应该能够自动回退到Mock服务
2. 当服务依赖缺失时，系统应该提供清晰的错误信息和建议
3. 当服务配置错误时，系统应该能够使用默认配置继续运行
4. 当系统检测到服务异常时，系统应该记录详细的错误日志
5. 当服务回退发生时，系统应该通知相关组件配置变更

### 需求 6

**用户故事：** 作为系统监控人员，我希望系统提供服务状态监控和性能指标，这样可以及时发现和解决服务问题

#### 验收标准

1. 当服务启动时，系统应该记录服务启动状态和耗时
2. 当服务运行时，系统应该定期报告服务健康状态
3. 当服务性能异常时，系统应该触发性能警告
4. 当查询服务指标时，系统应该提供详细的性能数据
5. 当服务状态变化时，系统应该更新状态报告