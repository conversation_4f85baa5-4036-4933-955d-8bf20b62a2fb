"""
联盟服务接口定义

定义联盟生成和管理的服务接口
"""

from abc import ABC, abstractmethod
from typing import Set, Dict, List, Optional, Any, FrozenSet
from ..dto.phase_results_dto import CoalitionResult


class ICoalitionService(ABC):
    """
    联盟服务接口
    
    负责生成和管理智能体联盟，包括：
    - 生成有效联盟
    - 联盟剪枝和优化
    - 联盟分析和统计
    """
    
    @abstractmethod
    def generate_coalitions(
        self, 
        agents: Dict[str, Any],
        max_coalitions: Optional[int] = None,
        pruning_enabled: bool = True
    ) -> CoalitionResult:
        """
        生成智能体联盟
        
        Args:
            agents: 智能体配置字典
            max_coalitions: 最大联盟数量限制
            pruning_enabled: 是否启用联盟剪枝
            
        Returns:
            CoalitionResult: 联盟生成结果
            
        Raises:
            CoalitionGenerationError: 联盟生成失败时抛出
        """
        pass
    
    @abstractmethod
    def validate_coalition(self, coalition: FrozenSet[str]) -> bool:
        """
        验证联盟的有效性
        
        Args:
            coalition: 要验证的联盟
            
        Returns:
            bool: 联盟是否有效
        """
        pass
    
    @abstractmethod
    def get_coalition_analysis(self, coalitions: Set[FrozenSet[str]]) -> Dict[str, Any]:
        """
        分析联盟集合的统计信息
        
        Args:
            coalitions: 联盟集合
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        pass
    
    @abstractmethod
    def prune_coalitions(
        self, 
        coalitions: Set[FrozenSet[str]], 
        criteria: Optional[Dict[str, Any]] = None
    ) -> Set[FrozenSet[str]]:
        """
        根据指定条件剪枝联盟
        
        Args:
            coalitions: 原始联盟集合
            criteria: 剪枝条件
            
        Returns:
            Set[FrozenSet[str]]: 剪枝后的联盟集合
        """
        pass
    
    @abstractmethod
    def get_coalition_dependencies(self, coalition: FrozenSet[str]) -> Dict[str, List[str]]:
        """
        获取联盟内智能体的依赖关系
        
        Args:
            coalition: 联盟
            
        Returns:
            Dict[str, List[str]]: 依赖关系映射
        """
        pass
    
    @abstractmethod
    def estimate_coalition_complexity(self, coalition: FrozenSet[str]) -> float:
        """
        估算联盟的复杂度
        
        Args:
            coalition: 联盟
            
        Returns:
            float: 复杂度评分
        """
        pass


class CoalitionGenerationError(Exception):
    """联盟生成异常"""
    pass


class CoalitionValidationError(Exception):
    """联盟验证异常"""
    pass