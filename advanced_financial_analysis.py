#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级金融分析脚本 - 包含详细的统计分析和风险指标
"""

import re
import sys
import json
from pathlib import Path
from typing import List, Dict, Tuple
import numpy as np
import pandas as pd
from datetime import datetime


def extract_net_value_data(file_path: str) -> pd.DataFrame:
    """
    从日志文件中提取净值数据并转换为DataFrame
    
    Args:
        file_path: 日志文件路径
        
    Returns:
        包含净值数据的DataFrame
    """
    data = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                if '💰 第' in line and '天净值变化' in line:
                    # 解析行内容
                    pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}).*?💰 第(\d+)天净值变化: 收益率=([^,]+), 净值=\$(.+)'
                    match = re.search(pattern, line.strip())
                    
                    if match:
                        timestamp = match.group(1)
                        day_number = int(match.group(2))
                        return_rate = float(match.group(3))
                        net_value = float(match.group(4))
                        
                        data.append({
                            'timestamp': pd.to_datetime(timestamp),
                            'day_number': day_number,
                            'return_rate': return_rate,
                            'net_value': net_value,
                            'line_number': line_num
                        })
    
    except FileNotFoundError:
        print(f"错误: 文件 '{file_path}' 不存在")
        return pd.DataFrame()
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return pd.DataFrame()
    
    return pd.DataFrame(data)


def calculate_advanced_metrics(df: pd.DataFrame) -> Dict:
    """
    计算高级金融指标
    
    Args:
        df: 包含收益率和净值数据的DataFrame
        
    Returns:
        包含各项金融指标的字典
    """
    if df.empty:
        return {}
    
    returns = df['return_rate'].values
    net_values = df['net_value'].values
    
    # 基础统计
    total_days = len(returns)
    mean_return = np.mean(returns)
    std_return = np.std(returns, ddof=1)
    
    # 年化指标 (假设252个交易日)
    annualized_return = (1 + mean_return) ** 252 - 1
    annualized_volatility = std_return * np.sqrt(252)
    
    # 风险调整收益指标
    risk_free_rate = 0.03
    sharpe_ratio = (annualized_return - risk_free_rate) / annualized_volatility if annualized_volatility != 0 else 0
    
    # 下行风险指标
    negative_returns = returns[returns < 0]
    downside_deviation = np.std(negative_returns) * np.sqrt(252) if len(negative_returns) > 0 else 0
    sortino_ratio = (annualized_return - risk_free_rate) / downside_deviation if downside_deviation != 0 else 0
    
    # 最大回撤相关指标
    peak = np.maximum.accumulate(net_values)
    drawdown = (peak - net_values) / peak
    max_drawdown = np.max(drawdown)
    
    # 计算回撤持续时间
    drawdown_periods = []
    in_drawdown = False
    drawdown_start = 0
    
    for i, dd in enumerate(drawdown):
        if dd > 0 and not in_drawdown:
            in_drawdown = True
            drawdown_start = i
        elif dd == 0 and in_drawdown:
            in_drawdown = False
            drawdown_periods.append(i - drawdown_start)
    
    avg_drawdown_duration = np.mean(drawdown_periods) if drawdown_periods else 0
    max_drawdown_duration = np.max(drawdown_periods) if drawdown_periods else 0
    
    # VaR和CVaR (95%置信度)
    var_95 = np.percentile(returns, 5)
    cvar_95 = np.mean(returns[returns <= var_95]) if np.any(returns <= var_95) else 0
    
    # 胜率和盈亏比
    positive_returns = returns[returns > 0]
    win_rate = len(positive_returns) / total_days
    
    avg_win = np.mean(positive_returns) if len(positive_returns) > 0 else 0
    avg_loss = np.mean(negative_returns) if len(negative_returns) > 0 else 0
    profit_loss_ratio = avg_win / abs(avg_loss) if avg_loss != 0 else 0
    
    # 收益分布统计
    skewness = calculate_skewness(returns)
    kurtosis = calculate_kurtosis(returns)
    
    # Calmar比率 (年化收益率/最大回撤)
    calmar_ratio = annualized_return / max_drawdown if max_drawdown != 0 else 0
    
    # 累计收益
    total_return = (net_values[-1] / net_values[0] - 1) if len(net_values) > 0 else 0
    
    return {
        # 基础指标
        'total_days': total_days,
        'total_return': total_return,
        'annualized_return': annualized_return,
        'mean_daily_return': mean_return,
        'daily_volatility': std_return,
        'annualized_volatility': annualized_volatility,
        
        # 风险调整收益
        'sharpe_ratio': sharpe_ratio,
        'sortino_ratio': sortino_ratio,
        'calmar_ratio': calmar_ratio,
        
        # 回撤指标
        'max_drawdown': max_drawdown,
        'avg_drawdown_duration': avg_drawdown_duration,
        'max_drawdown_duration': max_drawdown_duration,
        
        # 风险指标
        'var_95': var_95,
        'cvar_95': cvar_95,
        'downside_deviation': downside_deviation,
        
        # 交易统计
        'win_rate': win_rate,
        'profit_loss_ratio': profit_loss_ratio,
        'avg_win': avg_win,
        'avg_loss': avg_loss,
        
        # 分布特征
        'skewness': skewness,
        'kurtosis': kurtosis,
        
        # 极值
        'max_daily_return': np.max(returns),
        'min_daily_return': np.min(returns),
        'max_net_value': np.max(net_values),
        'min_net_value': np.min(net_values)
    }


def calculate_skewness(data: np.ndarray) -> float:
    """计算偏度"""
    if len(data) < 3:
        return 0
    mean = np.mean(data)
    std = np.std(data, ddof=1)
    if std == 0:
        return 0
    return np.mean(((data - mean) / std) ** 3)


def calculate_kurtosis(data: np.ndarray) -> float:
    """计算峰度"""
    if len(data) < 4:
        return 0
    mean = np.mean(data)
    std = np.std(data, ddof=1)
    if std == 0:
        return 0
    return np.mean(((data - mean) / std) ** 4) - 3  # 超额峰度


def print_advanced_analysis(metrics: Dict):
    """打印详细的金融分析报告"""
    if not metrics:
        print("没有可分析的数据")
        return
    
    print("=" * 80)
    print("                    高级金融分析报告")
    print("=" * 80)
    
    # 基础信息
    print(f"\n📊 基础统计信息")
    print(f"{'交易天数:':<20} {metrics['total_days']} 天")
    print(f"{'累计收益率:':<20} {metrics['total_return']:.4f} ({metrics['total_return']*100:.2f}%)")
    print(f"{'年化收益率:':<20} {metrics['annualized_return']:.4f} ({metrics['annualized_return']*100:.2f}%)")
    print(f"{'日均收益率:':<20} {metrics['mean_daily_return']:.4f} ({metrics['mean_daily_return']*100:.2f}%)")
    
    # 风险指标
    print(f"\n⚠️  风险指标")
    print(f"{'日收益波动率:':<20} {metrics['daily_volatility']:.4f} ({metrics['daily_volatility']*100:.2f}%)")
    print(f"{'年化波动率:':<20} {metrics['annualized_volatility']:.4f} ({metrics['annualized_volatility']*100:.2f}%)")
    print(f"{'下行偏差:':<20} {metrics['downside_deviation']:.4f} ({metrics['downside_deviation']*100:.2f}%)")
    print(f"{'VaR (95%):':<20} {metrics['var_95']:.4f} ({metrics['var_95']*100:.2f}%)")
    print(f"{'CVaR (95%):':<20} {metrics['cvar_95']:.4f} ({metrics['cvar_95']*100:.2f}%)")
    
    # 风险调整收益
    print(f"\n📈 风险调整收益指标")
    print(f"{'夏普率:':<20} {metrics['sharpe_ratio']:.4f}")
    print(f"{'索提诺率:':<20} {metrics['sortino_ratio']:.4f}")
    print(f"{'卡玛率:':<20} {metrics['calmar_ratio']:.4f}")
    
    # 回撤分析
    print(f"\n📉 回撤分析")
    print(f"{'最大回撤:':<20} {metrics['max_drawdown']:.4f} ({metrics['max_drawdown']*100:.2f}%)")
    print(f"{'平均回撤持续期:':<20} {metrics['avg_drawdown_duration']:.1f} 天")
    print(f"{'最长回撤持续期:':<20} {metrics['max_drawdown_duration']:.0f} 天")
    
    # 交易统计
    print(f"\n🎯 交易统计")
    print(f"{'胜率:':<20} {metrics['win_rate']:.4f} ({metrics['win_rate']*100:.2f}%)")
    print(f"{'盈亏比:':<20} {metrics['profit_loss_ratio']:.4f}")
    print(f"{'平均盈利:':<20} {metrics['avg_win']:.4f} ({metrics['avg_win']*100:.2f}%)")
    print(f"{'平均亏损:':<20} {metrics['avg_loss']:.4f} ({metrics['avg_loss']*100:.2f}%)")
    
    # 分布特征
    print(f"\n📊 收益分布特征")
    print(f"{'偏度:':<20} {metrics['skewness']:.4f}")
    print(f"{'峰度:':<20} {metrics['kurtosis']:.4f}")
    print(f"{'最大单日收益:':<20} {metrics['max_daily_return']:.4f} ({metrics['max_daily_return']*100:.2f}%)")
    print(f"{'最大单日亏损:':<20} {metrics['min_daily_return']:.4f} ({metrics['min_daily_return']*100:.2f}%)")
    
    # 净值统计
    print(f"\n💰 净值统计")
    print(f"{'最高净值:':<20} ${metrics['max_net_value']:,.2f}")
    print(f"{'最低净值:':<20} ${metrics['min_net_value']:,.2f}")
    
    print("=" * 80)


def main():
    """主函数"""
    # 默认文件路径
    default_file = "test_results/NVDA_phase1.md"
    
    # 获取输入文件路径
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    else:
        input_file = default_file
    
    print(f"正在进行高级金融分析: {input_file}")
    
    # 提取数据
    df = extract_net_value_data(input_file)
    
    if df.empty:
        print("未找到任何净值变化数据")
        return
    
    # 计算高级指标
    metrics = calculate_advanced_metrics(df)
    
    # 打印分析报告
    print_advanced_analysis(metrics)
    
    # 保存详细分析结果
    input_path = Path(input_file)
    base_name = input_path.stem
    output_dir = input_path.parent
    
    advanced_metrics_output = output_dir / f"{base_name}_advanced_metrics.json"
    
    # 添加分析时间戳并转换numpy类型为Python原生类型
    metrics['analysis_timestamp'] = datetime.now().isoformat()
    metrics['source_file'] = str(input_file)

    # 转换numpy类型为Python原生类型以支持JSON序列化
    def convert_numpy_types(obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {key: convert_numpy_types(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy_types(item) for item in obj]
        return obj

    metrics = convert_numpy_types(metrics)

    with open(advanced_metrics_output, 'w', encoding='utf-8') as f:
        json.dump(metrics, f, ensure_ascii=False, indent=2)
    
    print(f"\n详细分析结果已保存到: {advanced_metrics_output}")


if __name__ == "__main__":
    main()
