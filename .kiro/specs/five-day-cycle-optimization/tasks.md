# 实现任务列表

## 任务概述

基于现有的contribution_assessment模块，实现可配置周期的四阶段优化系统（默认5个交易日，支持动态调整）。系统将自动执行：阶段1（完整集合交易）→ 阶段2（子集计算并计算Shapley值）→ 阶段3（识别最差agent）→ 阶段4（OPRO优化提示词）的循环。

## 实现任务

### 任务 1: 创建周期管理器核心组件

**目标：** 实现CycleManager类，负责管理可配置交易日周期的识别、启动和结束（默认5天，支持动态调整）

#### 子任务
- [ ] 1.1 创建CycleManager类基础结构
  - 实现基于交易日历的可配置周期计算逻辑（默认5天，支持动态调整）
  - 集成现有的TradingCalendar组件
  - 实现周期状态跟踪和持久化
  - _需求: 1.1, 1.2_

- [ ] 1.2 实现周期数据模型
  - 创建CycleInfo数据类，包含周期编号、日期范围、交易日列表
  - 实现CycleStatus枚举，管理周期状态
  - 添加周期间数据传递和清理机制
  - _需求: 1.1, 4.1_

- [ ] 1.3 实现周期转换逻辑
  - 编写start_new_cycle()方法，启动新的交易周期（支持可配置天数）
  - 编写is_cycle_complete()方法，检查当前周期是否完成
  - 编写handle_cycle_transition()方法，处理周期转换
  - _需求: 1.1, 1.6_

### 任务 2: 创建阶段调度器

**目标：** 实现PhaseScheduler类，负责四个阶段的顺序执行和状态管理

#### 子任务
- [ ] 2.1 创建PhaseScheduler基础架构
  - 实现四个阶段的执行方法框架
  - 集成现有的CoalitionService、ShapleyService、OPROService
  - 实现阶段间数据传递机制
  - _需求: 1.1, 1.2, 3.1_

- [ ] 2.2 实现阶段一：完整集合交易
  - 编写execute_phase_one()方法
  - 调用现有的TradingSimulator进行完整联盟交易
  - 实现详细日志模式，记录完整交易过程
  - _需求: 1.1, 3.2_

- [ ] 2.3 实现阶段二：子集计算并计算Shapley值
  - 编写execute_phase_two()方法
  - 调用CoalitionService生成子集联盟
  - 调用ShapleyService计算周期性Shapley值
  - 实现简洁日志模式，提高执行效率
  - _需求: 1.2, 3.2_

- [ ] 2.4 实现阶段三：识别最差agent
  - 编写execute_phase_three()方法
  - 基于Shapley值结果识别表现最差的智能体
  - 实现智能体性能排名和分析逻辑
  - _需求: 1.2, 3.2_

- [ ] 2.5 实现阶段四：OPRO优化提示词
  - 编写execute_phase_four()方法
  - 调用OPROService对最差智能体进行提示词优化
  - 实现优化结果的应用和验证
  - _需求: 1.2, 3.4_

### 任务 3: 创建状态管理器

**目标：** 实现StateManager类，负责系统状态的持久化和恢复

#### 子任务
- [ ] 3.1 创建状态管理基础设施
  - 实现SystemState数据模型
  - 创建状态持久化存储机制
  - 实现状态序列化和反序列化
  - _需求: 2.1, 5.1_

- [ ] 3.2 实现状态保存和加载
  - 编写save_state()方法，保存当前系统状态
  - 编写load_state()方法，从持久化存储加载状态
  - 实现状态一致性检查和验证
  - _需求: 2.1, 5.2_

- [ ] 3.3 实现异常恢复支持
  - 实现系统崩溃后的状态恢复机制
  - 编写状态回滚和修复逻辑
  - 实现状态历史管理和清理
  - _需求: 5.1, 5.2_

### 任务 4: 创建主优化器类

**目标：** 实现PeriodicCycleOptimizer主入口类，协调所有组件工作

#### 子任务
- [ ] 4.1 创建主优化器架构
  - 实现PeriodicCycleOptimizer类基础结构
  - 集成CycleManager、PhaseScheduler、StateManager
  - 实现可配置的周期长度（默认5天，可调整）
  - 实现配置管理和日志系统
  - _需求: 1.1, 4.1_

- [ ] 4.2 实现优化循环主逻辑
  - 编写start_optimization_loop()方法，启动自动化优化循环
  - 实现循环控制逻辑，管理周期性执行
  - 支持动态调整周期长度配置
  - 集成错误处理和重试机制
  - _需求: 1.1, 1.6, 5.1_

- [ ] 4.3 实现系统监控和控制
  - 编写get_system_status()方法，提供实时状态查询
  - 编写stop_optimization_loop()方法，安全停止系统
  - 实现系统健康检查和性能监控
  - _需求: 2.1, 2.2_

- [ ] 4.4 实现数据导出功能
  - 编写export_cycle_data()方法，导出周期性数据
  - 实现结构化数据格式和报告生成
  - 集成现有的数据序列化组件
  - _需求: 6.1, 6.3_

### 任务 5: 集成现有服务组件

**目标：** 将新系统与现有的contribution_assessment服务无缝集成

#### 子任务
- [ ] 5.1 集成CoalitionService
  - 修改PhaseScheduler以使用CoalitionService生成联盟
  - 实现联盟生成配置的传递和管理
  - 确保联盟生成结果的正确处理
  - _需求: 3.1, 3.2_

- [ ] 5.2 集成ShapleyService
  - 修改PhaseScheduler以使用ShapleyService计算Shapley值
  - 实现周期性Shapley值计算的配置管理
  - 确保Shapley计算结果的正确存储和传递
  - _需求: 3.2, 3.3_

- [ ] 5.3 集成OPROService
  - 修改PhaseScheduler以使用OPROService进行提示词优化
  - 实现OPRO优化配置的管理和传递
  - 确保优化结果的正确应用和验证
  - _需求: 3.4, 3.5_

- [ ] 5.4 集成基础设施组件
  - 集成EventBus进行组件间通信
  - 集成ErrorHandler进行统一错误处理
  - 集成ConfigurationManager进行配置管理
  - _需求: 3.1, 5.1_

### 任务 6: 实现错误处理和恢复机制

**目标：** 建立完善的错误处理和系统恢复机制

#### 子任务
- [ ] 6.1 实现阶段级错误处理
  - 为每个阶段实现独立的错误捕获和处理
  - 实现阶段失败时的重试机制
  - 实现阶段跳过逻辑，确保系统继续运行
  - _需求: 5.1, 5.2_

- [ ] 6.2 实现网络和API错误处理
  - 实现LLM API调用失败的重试机制
  - 实现网络中断时的等待和恢复逻辑
  - 实现API限流和降级策略
  - _需求: 5.3, 5.4_

- [ ] 6.3 实现系统级错误恢复
  - 实现系统崩溃后的自动恢复机制
  - 实现状态一致性检查和修复
  - 实现优雅停机和资源清理
  - _需求: 5.1, 5.5_

### 任务 7: 实现配置和监控系统

**目标：** 建立完善的配置管理和系统监控机制

#### 子任务
- [ ] 7.1 实现配置管理系统
  - 创建周期性优化的专用配置文件格式，支持可配置周期长度
  - 实现配置验证和默认值处理（默认周期长度为5天）
  - 实现运行时配置更新机制
  - _需求: 4.1, 4.2, 4.4_

- [ ] 7.2 实现系统监控和日志
  - 实现周期性执行状态的实时监控
  - 实现详细的执行日志和性能指标收集
  - 实现异常检测和告警机制
  - _需求: 2.1, 2.2, 2.3_

- [ ] 7.3 实现数据导出和报告
  - 实现周期性数据的自动导出
  - 实现性能趋势分析和报告生成
  - 实现数据可视化和仪表板功能
  - _需求: 6.1, 6.2, 6.3_

### 任务 8: 创建主运行脚本

**目标：** 创建新的主运行脚本，替代现有的run_opro_system.py

#### 子任务
- [ ] 8.1 创建新的主运行脚本
  - 创建run_periodic_cycle.py脚本
  - 实现命令行参数解析和配置加载
  - 支持--cycle-days参数来配置周期长度
  - 实现PeriodicCycleOptimizer的初始化和启动
  - _需求: 1.1, 4.1_

- [ ] 8.2 实现运行模式支持
  - 实现单次运行模式（运行一个完整周期）
  - 实现连续运行模式（持续运行多个周期）
  - 实现测试模式（快速验证系统功能）
  - _需求: 1.1, 2.1_

- [ ] 8.3 实现系统控制功能
  - 实现优雅启动和停止机制
  - 实现运行时状态查询和控制
  - 实现系统健康检查和诊断
  - _需求: 2.1, 2.2, 5.1_

### 任务 9: 编写测试和文档

**目标：** 确保系统质量和可维护性

#### 子任务
- [ ] 9.1 编写单元测试
  - 为CycleManager编写单元测试
  - 为PhaseScheduler编写单元测试
  - 为StateManager编写单元测试
  - _需求: 所有需求的验证_

- [ ] 9.2 编写集成测试
  - 编写四阶段完整流程的集成测试
  - 编写错误恢复机制的测试
  - 编写配置管理的测试
  - _需求: 1.1, 1.2, 5.1_

- [ ] 9.3 编写用户文档
  - 编写系统安装和配置指南
  - 编写使用说明和最佳实践
  - 编写故障排除和维护指南
  - _需求: 4.1, 4.4, 6.1_

### 任务 10: 系统部署和验证

**目标：** 确保系统能够在生产环境中稳定运行

#### 子任务
- [ ] 10.1 进行系统集成验证
  - 验证与现有contribution_assessment模块的兼容性
  - 验证所有四个阶段的正确执行
  - 验证周期性循环的稳定性
  - _需求: 3.1, 3.2, 3.3_

- [ ] 10.2 进行性能和稳定性测试
  - 进行长期运行测试，验证系统稳定性
  - 进行负载测试，验证系统性能
  - 进行故障恢复测试，验证错误处理机制
  - _需求: 5.1, 5.2, 6.1_

- [ ] 10.3 完成最终部署
  - 完成生产环境的配置和部署
  - 完成系统监控和告警的配置
  - 完成用户培训和知识转移
  - _需求: 2.1, 4.1, 6.1_

## 实现优先级

### 高优先级（核心功能）
- 任务 1: 周期管理器核心组件
- 任务 2: 阶段调度器
- 任务 4: 主优化器类
- 任务 8: 主运行脚本

### 中优先级（系统完善）
- 任务 3: 状态管理器
- 任务 5: 服务组件集成
- 任务 6: 错误处理机制

### 低优先级（质量保证）
- 任务 7: 配置和监控系统
- 任务 9: 测试和文档
- 任务 10: 系统部署和验证

## 技术约束

1. **必须基于现有架构**：所有新组件必须基于contribution_assessment模块的现有架构
2. **不能修改assessor.py**：该文件已被标记为遗弃，只能作为参考
3. **服务导向设计**：新组件必须遵循现有的服务导向架构模式
4. **向后兼容**：新系统必须与现有组件保持兼容
5. **错误处理**：必须实现完善的错误处理和恢复机制

## 预期成果

完成所有任务后，系统将能够：
1. 自动识别5个交易日的周期
2. 按顺序执行四个优化阶段
3. 持续循环运行，不断优化智能体性能
4. 提供完善的监控和错误恢复机制
5. 与现有系统无缝集成