#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取包含"💰 第X天净值变化"的行内容的脚本
"""

import re
import sys
from pathlib import Path
from typing import List, Dict
import csv
import json


def extract_net_value_changes(file_path: str) -> List[Dict[str, str]]:
    """
    从日志文件中提取包含"💰 第X天净值变化"的行内容

    Args:
        file_path: 日志文件路径

    Returns:
        包含提取信息的字典列表
    """
    results = []

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                if '💰 第' in line and '天净值变化' in line:
                    # 解析日志行
                    parsed_data = parse_net_value_line(line.strip(), line_num)
                    if parsed_data:
                        results.append(parsed_data)

    except FileNotFoundError:
        print(f"错误: 文件 '{file_path}' 不存在")
        return []
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return []

    return results


def parse_net_value_line(line: str, line_num: int) -> Dict[str, str]:
    """
    解析包含净值变化的日志行

    Args:
        line: 日志行内容
        line_num: 行号

    Returns:
        解析后的数据字典
    """
    # 正则表达式匹配日志格式
    # 示例: 2025-07-28 21:19:03,715 - __main__ - INFO - 💰 第1天净值变化: 收益率=0.0284, 净值=$1028371.92
    pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}).*?💰 第(\d+)天净值变化: 收益率=([^,]+), 净值=\$(.+)'

    match = re.search(pattern, line)
    if match:
        timestamp = match.group(1)
        day_number = match.group(2)
        return_rate = match.group(3)
        net_value = match.group(4)

        return {
            'line_number': str(line_num),
            'timestamp': timestamp,
            'day_number': day_number,
            'return_rate': return_rate,
            'net_value': net_value,
            'full_line': line
        }

    return None


def save_to_csv(data: List[Dict[str, str]], output_file: str):
    """保存数据到CSV文件"""
    if not data:
        print("没有数据可保存")
        return

    fieldnames = ['line_number', 'timestamp', 'day_number', 'return_rate', 'net_value', 'full_line']

    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(data)

    print(f"数据已保存到 CSV 文件: {output_file}")


def save_to_json(data: List[Dict[str, str]], output_file: str):
    """保存数据到JSON文件"""
    if not data:
        print("没有数据可保存")
        return

    with open(output_file, 'w', encoding='utf-8') as jsonfile:
        json.dump(data, jsonfile, ensure_ascii=False, indent=2)

    print(f"数据已保存到 JSON 文件: {output_file}")


def print_summary(data: List[Dict[str, str]]):
    """打印提取结果摘要"""
    if not data:
        print("未找到包含'💰 第X天净值变化'的行")
        return

    print(f"\n=== 提取结果摘要 ===")
    print(f"总共找到 {len(data)} 条净值变化记录")
    print(f"天数范围: 第{data[0]['day_number']}天 到 第{data[-1]['day_number']}天")

    # 统计收益情况
    positive_returns = sum(1 for item in data if float(item['return_rate']) > 0)
    negative_returns = sum(1 for item in data if float(item['return_rate']) < 0)
    zero_returns = sum(1 for item in data if float(item['return_rate']) == 0)

    print(f"收益分布:")
    print(f"  正收益: {positive_returns} 次")
    print(f"  负收益: {negative_returns} 次")
    print(f"  零收益: {zero_returns} 次")

    # 计算净值统计
    net_values = [float(item['net_value']) for item in data]
    print(f"净值范围: ${min(net_values):,.2f} 到 ${max(net_values):,.2f}")

    print(f"\n前5条记录:")
    for i, item in enumerate(data[:5]):
        print(f"  {i+1}. 第{item['day_number']}天 - 收益率: {item['return_rate']}, 净值: ${item['net_value']}")


def main():
    """主函数"""
    # 默认文件路径
    default_file = "test_results/NVDA_phase1.md"

    # 获取输入文件路径
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    else:
        input_file = default_file

    print(f"正在处理文件: {input_file}")

    # 提取数据
    extracted_data = extract_net_value_changes(input_file)

    if not extracted_data:
        print("未找到任何净值变化数据")
        return

    # 打印摘要
    print_summary(extracted_data)

    # 生成输出文件名
    input_path = Path(input_file)
    base_name = input_path.stem
    output_dir = input_path.parent

    csv_output = output_dir / f"{base_name}_net_value_changes.csv"
    json_output = output_dir / f"{base_name}_net_value_changes.json"

    # 保存到文件
    save_to_csv(extracted_data, str(csv_output))
    save_to_json(extracted_data, str(json_output))

    print(f"\n处理完成! 共提取 {len(extracted_data)} 条记录")


if __name__ == "__main__":
    main()
