# 设计文档：修复 TradingSimulator 联盟模拟方法

## 概述

当前OPRO系统在运行阶段二（子集联盟评估）时出现关键错误：`TradingSimulator` 对象缺少 `simulate_coalition` 方法。这导致系统无法完成多智能体联盟的交易模拟，严重影响了Shapley值计算和OPRO优化流程的正常运行。

本设计文档详细说明如何实现 `simulate_coalition` 方法，使其与现有的 `run_simulation_for_coalition` 方法协同工作，确保系统能够成功运行子集联盟模拟。

## 架构

### 问题分析

通过代码分析，我们发现：

1. `TradingSimulator` 类已经实现了 `run_simulation_for_coalition` 方法，该方法可以为指定联盟运行交易模拟。
2. 系统在阶段二（子集联盟评估）时尝试调用 `simulate_coalition` 方法，但该方法不存在。
3. 从错误日志可以看出，系统在处理子集联盟时需要使用 `simulate_coalition` 方法。

### 解决方案

我们将采用以下方案解决问题：

1. 在 `TradingSimulator` 类中添加 `simulate_coalition` 方法，作为 `run_simulation_for_coalition` 方法的别名或包装器。
2. 确保 `simulate_coalition` 方法的参数和返回值与系统期望的接口一致。
3. 添加适当的日志记录和错误处理，以便于问题诊断。

## 组件和接口

### TradingSimulator 类

`TradingSimulator` 类是交易模拟引擎的核心组件，负责为特定智能体联盟运行交易模拟，计算联盟的性能指标。

#### 新增方法：simulate_coalition

```python
def simulate_coalition(self,
                      coalition: Union[Set[str], List[str]],
                      agents: Dict[str, Any],
                      simulation_days: Optional[int] = None,
                      current_week_number: Optional[int] = None) -> Dict[str, Any]:
    """
    为指定联盟运行交易模拟并计算夏普比率（实时分析模式）
    
    这是 run_simulation_for_coalition 方法的别名，用于保持与系统其他部分的接口兼容性。
    
    参数:
        coalition: 智能体联盟（智能体ID集合或列表）
        agents: 智能体实例字典（必需，不能为None）
        simulation_days: 模拟天数，如果为None则使用配置中的默认值
        current_week_number: 当前周数，用于正确显示周级日志（可选）
        
    返回:
        包含以下字段的字典：
        - sharpe_ratio: 联盟的夏普比率（特征函数值v(S)）
        - daily_returns: 每日收益率列表
        - weekly_data: 周级数据列表
        - total_days: 总模拟天数
        - simulation_time: 模拟耗时（秒）
        - error: 错误信息（如果有）
    """
    self.logger.info(f"调用 simulate_coalition 方法（作为 run_simulation_for_coalition 的别名）")
    return self.run_simulation_for_coalition(
        coalition=coalition,
        agents=agents,
        simulation_days=simulation_days,
        current_week_number=current_week_number
    )
```

## 数据模型

本修复不涉及数据模型的变更，`simulate_coalition` 方法将使用与 `run_simulation_for_coalition` 方法相同的数据结构：

### 输入参数

- `coalition`: 智能体联盟（智能体ID集合或列表）
- `agents`: 智能体实例字典
- `simulation_days`: 模拟天数（可选）
- `current_week_number`: 当前周数（可选）

### 返回值

包含以下字段的字典：
- `sharpe_ratio`: 联盟的夏普比率（特征函数值v(S)）
- `daily_returns`: 每日收益率列表
- `weekly_data`: 周级数据列表
- `total_days`: 总模拟天数
- `simulation_time`: 模拟耗时（秒）
- `error`: 错误信息（如果有）

## 错误处理

`simulate_coalition` 方法将继承 `run_simulation_for_coalition` 方法的错误处理机制，包括：

1. 验证联盟有效性
2. 处理模拟过程中的异常
3. 记录详细的错误信息和堆栈跟踪
4. 返回包含错误信息的结果字典

## 测试策略

### 单元测试

1. 测试 `simulate_coalition` 方法是否正确调用 `run_simulation_for_coalition` 方法
2. 测试不同参数组合下的方法行为
3. 测试错误处理机制

### 集成测试

1. 测试 `simulate_coalition` 方法与 `ContributionAssessor` 类的集成
2. 测试完整的子集联盟评估流程

### 边界测试

1. 测试空联盟场景
2. 测试单智能体联盟场景
3. 测试满联盟场景

## 实现计划

1. 在 `TradingSimulator` 类中添加 `simulate_coalition` 方法
2. 添加适当的日志记录
3. 编写单元测试
4. 进行集成测试
5. 验证修复效果

## 兼容性考虑

本修复是一个向后兼容的更改，不会影响现有功能。`simulate_coalition` 方法作为 `run_simulation_for_coalition` 方法的别名，确保了与现有代码的兼容性。

## 性能影响

由于 `simulate_coalition` 方法只是 `run_simulation_for_coalition` 方法的别名，不会引入额外的性能开销。