"""
日志配置工具模块

提供统一的日志配置功能，包括第三方库日志级别控制
"""

import logging
from typing import List, Optional


def setup_clean_logging(
    verbose: bool = False, 
    log_file: Optional[str] = None,
    logger_name: Optional[str] = None
) -> logging.Logger:
    """
    设置干净的日志记录，过滤第三方库的调试信息
    
    参数:
        verbose: 是否启用详细日志（DEBUG级别）
        log_file: 日志文件路径（可选）
        logger_name: 日志记录器名称（可选）
    
    返回:
        配置好的日志记录器
    """
    log_level = logging.DEBUG if verbose else logging.INFO
    
    # 设置处理器
    handlers: List[logging.Handler] = [logging.StreamHandler()]
    if log_file:
        handlers.append(logging.FileHandler(log_file, encoding='utf-8'))
    
    # 基础日志配置
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=handlers,
        force=True  # 强制重新配置
    )
    
    # 设置第三方库的日志级别，避免过多的调试信息
    _suppress_third_party_debug_logs()
    
    # 返回指定的日志记录器
    if logger_name:
        return logging.getLogger(logger_name)
    else:
        return logging.getLogger(__name__)


def _suppress_third_party_debug_logs():
    """抑制第三方库的调试日志"""
    third_party_loggers = [
        # HTTP 相关
        'httpcore',
        'httpcore.connection',
        'httpcore.http11',
        'httpcore.proxy',
        'httpx',
        'urllib3',
        'urllib3.connectionpool',
        'requests',
        'requests.packages.urllib3',
        
        # ZhipuAI 相关
        'zhipuai',
        'zhipuai.api_resource',
        'zhipuai.api_resource.chat',
        'zhipuai.api_resource.chat.completions',
        'zhipuai.core',
        'zhipuai.core._http_client',
        
        # OpenAI 相关
        'openai',
        'openai._base_client',
        'openai._client',
        
        # 其他常见的第三方库
        'asyncio',
        'concurrent.futures',
        'multiprocessing',
        'threading',
        
        # 数据库相关
        'sqlalchemy',
        'sqlite3',
        
        # 网络相关
        'socket',
        'ssl',
    ]
    
    for logger_name in third_party_loggers:
        third_party_logger = logging.getLogger(logger_name)
        # 将第三方库的日志级别设置为WARNING，只显示警告和错误
        third_party_logger.setLevel(logging.WARNING)


def set_logger_level(logger_name: str, level: int):
    """
    设置指定日志记录器的级别
    
    参数:
        logger_name: 日志记录器名称
        level: 日志级别（如 logging.DEBUG, logging.INFO 等）
    """
    logger = logging.getLogger(logger_name)
    logger.setLevel(level)


def get_clean_logger(name: str, level: int = logging.INFO) -> logging.Logger:
    """
    获取一个干净的日志记录器
    
    参数:
        name: 日志记录器名称
        level: 日志级别
    
    返回:
        配置好的日志记录器
    """
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # 如果没有处理器，添加一个默认的
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    return logger


def enable_debug_for_module(module_name: str):
    """
    为特定模块启用调试日志
    
    参数:
        module_name: 模块名称
    """
    logger = logging.getLogger(module_name)
    logger.setLevel(logging.DEBUG)


def disable_debug_for_module(module_name: str):
    """
    为特定模块禁用调试日志
    
    参数:
        module_name: 模块名称
    """
    logger = logging.getLogger(module_name)
    logger.setLevel(logging.INFO)
