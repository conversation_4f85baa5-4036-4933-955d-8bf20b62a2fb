#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取包含"💰 第X天净值变化"的行内容的脚本
"""

import re
import sys
from pathlib import Path
from typing import List, Dict, Tuple
import csv
import json
import numpy as np
import pandas as pd
from datetime import datetime


def extract_net_value_changes(file_path: str) -> List[Dict[str, str]]:
    """
    从日志文件中提取包含"💰 第X天净值变化"的行内容

    Args:
        file_path: 日志文件路径

    Returns:
        包含提取信息的字典列表
    """
    results = []

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                if '💰 第' in line and '天净值变化' in line:
                    # 解析日志行
                    parsed_data = parse_net_value_line(line.strip(), line_num)
                    if parsed_data:
                        results.append(parsed_data)

    except FileNotFoundError:
        print(f"错误: 文件 '{file_path}' 不存在")
        return []
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return []

    return results


def parse_net_value_line(line: str, line_num: int) -> Dict[str, str]:
    """
    解析包含净值变化的日志行

    Args:
        line: 日志行内容
        line_num: 行号

    Returns:
        解析后的数据字典
    """
    # 正则表达式匹配日志格式
    # 示例: 2025-07-28 21:19:03,715 - __main__ - INFO - 💰 第1天净值变化: 收益率=0.0284, 净值=$1028371.92
    pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}).*?💰 第(\d+)天净值变化: 收益率=([^,]+), 净值=\$(.+)'

    match = re.search(pattern, line)
    if match:
        timestamp = match.group(1)
        day_number = match.group(2)
        return_rate = match.group(3)
        net_value = match.group(4)

        return {
            'line_number': str(line_num),
            'timestamp': timestamp,
            'day_number': day_number,
            'return_rate': return_rate,
            'net_value': net_value,
            'full_line': line
        }

    return None


def calculate_financial_metrics(data: List[Dict[str, str]]) -> Dict[str, float]:
    """
    计算金融指标：夏普率、波动率、最大回撤、年化收益率

    Args:
        data: 包含收益率数据的字典列表

    Returns:
        包含各项金融指标的字典
    """
    if not data:
        return {}

    # 提取收益率数据
    returns = []
    net_values = []

    for item in data:
        try:
            returns.append(float(item['return_rate']))
            net_values.append(float(item['net_value']))
        except (ValueError, KeyError):
            continue

    if not returns:
        return {}

    returns = np.array(returns)
    net_values = np.array(net_values)

    # 计算基本统计指标
    mean_return = np.mean(returns)
    std_return = np.std(returns, ddof=1)  # 样本标准差

    # 年化收益率 (假设252个交易日)
    annualized_return = (1 + mean_return) ** 252 - 1

    # 年化波动率
    annualized_volatility = std_return * np.sqrt(252)

    # 夏普率 (假设无风险利率为3%)
    risk_free_rate = 0.03
    if annualized_volatility != 0:
        sharpe_ratio = (annualized_return - risk_free_rate) / annualized_volatility
    else:
        sharpe_ratio = 0

    # 最大回撤
    max_drawdown = calculate_max_drawdown(net_values)

    # 累计收益率
    total_return = (net_values[-1] / net_values[0] - 1) if len(net_values) > 0 else 0

    # 胜率 (正收益天数比例)
    win_rate = np.sum(returns > 0) / len(returns) if len(returns) > 0 else 0

    # 盈亏比 (平均盈利/平均亏损)
    positive_returns = returns[returns > 0]
    negative_returns = returns[returns < 0]

    if len(positive_returns) > 0 and len(negative_returns) > 0:
        profit_loss_ratio = np.mean(positive_returns) / abs(np.mean(negative_returns))
    else:
        profit_loss_ratio = 0

    return {
        'total_days': len(returns),
        'mean_daily_return': mean_return,
        'daily_volatility': std_return,
        'annualized_return': annualized_return,
        'annualized_volatility': annualized_volatility,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown': max_drawdown,
        'total_return': total_return,
        'win_rate': win_rate,
        'profit_loss_ratio': profit_loss_ratio,
        'max_daily_return': np.max(returns),
        'min_daily_return': np.min(returns),
        'max_net_value': np.max(net_values),
        'min_net_value': np.min(net_values)
    }


def calculate_max_drawdown(net_values: np.ndarray) -> float:
    """
    计算最大回撤

    Args:
        net_values: 净值序列

    Returns:
        最大回撤比例
    """
    if len(net_values) == 0:
        return 0

    # 计算累计最高净值
    peak = np.maximum.accumulate(net_values)

    # 计算回撤
    drawdown = (peak - net_values) / peak

    # 返回最大回撤
    return np.max(drawdown)


def save_to_csv(data: List[Dict[str, str]], output_file: str):
    """保存数据到CSV文件"""
    if not data:
        print("没有数据可保存")
        return

    fieldnames = ['line_number', 'timestamp', 'day_number', 'return_rate', 'net_value', 'full_line']

    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(data)

    print(f"数据已保存到 CSV 文件: {output_file}")


def save_to_json(data: List[Dict[str, str]], output_file: str):
    """保存数据到JSON文件"""
    if not data:
        print("没有数据可保存")
        return

    with open(output_file, 'w', encoding='utf-8') as jsonfile:
        json.dump(data, jsonfile, ensure_ascii=False, indent=2)

    print(f"数据已保存到 JSON 文件: {output_file}")


def save_metrics_to_json(metrics: Dict[str, float], output_file: str):
    """保存金融指标到JSON文件"""
    if not metrics:
        print("没有金融指标数据可保存")
        return

    # 格式化指标数据，添加百分比显示
    formatted_metrics = {
        'summary': {
            'total_days': int(metrics['total_days']),
            'total_return_pct': f"{metrics['total_return']*100:.2f}%",
            'annualized_return_pct': f"{metrics['annualized_return']*100:.2f}%",
            'annualized_volatility_pct': f"{metrics['annualized_volatility']*100:.2f}%",
            'sharpe_ratio': f"{metrics['sharpe_ratio']:.4f}",
            'max_drawdown_pct': f"{metrics['max_drawdown']*100:.2f}%",
            'win_rate_pct': f"{metrics['win_rate']*100:.2f}%"
        },
        'detailed_metrics': metrics,
        'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }

    with open(output_file, 'w', encoding='utf-8') as jsonfile:
        json.dump(formatted_metrics, jsonfile, ensure_ascii=False, indent=2)

    print(f"金融指标已保存到 JSON 文件: {output_file}")


def print_summary(data: List[Dict[str, str]]):
    """打印提取结果摘要和金融指标"""
    if not data:
        print("未找到包含'💰 第X天净值变化'的行")
        return

    print(f"\n=== 提取结果摘要 ===")
    print(f"总共找到 {len(data)} 条净值变化记录")
    print(f"天数范围: 第{data[0]['day_number']}天 到 第{data[-1]['day_number']}天")

    # 统计收益情况
    positive_returns = sum(1 for item in data if float(item['return_rate']) > 0)
    negative_returns = sum(1 for item in data if float(item['return_rate']) < 0)
    zero_returns = sum(1 for item in data if float(item['return_rate']) == 0)

    print(f"收益分布:")
    print(f"  正收益: {positive_returns} 次")
    print(f"  负收益: {negative_returns} 次")
    print(f"  零收益: {zero_returns} 次")

    # 计算净值统计
    net_values = [float(item['net_value']) for item in data]
    print(f"净值范围: ${min(net_values):,.2f} 到 ${max(net_values):,.2f}")

    # 计算金融指标
    metrics = calculate_financial_metrics(data)
    if metrics:
        print(f"\n=== 金融指标分析 ===")
        print(f"交易天数: {metrics['total_days']} 天")
        print(f"累计收益率: {metrics['total_return']:.4f} ({metrics['total_return']*100:.2f}%)")
        print(f"年化收益率: {metrics['annualized_return']:.4f} ({metrics['annualized_return']*100:.2f}%)")
        print(f"日均收益率: {metrics['mean_daily_return']:.4f} ({metrics['mean_daily_return']*100:.2f}%)")
        print(f"日收益波动率: {metrics['daily_volatility']:.4f} ({metrics['daily_volatility']*100:.2f}%)")
        print(f"年化波动率: {metrics['annualized_volatility']:.4f} ({metrics['annualized_volatility']*100:.2f}%)")
        print(f"夏普率: {metrics['sharpe_ratio']:.4f}")
        print(f"最大回撤: {metrics['max_drawdown']:.4f} ({metrics['max_drawdown']*100:.2f}%)")
        print(f"胜率: {metrics['win_rate']:.4f} ({metrics['win_rate']*100:.2f}%)")
        print(f"盈亏比: {metrics['profit_loss_ratio']:.4f}")
        print(f"单日最大收益: {metrics['max_daily_return']:.4f} ({metrics['max_daily_return']*100:.2f}%)")
        print(f"单日最大亏损: {metrics['min_daily_return']:.4f} ({metrics['min_daily_return']*100:.2f}%)")

    print(f"\n前5条记录:")
    for i, item in enumerate(data[:5]):
        print(f"  {i+1}. 第{item['day_number']}天 - 收益率: {item['return_rate']}, 净值: ${item['net_value']}")


def main():
    """主函数"""
    # 默认文件路径
    default_file = "test_results/NVDA_phase1.md"

    # 获取输入文件路径
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    else:
        input_file = default_file

    print(f"正在处理文件: {input_file}")

    # 提取数据
    extracted_data = extract_net_value_changes(input_file)

    if not extracted_data:
        print("未找到任何净值变化数据")
        return

    # 打印摘要
    print_summary(extracted_data)

    # 生成输出文件名
    input_path = Path(input_file)
    base_name = input_path.stem
    output_dir = input_path.parent

    csv_output = output_dir / f"{base_name}_net_value_changes.csv"
    json_output = output_dir / f"{base_name}_net_value_changes.json"

    # 保存到文件
    save_to_csv(extracted_data, str(csv_output))
    save_to_json(extracted_data, str(json_output))

    # 保存金融指标分析
    metrics = calculate_financial_metrics(extracted_data)
    if metrics:
        metrics_output = output_dir / f"{base_name}_financial_metrics.json"
        save_metrics_to_json(metrics, str(metrics_output))

    print(f"\n处理完成! 共提取 {len(extracted_data)} 条记录")


if __name__ == "__main__":
    main()
