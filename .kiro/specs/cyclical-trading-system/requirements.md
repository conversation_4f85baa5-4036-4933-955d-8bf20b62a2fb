# Requirements Document

## Introduction

The current OPRO trading system runs as discrete modes (evaluation, optimization, etc.) but lacks the intended cyclical workflow where trading occurs in 5-day periods followed by automatic Shapley value calculation and agent optimization. This feature will implement a continuous cyclical trading system that automatically transitions between trading periods and optimization phases.

## Requirements

### Requirement 1

**User Story:** As a trading system operator, I want the system to automatically run continuous 5-day trading cycles followed by optimization phases, so that the system can continuously improve agent performance without manual intervention.

#### Acceptance Criteria

1. WHEN the system starts in cyclical mode THEN it SHALL begin a 5-day trading period
2. WHEN a 5-day trading period completes THEN the system SHALL automatically transition to Shapley value calculation phase
3. WHEN Shapley value calculation completes THEN the system SHALL identify the worst-performing agent
4. WHEN the worst-performing agent is identified THEN the system SHALL run OPRO optimization for that agent
5. WHEN optimization completes THEN the system SHALL start a new 5-day trading cycle
6. WHEN the system encounters an error in any phase THEN it SHALL log the error and continue to the next cycle

### Requirement 2

**User Story:** As a system administrator, I want to configure the cycle parameters (trading days per cycle, maximum cycles, etc.), so that I can control the system's behavior based on different testing scenarios.

#### Acceptance Criteria

1. WHEN configuring the system THEN the user SHALL be able to specify trading days per cycle (default: 5)
2. WHEN configuring the system THEN the user SHALL be able to specify maximum number of cycles (default: unlimited)
3. WHEN configuring the system THEN the user SHALL be able to specify which agents to include in optimization
4. WHEN configuring the system THEN the user SHALL be able to enable/disable specific phases (trading, shapley, optimization)
5. IF maximum cycles is reached THEN the system SHALL stop and generate a final report

### Requirement 3

**User Story:** As a system monitor, I want to track the progress and status of each cycle phase, so that I can understand system performance and identify issues.

#### Acceptance Criteria

1. WHEN each phase starts THEN the system SHALL log the phase name, start time, and relevant parameters
2. WHEN each phase completes THEN the system SHALL log completion status, duration, and key metrics
3. WHEN a cycle completes THEN the system SHALL generate a cycle summary report
4. WHEN the system runs multiple cycles THEN it SHALL maintain a running log of all cycle statistics
5. WHEN an error occurs THEN the system SHALL log detailed error information and recovery actions

### Requirement 4

**User Story:** As a researcher, I want to access intermediate results from each phase, so that I can analyze the system's learning and optimization patterns over time.

#### Acceptance Criteria

1. WHEN a trading phase completes THEN the system SHALL save trading results with cycle identifier
2. WHEN Shapley calculation completes THEN the system SHALL save Shapley values with timestamp and cycle info
3. WHEN optimization completes THEN the system SHALL save optimization results and agent improvements
4. WHEN accessing historical data THEN the user SHALL be able to query results by cycle number, date range, or agent
5. WHEN exporting data THEN the system SHALL provide consolidated reports across multiple cycles

### Requirement 5

**User Story:** As a system operator, I want to control the running system (pause, resume, stop), so that I can manage system resources and respond to operational needs.

#### Acceptance Criteria

1. WHEN the system is running THEN the operator SHALL be able to send a pause signal
2. WHEN the system is paused THEN it SHALL complete the current phase before pausing
3. WHEN the system is paused THEN the operator SHALL be able to resume from the next phase
4. WHEN the system receives a stop signal THEN it SHALL complete the current phase and generate a final report
5. WHEN the system is interrupted unexpectedly THEN it SHALL be able to resume from the last completed phase

### Requirement 6

**User Story:** As a system integrator, I want the cyclical mode to work with existing OPRO components, so that I can leverage current functionality without breaking existing workflows.

#### Acceptance Criteria

1. WHEN running in cyclical mode THEN the system SHALL use existing ContributionAssessor for Shapley calculations
2. WHEN running optimization THEN the system SHALL use existing OPRO optimization methods
3. WHEN running trading simulation THEN the system SHALL use existing trading simulation components
4. WHEN switching between phases THEN the system SHALL properly manage state and data persistence
5. WHEN running in cyclical mode THEN existing single-mode operations SHALL remain functional