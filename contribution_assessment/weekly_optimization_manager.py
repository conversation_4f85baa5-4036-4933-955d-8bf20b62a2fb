#!/usr/bin/env python3
"""
周期性优化管理器 (Weekly Optimization Manager)

本模块实现了周期性（每周）在线优化系统的核心管理功能。
与传统的离线批量优化不同，该系统在模拟过程中持续进行优化，
使智能体能够在模拟期间不断改进和进化。

主要功能：
1. 管理完整的周期性优化流程
2. 协调评估、优化、应用三个阶段
3. 处理周期边界和数据持久化
4. 支持动态智能体配置更新
5. 提供优化进度监控和异常处理

核心优势：
- 在线优化：模拟过程中实时改进
- 持续学习：智能体持续进化
- 灵活配置：支持不同优化策略
- 高效处理：增量数据处理
"""

import os
import json
import logging
import time
import copy
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, TYPE_CHECKING
from dataclasses import dataclass, asdict
from enum import Enum
import numpy as np

# 导入相关模块
from .services.opro_service import OPROService

# 使用TYPE_CHECKING避免循环导入
if TYPE_CHECKING:
    from .refactored_assessor import RefactoredContributionAssessor
from .historical_score_manager import HistoricalScoreManager
from .llm_interface import LLMInterface


class OptimizationPhase(Enum):
    """优化阶段枚举"""
    INITIALIZATION = "initialization"
    SIMULATION = "simulation"
    EVALUATION = "evaluation"
    OPTIMIZATION = "optimization"
    APPLICATION = "application"
    COMPLETION = "completion"


@dataclass
class WeeklyOptimizationConfig:
    """周期性优化配置"""
    # 基础配置
    optimization_frequency: int = 5  # 优化频率（交易天数）- 统一为5个交易日
    min_days_for_optimization: int = 5  # 最少运行天数才开始优化
    max_optimization_iterations: int = 3  # 每次优化的最大迭代次数

    # 优化策略配置
    performance_degradation_threshold: float = -0.05  # 性能下降阈值
    optimization_timeout: int = 300  # 优化超时时间（秒）

    # 智能体选择策略
    optimize_worst_performers: bool = True  # 是否优化表现最差的智能体
    max_agents_per_cycle: int = 1  # 每个周期最多优化的智能体数量
    min_performance_for_optimization: float = -0.1  # 最低性能要求

    # 数据管理
    historical_window_weeks: int = 4  # 历史数据窗口（周）
    backup_enabled: bool = True  # 是否启用备份
    cleanup_old_data: bool = True  # 是否清理旧数据
    
    # 监控和日志
    verbose_logging: bool = True  # 详细日志
    progress_reporting: bool = True  # 进度报告
    performance_tracking: bool = True  # 性能跟踪
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)


@dataclass
class WeeklyOptimizationResult:
    """周期性优化结果"""
    week_number: int
    start_date: str
    end_date: str
    phase: OptimizationPhase
    
    # 评估结果
    shapley_values: Dict[str, float]
    agent_performance: Dict[str, Any]
    
    # 优化结果
    agents_optimized: List[str]
    optimization_results: Dict[str, Any]
    estimated_improvements: Dict[str, float]
    
    # 执行信息
    execution_time: float
    success: bool
    error_message: Optional[str] = None
    
    # 统计信息
    total_trades: int = 0
    total_return: float = 0.0
    sharpe_ratio: float = 0.0
    
    created_at: str = ""
    
    def __post_init__(self):
        if not self.created_at:
            self.created_at = datetime.now().isoformat()


class WeeklyOptimizationManager:
    """
    周期性优化管理器
    
    负责管理整个周期性优化流程，包括：
    1. 周期边界检测和管理
    2. 智能体性能评估
    3. 优化决策和执行
    4. 配置动态更新
    5. 结果跟踪和报告
    """
    
    def __init__(self, 
                 config: WeeklyOptimizationConfig,
                 llm_interface: LLMInterface,
                 logger: Optional[logging.Logger] = None,
                 base_results_path: str = "results"):
        """
        初始化周期性优化管理器
        
        参数:
            config: 优化配置
            llm_interface: LLM接口
            logger: 日志记录器
            base_results_path: 结果存储基础路径
        """
        self.config = config
        self.llm_interface = llm_interface
        self.logger = logger or self._create_default_logger()
        self.base_results_path = base_results_path
        
        # 初始化核心组件
        self.historical_manager = HistoricalScoreManager(
            results_base_path=os.path.join(base_results_path, "periodic_shapley"),
            db_path=os.path.join(base_results_path, "weekly_optimization.db"),
            logger=self.logger
        )
        
        self.opro_service = OPROService(
            historical_score_manager=self.historical_manager,
            enabled=True,
            logger=self.logger
        )
        
        # 状态管理
        self.current_phase = OptimizationPhase.INITIALIZATION
        self.current_week = 0
        self.simulation_start_date = None
        self.simulation_end_date = None
        self.last_optimization_date = None
        
        # 结果跟踪
        self.weekly_results: List[WeeklyOptimizationResult] = []
        self.performance_history: Dict[str, List[float]] = {}
        self.optimization_log: List[Dict[str, Any]] = []
        
        # 智能体管理
        self.active_agents: Dict[str, Any] = {}
        self.agent_configurations: Dict[str, Dict[str, Any]] = {}
        self.optimized_prompts: Dict[str, str] = {}
        
        # 监控统计
        self.stats = {
            "total_weeks_processed": 0,
            "total_optimizations": 0,
            "successful_optimizations": 0,
            "failed_optimizations": 0,
            "total_improvements": 0.0,
            "average_improvement": 0.0,
            "best_performing_agent": None,
            "worst_performing_agent": None
        }
        
        # 创建结果目录
        self.results_dir = os.path.join(base_results_path, "weekly_optimization")
        os.makedirs(self.results_dir, exist_ok=True)
        
        self.logger.info("周期性优化管理器初始化完成")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.WeeklyOptimizationManager")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def initialize_simulation(self, 
                            start_date: str, 
                            end_date: str, 
                            agents: Dict[str, Any],
                            system_config: Dict[str, Any]) -> bool:
        """
        初始化模拟设置
        
        参数:
            start_date: 模拟开始日期
            end_date: 模拟结束日期
            agents: 智能体字典
            system_config: 系统配置
            
        返回:
            是否初始化成功
        """
        try:
            self.simulation_start_date = datetime.fromisoformat(start_date)
            self.simulation_end_date = datetime.fromisoformat(end_date)
            self.active_agents = agents.copy()
            self.system_config = system_config.copy()
            
            # 初始化智能体配置
            for agent_id, agent in agents.items():
                self.agent_configurations[agent_id] = {
                    "original_prompt": getattr(agent, 'prompt_template', ''),
                    "current_prompt": getattr(agent, 'prompt_template', ''),
                    "optimization_history": [],
                    "performance_history": []
                }
            
            self.current_phase = OptimizationPhase.SIMULATION
            self.current_week = 0
            
            self.logger.info(f"模拟初始化成功: {start_date} 到 {end_date}")
            self.logger.info(f"智能体数量: {len(agents)}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"模拟初始化失败: {e}")
            return False
    
    def should_optimize(self, current_date: str, trading_days_elapsed: Optional[int] = None) -> bool:
        """
        判断是否应该进行优化

        参数:
            current_date: 当前日期
            trading_days_elapsed: 已经过的交易天数（可选，如果提供则优先使用）

        返回:
            是否应该优化
        """
        if not self.simulation_start_date:
            self.logger.debug(f"should_optimize: 模拟开始日期未设置，返回False")
            return False

        # 统一使用交易天数作为判断依据
        if trading_days_elapsed is not None:
            days_elapsed = trading_days_elapsed
            self.logger.debug(f"should_optimize: 使用交易天数计算 - 已过交易天数: {days_elapsed}")
        else:
            # 如果没有提供交易天数，则从日期估算交易天数（假设每周5个交易日）
            current_dt = datetime.fromisoformat(current_date)
            calendar_days = (current_dt - self.simulation_start_date).days
            # 估算交易天数：每7个日历日约有5个交易日
            estimated_trading_days = int(calendar_days * 5 / 7)
            days_elapsed = estimated_trading_days
            self.logger.debug(f"should_optimize: 从日历天数 {calendar_days} 估算交易天数 {days_elapsed}")

        # 检查是否达到最小优化天数要求
        min_days_required = self.config.min_days_for_optimization

        if days_elapsed < min_days_required:
            self.logger.debug(f"should_optimize: 交易天数不足 {days_elapsed} < {min_days_required}，返回False")
            return False

        # 检查是否是优化周期边界
        # 统一使用配置中的优化频率（已修改为5个交易日）
        optimization_frequency = self.config.optimization_frequency

        if days_elapsed % optimization_frequency == 0:
            self.logger.debug(f"should_optimize: 到达优化边界 {days_elapsed} % {optimization_frequency} == 0，返回True")
            return True

        self.logger.debug(f"should_optimize: 未到达优化边界 {days_elapsed} % {optimization_frequency} != 0，返回False")
        return False
    
    def get_current_week_number(self, current_date: str, trading_days_elapsed: Optional[int] = None) -> int:
        """
        获取当前周数

        参数:
            current_date: 当前日期
            trading_days_elapsed: 已经过的交易天数（可选，如果提供则优先使用）

        返回:
            当前周数
        """
        if not self.simulation_start_date:
            return 0

        # 统一使用交易天数作为判断依据
        if trading_days_elapsed is not None:
            days_elapsed = trading_days_elapsed
        else:
            # 如果没有提供交易天数，则从日期估算交易天数（假设每周5个交易日）
            current_dt = datetime.fromisoformat(current_date)
            calendar_days = (current_dt - self.simulation_start_date).days
            # 估算交易天数：每7个日历日约有5个交易日
            days_elapsed = int(calendar_days * 5 / 7)

        # 统一使用配置中的优化频率（已修改为5个交易日）
        optimization_frequency = self.config.optimization_frequency

        return days_elapsed // optimization_frequency + 1
    
    def execute_weekly_optimization(self,
                                  current_date: str,
                                  assessor: 'RefactoredContributionAssessor',
                                  week_data: Optional[Dict[str, Any]] = None) -> WeeklyOptimizationResult:
        """
        执行周期性优化

        参数:
            current_date: 当前日期
            assessor: 贡献评估器
            week_data: 当周数据（可选）

        返回:
            优化结果
        """
        start_time = time.time()
        week_number = self.get_current_week_number(current_date,
                                                  week_data.get("trading_days_elapsed") if week_data else None)

        self.logger.info(f"🚀 开始第 {week_number} 周优化 ({current_date})")

        # 使用TradingCalendar计算周期边界，确保与assessor一致
        week_start, week_end = self._get_week_boundaries_from_trading_calendar(
            current_date, week_number, assessor
        )
        
        result = WeeklyOptimizationResult(
            week_number=week_number,
            start_date=week_start,
            end_date=week_end,
            phase=OptimizationPhase.EVALUATION,
            shapley_values={},
            agent_performance={},
            agents_optimized=[],
            optimization_results={},
            estimated_improvements={},
            execution_time=0.0,
            success=False
        )
        
        try:
            # 阶段1：评估当前性能
            self.logger.info("📊 阶段1：评估当周智能体性能...")
            self.current_phase = OptimizationPhase.EVALUATION
            
            evaluation_result = self._evaluate_weekly_performance(
                assessor, week_start, week_end, week_data
            )
            
            if not evaluation_result.get("success", False):
                raise Exception(f"性能评估失败: {evaluation_result.get('error', 'Unknown error')}")
            
            result.shapley_values = evaluation_result.get("shapley_values", {})
            result.agent_performance = evaluation_result.get("agent_performance", {})
            
            # 阶段2：决定优化目标
            self.logger.info("🎯 阶段2：选择优化目标...")
            target_agents = self._select_optimization_targets(result.shapley_values)
            
            if not target_agents:
                self.logger.info("✅ 本周无需优化，所有智能体表现良好")
                result.success = True
                result.phase = OptimizationPhase.COMPLETION
                return result
            
            self.logger.info(f"选择优化目标: {', '.join(target_agents)}")
            
            # 阶段3：执行优化
            self.logger.info("🔧 阶段3：执行智能体优化...")
            self.current_phase = OptimizationPhase.OPTIMIZATION
            
            optimization_results = {}
            for agent_id in target_agents:
                opt_result = self._optimize_agent(agent_id, result.shapley_values[agent_id])
                optimization_results[agent_id] = opt_result
                
                if opt_result.get("success", False):
                    result.agents_optimized.append(agent_id)
                    result.estimated_improvements[agent_id] = opt_result.get("estimated_improvement", 0.0)
            
            result.optimization_results = optimization_results
            
            # 阶段4：应用优化结果
            if result.agents_optimized:
                self.logger.info("🔄 阶段4：应用优化结果...")
                self.current_phase = OptimizationPhase.APPLICATION
                
                self._apply_optimization_results(result.agents_optimized, optimization_results)
            
            # 更新统计信息
            self._update_statistics(result)
            
            result.success = True
            result.phase = OptimizationPhase.COMPLETION
            
            self.logger.info(f"✅ 第 {week_number} 周优化完成，优化了 {len(result.agents_optimized)} 个智能体")
            
        except Exception as e:
            self.logger.error(f"❌ 第 {week_number} 周优化失败: {e}")
            result.success = False
            result.error_message = str(e)
            result.phase = OptimizationPhase.COMPLETION
            
        finally:
            result.execution_time = time.time() - start_time
            self.weekly_results.append(result)
            self.current_week = week_number
            self.last_optimization_date = current_date
            
            # 保存结果
            self._save_weekly_result(result)
        
        return result
    
    def _evaluate_weekly_performance(self, 
                                   assessor: 'RefactoredContributionAssessor',
                                   start_date: str,
                                   end_date: str,
                                   week_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        评估周期性性能
        
        参数:
            assessor: 贡献评估器
            start_date: 开始日期
            end_date: 结束日期
            week_data: 周数据（可选）
            
        返回:
            评估结果
        """
        try:
            # 如果提供了周数据，直接使用
            if week_data and "shapley_values" in week_data:
                self.logger.info("✅ 使用已有的周数据，跳过重新评估")
                self.logger.info(f"📊 周数据包含 {len(week_data['shapley_values'])} 个智能体的Shapley值")
                return {
                    "success": True,
                    "shapley_values": week_data["shapley_values"],
                    "agent_performance": week_data.get("agent_performance", {})
                }

            # 调试信息：检查week_data的结构
            if week_data:
                self.logger.info(f"🔍 调试: week_data包含的键: {list(week_data.keys())}")
                if "shapley_values" not in week_data:
                    self.logger.warning("⚠️ week_data中没有shapley_values字段，需要重新评估")
            else:
                self.logger.info("⚠️ 没有提供week_data，需要重新评估")

            # 否则运行评估（使用重构后的assessor.run()方法）
            self.logger.info(f"📊 运行完整的周期性评估: {start_date} 至 {end_date}")
            
            # 如果 active_agents 为空，则让 assessor 使用默认智能体
            if not self.active_agents:
                self.logger.info("⚠️ 没有活跃智能体，将使用 assessor 的默认智能体")
                agents_to_use = None
                target_agents = None
            else:
                agents_to_use = self.active_agents
                target_agents = list(self.active_agents.keys())
            
            result = assessor.run(
                agents=agents_to_use,
                target_agents=target_agents,
                max_coalitions=None  # 使用所有联盟确保准确的Shapley值计算
            )
            
            if result.get("success", False):
                return {
                    "success": True,
                    "shapley_values": result.get("shapley_values", {}),
                    "agent_performance": result.get("agent_performance", {})
                }
            else:
                return {
                    "success": False,
                    "error": result.get("error", "Evaluation failed")
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def _select_optimization_targets(self, shapley_values: Dict[str, float]) -> List[str]:
        """
        选择优化目标智能体
        
        参数:
            shapley_values: Shapley值字典
            
        返回:
            需要优化的智能体列表
        """
        if not shapley_values:
            return []
        
        # 按Shapley值排序
        sorted_agents = sorted(shapley_values.items(), key=lambda x: x[1])
        
        target_agents = []
        
        if self.config.optimize_worst_performers:
            # 选择表现最差的智能体
            for agent_id, score in sorted_agents:
                if len(target_agents) >= self.config.max_agents_per_cycle:
                    break
                
                if score < self.config.min_performance_for_optimization:
                    target_agents.append(agent_id)
        
        return target_agents
    
    def _optimize_agent(self, agent_id: str, current_score: float) -> Dict[str, Any]:
        """
        优化单个智能体
        
        参数:
            agent_id: 智能体ID
            current_score: 当前得分
            
        返回:
            优化结果
        """
        try:
            self.logger.info(f"  优化智能体 {agent_id} (当前得分: {current_score:.6f})")
            
            # 获取当前提示词
            current_prompt = self.agent_configurations.get(agent_id, {}).get("current_prompt", "")
            if not current_prompt:
                self.logger.warning(f"智能体 {agent_id} 没有找到当前提示词，使用默认提示词")
                current_prompt = "你是一个专业的投资顾问，提供基于数据的投资建议。"
            
            # 运行OPRO优化
            optimization_result = self.opro_service.optimize_single_agent(
                agent_name=agent_id,
                current_prompt=current_prompt,
                performance_score=current_score
            )
            
            if optimization_result.get("success", False):
                optimized_prompt = optimization_result.get("optimized_prompt", "")
                estimated_improvement = optimization_result.get("estimated_improvement", 0.0)
                
                # 更新智能体配置
                self.agent_configurations[agent_id]["optimization_history"].append({
                    "timestamp": datetime.now().isoformat(),
                    "original_score": current_score,
                    "optimized_prompt": optimized_prompt,
                    "estimated_improvement": estimated_improvement
                })
                
                # 缓存优化后的提示词
                self.optimized_prompts[agent_id] = optimized_prompt
                
                self.logger.info(f"    ✅ 优化成功，预期改进: {estimated_improvement:.6f}")
                
                return {
                    "success": True,
                    "optimized_prompt": optimized_prompt,
                    "estimated_improvement": estimated_improvement,
                    "optimization_details": optimization_result
                }
            else:
                self.logger.warning(f"    ❌ 优化失败: {optimization_result.get('error', 'Unknown error')}")
                return {
                    "success": False,
                    "error": optimization_result.get("error", "Optimization failed")
                }
                
        except Exception as e:
            self.logger.error(f"    ❌ 智能体 {agent_id} 优化异常: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _apply_optimization_results(self, 
                                  optimized_agents: List[str], 
                                  optimization_results: Dict[str, Any]):
        """
        应用优化结果
        
        参数:
            optimized_agents: 已优化的智能体列表
            optimization_results: 优化结果字典
        """
        for agent_id in optimized_agents:
            if agent_id in optimization_results and optimization_results[agent_id].get("success", False):
                optimized_prompt = optimization_results[agent_id].get("optimized_prompt", "")
                
                # 更新智能体配置
                self.agent_configurations[agent_id]["current_prompt"] = optimized_prompt
                
                # 如果智能体仍在活跃状态，直接更新
                if agent_id in self.active_agents:
                    agent = self.active_agents[agent_id]
                    if hasattr(agent, 'update_prompt'):
                        agent.update_prompt(optimized_prompt)
                        self.logger.info(f"    🔄 智能体 {agent_id} 提示词已更新")
                    else:
                        self.logger.warning(f"    ⚠️ 智能体 {agent_id} 不支持动态提示词更新")
    
    def _update_statistics(self, result: WeeklyOptimizationResult):
        """
        更新统计信息
        
        参数:
            result: 优化结果
        """
        self.stats["total_weeks_processed"] += 1
        self.stats["total_optimizations"] += len(result.agents_optimized)
        
        if result.success:
            self.stats["successful_optimizations"] += 1
            
            # 更新改进统计
            total_improvement = sum(result.estimated_improvements.values())
            self.stats["total_improvements"] += total_improvement
            
            if self.stats["total_optimizations"] > 0:
                self.stats["average_improvement"] = self.stats["total_improvements"] / self.stats["total_optimizations"]
        else:
            self.stats["failed_optimizations"] += 1
        
        # 更新最佳和最差智能体
        if result.shapley_values:
            sorted_agents = sorted(result.shapley_values.items(), key=lambda x: x[1])
            self.stats["worst_performing_agent"] = sorted_agents[0][0]
            self.stats["best_performing_agent"] = sorted_agents[-1][0]
    
    def _save_weekly_result(self, result: WeeklyOptimizationResult):
        """
        保存周期性结果
        
        参数:
            result: 优化结果
        """
        try:
            result_file = os.path.join(
                self.results_dir,
                f"weekly_optimization_{result.week_number:03d}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            )
            
            # 创建可序列化的结果字典
            serializable_result = self._make_serializable(asdict(result))
            
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(serializable_result, f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.info(f"周期性结果已保存: {result_file}")
            
        except Exception as e:
            self.logger.error(f"保存周期性结果失败: {e}")
    
    def get_optimization_status(self) -> Dict[str, Any]:
        """
        获取优化状态
        
        返回:
            优化状态信息
        """
        return {
            "current_phase": self.current_phase.value,
            "current_week": self.current_week,
            "last_optimization": self.last_optimization_date,
            "total_weeks_processed": len(self.weekly_results),
            "successful_optimizations": sum(1 for r in self.weekly_results if r.success),
            "failed_optimizations": sum(1 for r in self.weekly_results if not r.success),
            "active_agents": len(self.active_agents),
            "optimized_agents": len(self.optimized_prompts),
            "statistics": self.stats,
            "config": self.config.to_dict()
        }
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """
        获取性能摘要
        
        返回:
            性能摘要信息
        """
        if not self.weekly_results:
            return {"status": "no_data"}
        
        # 计算整体统计
        total_improvements = sum(
            sum(r.estimated_improvements.values()) for r in self.weekly_results
        )
        
        successful_weeks = [r for r in self.weekly_results if r.success]
        
        agent_performance = {}
        for agent_id in self.active_agents:
            agent_improvements = []
            for result in self.weekly_results:
                if agent_id in result.estimated_improvements:
                    agent_improvements.append(result.estimated_improvements[agent_id])
            
            if agent_improvements:
                agent_performance[agent_id] = {
                    "total_optimizations": len(agent_improvements),
                    "total_improvement": sum(agent_improvements),
                    "average_improvement": sum(agent_improvements) / len(agent_improvements),
                    "best_improvement": max(agent_improvements),
                    "worst_improvement": min(agent_improvements)
                }
        
        return {
            "status": "active",
            "total_weeks": len(self.weekly_results),
            "successful_weeks": len(successful_weeks),
            "success_rate": len(successful_weeks) / len(self.weekly_results) * 100,
            "total_improvements": total_improvements,
            "average_improvement_per_week": total_improvements / len(self.weekly_results),
            "agent_performance": agent_performance,
            "current_status": self.get_optimization_status()
        }
    
    def export_results(self, output_path: str) -> bool:
        """
        导出结果
        
        参数:
            output_path: 输出路径
            
        返回:
            是否导出成功
        """
        try:
            export_data = {
                "export_timestamp": datetime.now().isoformat(),
                "configuration": self.config.to_dict(),
                "weekly_results": [asdict(r) for r in self.weekly_results],
                "agent_configurations": self.agent_configurations,
                "performance_summary": self.get_performance_summary(),
                "optimization_status": self.get_optimization_status()
            }
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"结果导出成功: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"结果导出失败: {e}")
            return False
    
    def cleanup(self):
        """清理资源"""
        try:
            # 清理旧数据
            if self.config.cleanup_old_data:
                self.historical_manager.cleanup_old_data(days_to_keep=30)
            
            # 导出最终结果
            final_export_path = os.path.join(
                self.results_dir,
                f"final_optimization_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            )
            self.export_results(final_export_path)
            
            self.logger.info("周期性优化管理器清理完成")
            
        except Exception as e:
            self.logger.error(f"清理失败: {e}")

    def _get_week_boundaries_from_trading_calendar(self,
                                                 current_date: str,
                                                 week_number: int,
                                                 assessor) -> Tuple[str, str]:
        """
        使用TradingCalendar获取周边界，确保与assessor的周划分逻辑一致

        参数:
            current_date: 当前日期
            week_number: 周数
            assessor: 贡献评估器（用于获取配置）

        返回:
            (week_start, week_end) 元组
        """
        try:
            # 导入TradingCalendar
            from utils.trading_calendar import TradingCalendar

            # 获取配置信息
            start_date = assessor.config.get("start_date")
            end_date = assessor.config.get("end_date")
            stocks = assessor.config.get("stocks", ["AAPL"])

            if not start_date or not end_date:
                self.logger.warning("无法从assessor获取日期配置，使用默认方法")
                return self._get_week_boundaries_fallback(current_date)

            # 创建交易日历
            calendar = TradingCalendar(logger=self.logger)

            # 获取实际交易日
            trading_days = calendar.get_trading_days(
                start_date, end_date, use_database=True, stocks=stocks
            )

            if not trading_days:
                self.logger.warning("无法获取交易日数据，使用默认方法")
                return self._get_week_boundaries_fallback(current_date)

            # 进行周划分
            trading_weeks = calendar.get_trading_weeks(
                trading_days, strategy="adaptive", days_per_week=5
            )

            # 找到对应的周
            if week_number <= len(trading_weeks):
                target_week = trading_weeks[week_number - 1]
                week_start = target_week['start_date']
                week_end = target_week['end_date']

                self.logger.info(f"📅 使用TradingCalendar获取第{week_number}周边界: {week_start} 到 {week_end}")
                return week_start, week_end
            else:
                self.logger.warning(f"周数{week_number}超出范围，使用默认方法")
                return self._get_week_boundaries_fallback(current_date)

        except Exception as e:
            self.logger.error(f"使用TradingCalendar获取周边界失败: {e}")
            return self._get_week_boundaries_fallback(current_date)

    def _get_week_boundaries_fallback(self, current_date: str) -> Tuple[str, str]:
        """
        默认的周边界计算方法（降级方案）

        参数:
            current_date: 当前日期

        返回:
            (week_start, week_end) 元组
        """
        current_dt = datetime.fromisoformat(current_date)
        week_start = current_dt - timedelta(days=self.config.optimization_frequency)
        week_end = current_dt

        return week_start.isoformat(), week_end.isoformat()
    
    def _make_serializable(self, obj):
        """
        将对象转换为JSON可序列化的格式
        
        参数:
            obj: 要序列化的对象
            
        返回:
            JSON可序列化的对象
        """
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif hasattr(np, 'integer') and isinstance(obj, np.integer):
            return int(obj)
        elif hasattr(np, 'floating') and isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif hasattr(obj, '__dict__'):
            # 处理有__dict__属性的对象
            return self._make_serializable(obj.__dict__)
        elif isinstance(obj, Enum):
            # 处理枚举类型
            return obj.value
        elif hasattr(obj, 'isoformat'):
            # 处理日期时间对象
            return obj.isoformat()
        else:
            # 其他类型尝试转换为字符串
            try:
                return str(obj)
            except:
                return f"<非序列化对象: {type(obj).__name__}>"
    
    def get_original_agents(self) -> Optional[Dict[str, Any]]:
        """
        获取原始智能体状态（优化前的状态）
        
        用于A/B测试中获取未经优化的智能体作为对比基准。
        该方法从智能体配置历史中恢复原始状态。
        
        返回:
            原始智能体字典，如果没有原始状态则返回None
        """
        if not hasattr(self, 'active_agents') or not self.active_agents:
            self.logger.warning("没有活跃的智能体，无法获取原始状态")
            return None
        
        if not hasattr(self, 'agent_configurations') or not self.agent_configurations:
            self.logger.warning("没有智能体配置历史，无法恢复原始状态")
            return None
        
        try:
            # 创建原始智能体的深拷贝
            original_agents = {}
            
            for agent_id, agent in self.active_agents.items():
                if agent_id in self.agent_configurations:
                    config = self.agent_configurations[agent_id]
                    original_prompt = config.get("original_prompt", "")
                    
                    # 深拷贝智能体
                    original_agent = copy.deepcopy(agent)
                    
                    # 恢复原始提示词
                    if hasattr(original_agent, 'prompt_template'):
                        original_agent.prompt_template = original_prompt
                        self.logger.debug(f"恢复智能体 {agent_id} 的原始提示词")
                    elif hasattr(original_agent, 'prompt'):
                        original_agent.prompt = original_prompt
                        self.logger.debug(f"恢复智能体 {agent_id} 的原始提示")
                    
                    original_agents[agent_id] = original_agent
                else:
                    # 如果没有配置历史，使用当前状态
                    self.logger.warning(f"智能体 {agent_id} 没有配置历史，使用当前状态")
                    original_agents[agent_id] = copy.deepcopy(agent)
            
            self.logger.info(f"成功恢复 {len(original_agents)} 个原始智能体状态")
            return original_agents
            
        except Exception as e:
            self.logger.error(f"恢复原始智能体状态失败: {e}")
            return None
    
    def get_optimization_history(self) -> Dict[str, Any]:
        """
        获取完整的优化历史记录
        
        返回:
            包含原始提示词和优化历史的字典
        """
        if not hasattr(self, 'agent_configurations'):
            return {}
        
        history = {
            "agent_configurations": self.agent_configurations,
            "optimization_statistics": getattr(self, 'optimization_statistics', {}),
            "weekly_results": getattr(self, 'weekly_results', [])
        }
        
        return history