"""
交易模拟引擎 (Trading Simulation Engine)

本模块实现了针对特定智能体联盟的交易模拟功能，用于计算联盟的特征函数值v(S)。
通过集成交易环境和智能体协调逻辑，为Shapley值计算提供准确的性能评估。

主要功能：
1. 为指定联盟运行完整的交易模拟
2. 按依赖关系动态执行智能体
3. 利用分析缓存避免重复计算
4. 计算并返回夏普比率作为特征函数值
"""

import numpy as np
import pandas as pd
import time
import copy
from typing import Dict, Any, List, Set, Optional, Union
from datetime import datetime
import logging

# 导入每日状态管理器
try:
    from state_management.daily_state_manager import DailyStateManager
except ImportError:
    # 如果导入失败，使用None占位符
    DailyStateManager = None
import hashlib

# 导入并行LLM管理器
try:
    from .parallel_llm_manager import ParallelLLMManager
except ImportError:
    ParallelLLMManager = None

# 导入项目依赖
from stock_trading_env import StockTradingEnv


class TradingSimulator:
    """
    交易模拟器
    
    负责为特定智能体联盟运行交易模拟，计算联盟的性能指标。
    支持缓存优化和依赖关系管理，确保模拟的准确性和效率。
    """
    
    def __init__(self, 
                 base_config: Dict[str, Any],
                 logger: Optional[logging.Logger] = None):
        """
        初始化交易模拟器
        
        参数:
            base_config: 基础配置，包含交易环境配置
            logger: 日志记录器，如果为None则创建默认记录器
        """
        self.logger = logger or self._create_default_logger()
        self.base_config = base_config
        
        # 并行执行配置
        self.enable_parallel_execution = base_config.get("enable_parallel_execution", False)
        self.parallel_llm_manager = None
        if self.enable_parallel_execution and ParallelLLMManager:
            max_concurrent = base_config.get("max_concurrent_agents", 3)
            parallel_timeout = base_config.get("parallel_timeout", 120.0)  # 默认2分钟
            self.parallel_llm_manager = ParallelLLMManager(
                max_concurrent=max_concurrent, 
                timeout=parallel_timeout
            )
            self.logger.info(f"✅ 并行LLM管理器已启用 (并发: {max_concurrent}, 超时: {parallel_timeout}s)")
        
        # 周期性评估配置
        self.weekly_evaluation_enabled = base_config.get("weekly_evaluation_enabled", True)
        self.trading_days_per_week = base_config.get("trading_days_per_week", 5)
        self.weekly_shapley_callback = None  # 周末Shapley值计算回调函数
        self.current_simulation_week = None  # 当前模拟周数（用于正确显示日志）
        
        # 周期性评估统计
        self._weekly_stats = {
            "completed_weeks": 0,
            "shapley_calculations": 0,
            "strategy_adjustments": 0,
            "last_weekly_evaluation": None
        }
        
        # 模拟统计信息
        self._stats = {
            "total_simulations": 0,
            "successful_simulations": 0,
            "failed_simulations": 0,
            "total_simulation_time": 0.0,
            "average_simulation_time": 0.0,
            "last_simulation": None
        }
        
        # 智能体依赖关系图（与CoalitionManager保持一致）
        self.agent_graph = {
            "NAA": [],  # 新闻分析智能体，无依赖
            "TAA": [],  # 技术分析智能体，无依赖
            "FAA": [],  # 基本面分析智能体，无依赖
            "BOA": ["NAA", "TAA", "FAA"],  # 看多展望智能体，依赖分析层
            "BeOA": ["NAA", "TAA", "FAA"], # 看空展望智能体，依赖分析层
            "NOA": ["NAA", "TAA", "FAA"],  # 中立观察智能体，依赖分析层
            "TRA": ["BOA", "BeOA", "NOA"]  # 交易智能体，依赖展望层
        }
        
        # 智能体层级（用于按序执行）
        self.agent_layers = [
            ["NAA", "TAA", "FAA"],  # 分析层
            ["BOA", "BeOA", "NOA"], # 展望层
            ["TRA"]                 # 决策层
        ]
        
        # 风险无关收益率（用于夏普比率计算）
        self.risk_free_rate = base_config.get("risk_free_rate", 0.02)  # 默认2%年化

        # 详细日志模式控制
        self.detailed_logging = False

        # 初始化每日状态管理器
        self.daily_state_manager = None
        if DailyStateManager is not None:
            try:
                self.daily_state_manager = DailyStateManager()
                self.logger.debug("DailyStateManager初始化成功")
            except Exception as e:
                self.logger.warning(f"DailyStateManager初始化失败，将跳过状态持久化: {e}")

        # self.logger.info("交易模拟器初始化完成")

    def set_detailed_logging(self, detailed: bool) -> None:
        """
        设置详细日志模式

        参数:
            detailed: True为详细模式，False为简洁模式
        """
        self.detailed_logging = detailed
        # mode_str = "详细" if detailed else "简洁"
        # self.logger.debug(f"🔧 交易模拟器日志模式设置为: {mode_str}模式")

    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.TradingSimulator")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def shutdown(self):
        """清理资源"""
        if self.parallel_llm_manager:
            self.parallel_llm_manager.shutdown()
            self.logger.info("并行LLM管理器已关闭")
    
    def run_simulation_for_coalition(self,
                                   coalition: Union[Set[str], List[str]],
                                   agents: Dict[str, Any],
                                   simulation_days: Optional[int] = None,
                                   current_week_number: Optional[int] = None,
                                   stop_after_one_week: bool = False,
                                   is_full_coalition: bool = False,  # 新增标志
                                   config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        为指定联盟运行交易模拟并计算夏普比率（实时分析模式）

        这是核心方法，执行以下步骤：
        1. 初始化交易环境
        2. 每个交易日实时调用联盟中的智能体进行分析
        3. 运行完整的交易周期（支持周期性评估）
        4. 计算并返回详细的模拟结果

        注意：已取消分析缓存，改为每次实时调用智能体进行分析

        参数:
            coalition: 智能体联盟（智能体ID集合或列表）
            agents: 智能体实例字典（必需，不能为None）
            simulation_days: 模拟天数，如果为None则使用配置中的默认值
            current_week_number: 当前周数，用于正确显示周级日志（可选）
            stop_after_one_week: 是否在一周结束后停止模拟（5个交易日循环模式）

        返回:
            包含以下字段的字典：
            - sharpe_ratio: 联盟的夏普比率（特征函数值v(S)）
            - daily_returns: 每日收益率列表
            - weekly_data: 周级数据列表
            - total_days: 总模拟天数
            - simulation_time: 模拟耗时（秒）
            - error: 错误信息（如果有）
        """
        start_time = time.time()
        coalition_set = set(coalition) if isinstance(coalition, list) else coalition
        
        self.logger.info(f"开始联盟交易模拟: {coalition_set}")
        
        try:
            # 验证联盟有效性
            if not self._validate_coalition(coalition_set):
                self.logger.warning(f"无效联盟: {coalition_set}")
                return {
                    "sharpe_ratio": 0.0,
                    "daily_returns": [],
                    "weekly_data": [],
                    "total_days": 0,
                    "simulation_time": 0.0,
                    "error": "Invalid coalition"
                }
            
            # 初始化交易环境
            coalition_id = self._generate_coalition_id(coalition_set)
            env = self._create_trading_environment(simulation_days, coalition_id, current_week_number, config)
            
            # 收集每日收益率和周级数据
            daily_returns = []
            weekly_data = []
            current_week_returns = []
            # 优先使用传入的周数，其次使用设置的周数，最后默认从1开始
            if current_week_number is not None:
                week_counter = current_week_number
                # 同时更新实例变量，确保一致性
                self.current_simulation_week = current_week_number
                if self.detailed_logging:
                    self.logger.info(f"📅 设置当前模拟周数: {self.current_simulation_week} (来自参数)")
            elif self.current_simulation_week is not None:
                week_counter = self.current_simulation_week
                if self.detailed_logging:
                    self.logger.info(f"📅 使用当前模拟周数: {self.current_simulation_week} (来自实例变量)")
            else:
                week_counter = 1
                self.current_simulation_week = 1
                if self.detailed_logging:
                    self.logger.info(f"📅 设置默认模拟周数: {self.current_simulation_week}")
            
            # 运行交易模拟
            # 修复：使用 env.total_days - 1 避免索引越界
            for day in range(env.total_days - 1):
                try:
                    # 获取当前状态
                    state = env.get_state()

                    # 实时分析模式：每个交易日直接调用智能体
                    agent_outputs = self._execute_coalition_agents_real_time(
                        coalition_set, state, agents, env, is_full_coalition
                    )

                    # 获取交易决策
                    trading_action = self._get_trading_action(agent_outputs, coalition_set)

                    # 调试信息：记录交易动作（只在详细模式下显示）
                    if trading_action != {"__HOLD__": 1.0} and self.detailed_logging:
                        self.logger.info(f"📈 第{day+1}天交易动作: {trading_action}")

                    # 执行交易
                    _, _, done, info = env.step(trading_action)

                    # 更新决策性能数据
                    self._update_decision_performance(env, coalition_set, info)

                    # 调试信息：记录净值变化（只在详细模式下显示）
                    daily_return = info.get("daily_return", 0.0)
                    if abs(daily_return) > 0.001 and self.detailed_logging:  # 显著变化且详细模式
                        net_worth = info.get("net_worth", 0)
                        self.logger.info(f"💰 第{day+1}天净值变化: 收益率={daily_return:.4f}, 净值=${net_worth:.2f}")

                    # 记录日收益率
                    daily_return = info.get("daily_return", 0.0)
                    daily_returns.append(daily_return)
                    current_week_returns.append(daily_return)

                    # 检查是否完成一周的交易
                    if self._is_week_end(day):
                        week_data = self._process_weekly_data(
                            current_week_returns, coalition_set, day, week_counter
                        )
                        weekly_data.append(week_data)

                        # 执行周末Shapley值计算
                        if self.weekly_evaluation_enabled:
                            self._trigger_weekly_shapley_calculation(
                                coalition_set, week_data, weekly_data
                            )

                        current_week_returns = []  # 重置周收益率
                        
                        # 只有在非stop_after_one_week模式下才增加周数
                        # 在stop_after_one_week模式下，我们保持当前周数不变
                        if not stop_after_one_week:
                            week_counter += 1  # 增加周数
                            self.current_simulation_week = week_counter  # 同步更新实例变量
                            if self.detailed_logging:
                                self.logger.info(f"📅 周数增加: 当前为第{week_counter}周")
                        else:
                            if self.detailed_logging:
                                self.logger.info(f"📅 保持当前周数: 第{week_counter}周 (stop_after_one_week=True)")
                        
                        # 5个交易日循环模式：在一周结束后停止
                        if stop_after_one_week:
                            if self.detailed_logging:
                                self.logger.info(f"🔄 5日循环模式：第{week_counter-1}周完成，停止模拟")
                            
                            # 记录周期边界信息，便于调试
                            self.logger.info(f"📊 周期边界: 当前周={week_counter-1}, 下一周={week_counter}, 交易日={day}")
                            
                            # 确保不会重复执行相同周的交易模拟
                            break

                    if done:
                        break

                except Exception as e:
                    self.logger.error(f"第 {day + 1} 天模拟失败: {e}")
                    daily_returns.append(0.0)  # 失败时记录0收益
                    current_week_returns.append(0.0)
            
            # 处理最后不完整的周（只有在有足够数据且不是跨周的情况下才处理）
            if current_week_returns and len(current_week_returns) >= 2:
                # 检查是否真的是不完整周，而不是跨周的错误数据
                total_trading_days = len(daily_returns)
                expected_complete_weeks = total_trading_days // self.trading_days_per_week

                # 只有当确实存在不完整周时才处理
                if week_counter <= expected_complete_weeks + 1:
                    # 记录不完整周的信息
                    self.logger.info(f"📊 处理不完整周: 周数={week_counter}, 天数={len(current_week_returns)}, 预期完整周={expected_complete_weeks}")
                    
                    week_data = self._process_weekly_data(
                        current_week_returns, coalition_set, len(daily_returns) - 1, week_counter
                    )
                    weekly_data.append(week_data)

                    if self.weekly_evaluation_enabled:
                        self._trigger_weekly_shapley_calculation(
                            coalition_set, week_data, weekly_data
                        )
                elif self.detailed_logging:
                    self.logger.debug(f"跳过异常周数据（周{week_counter}，预期最多{expected_complete_weeks + 1}周）: {coalition_set}")
            elif current_week_returns and self.detailed_logging:
                # 如果不完整周数据不足，记录调试信息
                self.logger.debug(f"跳过不完整周（仅{len(current_week_returns)}天数据）: {coalition_set}")
            
            # 计算夏普比率
            sharpe_ratio = self._calculate_sharpe_ratio(daily_returns)
            
            # 更新统计信息
            simulation_time = time.time() - start_time
            self._update_stats(simulation_time, success=True)
            
            # 详细诊断日志（只在详细模式下显示）
            if self.detailed_logging:
                self.logger.info(f"✅ 联盟 {coalition_set} 模拟完成:")
                self.logger.info(f"  📊 夏普比率: {sharpe_ratio:.6f}")
                self.logger.info(f"  📈 日收益率数量: {len(daily_returns)}")
                self.logger.info(f"  🕐 模拟时间: {simulation_time:.2f}s")
            
            # 如果Sharpe比率为0，额外诊断
            if sharpe_ratio == 0.0:
                self.logger.warning(f"🚨 联盟 {coalition_set} Sharpe比率为0！")
                if daily_returns:
                    non_zero_returns = [r for r in daily_returns if abs(r) > 1e-10]
                    self.logger.warning(f"  非零收益率天数: {len(non_zero_returns)}/{len(daily_returns)}")
                    if non_zero_returns:
                        self.logger.warning(f"  非零收益率范围: {min(non_zero_returns):.6f} ~ {max(non_zero_returns):.6f}")
            
            # 保存最终的每日state到数据库（如果可用）
            self._persist_final_daily_states(env, daily_returns)
            
            # 返回增强格式，包含每日收益数据
            return {
                "sharpe_ratio": sharpe_ratio,
                "daily_returns": daily_returns,
                "weekly_data": weekly_data,
                "total_days": len(daily_returns),
                "simulation_time": simulation_time
            }
            
        except Exception as e:
            self.logger.error(f"联盟 {coalition_set} 模拟失败: {e}")
            simulation_time = time.time() - start_time
            self._update_stats(simulation_time, success=False)
            return {
                "sharpe_ratio": 0.0,
                "daily_returns": [],
                "weekly_data": [],
                "total_days": 0,
                "simulation_time": simulation_time,
                "error": str(e)
            }
    
    def _validate_coalition(self, coalition: Set[str]) -> bool:
        """
        验证联盟的有效性
        
        检查联盟是否满足基本约束：
        1. 必须包含TRA（交易智能体）
        2. 必须至少包含一个分析智能体
        
        参数:
            coalition: 智能体联盟
            
        返回:
            联盟是否有效
        """
        # 检查是否包含交易智能体
        if "TRA" not in coalition:
            return False
        
        # 检查是否包含至少一个分析智能体
        analyst_agents = {"NAA", "TAA", "FAA"}
        if not coalition.intersection(analyst_agents):
            return False
        
        return True

    def _validate_state_data(self, state: Dict[str, Any], day: int) -> Dict[str, Any]:
        """
        验证state数据的质量和完整性

        参数:
            state: 当前状态数据
            day: 当前交易日

        返回:
            验证结果字典，包含valid字段和issues列表
        """
        issues = []

        try:
            # 检查基本字段
            required_fields = ['current_date', 'cash', 'positions']
            for field in required_fields:
                if field not in state:
                    issues.append(f"缺少必需字段: {field}")

            # 检查日期格式
            current_date = state.get('current_date')
            if current_date and not isinstance(current_date, str):
                issues.append(f"日期格式错误: {type(current_date)}")

            # 检查价格数据
            price_history = state.get('price_history', {})
            if not price_history:
                issues.append("缺少价格历史数据")
            else:
                # 检查价格数据是否为空
                has_price_data = any(
                    isinstance(prices, list) and len(prices) > 0
                    for prices in price_history.values()
                )
                if not has_price_data:
                    issues.append("价格历史数据为空")

            # 检查新闻数据（可选，但如果存在应该有效）
            news_history = state.get('news_history', {})
            if news_history:
                news_count = 0
                for date_news in news_history.values():
                    if isinstance(date_news, dict):
                        for news_list in date_news.values():
                            if isinstance(news_list, list):
                                news_count += len(news_list)
                if news_count == 0:
                    issues.append("新闻历史数据存在但为空")

            # 检查基本面数据（可选，但如果存在应该有效）
            fundamental_data = state.get('fundamental_data', {})
            if fundamental_data:
                has_fund_data = any(
                    isinstance(fund_data, dict) and fund_data
                    for fund_data in fundamental_data.values()
                )
                if not has_fund_data:
                    issues.append("基本面数据存在但为空")

        except Exception as e:
            issues.append(f"数据验证异常: {str(e)}")

        return {
            "valid": len(issues) == 0,
            "issues": issues,
            "day": day
        }

    def _generate_coalition_id(self, coalition: Set[str]) -> str:
        """
        生成联盟ID
        
        参数:
            coalition: 智能体联盟
            
        返回:
            联盟ID字符串
        """
        # 按字母顺序排序联盟成员，确保一致性
        sorted_agents = sorted(list(coalition))
        coalition_str = "_".join(sorted_agents)
        # 使用哈希生成较短的ID
        coalition_hash = hashlib.md5(coalition_str.encode()).hexdigest()[:8]
        return f"coalition_{coalition_hash}_{coalition_str}"
    
    def _create_trading_environment(self, simulation_days: Optional[int] = None, coalition_id: Optional[str] = None, current_week_number: Optional[int] = None, config: Optional[Dict[str, Any]] = None) -> StockTradingEnv:
        """
        创建交易环境实例
        
        参数:
            simulation_days: 模拟天数
            coalition_id: 联盟ID，用于决策快照上下文
            current_week_number: 当前周数，用于调整交易起始日期
            
        返回:
            交易环境实例
        """
        # 复制基础配置
        env_config = copy.deepcopy(self.base_config)
        
        # 获取基础起始日期
        base_start_date = pd.to_datetime(env_config.get("start_date", "2025-01-01"))
        
        # 根据当前周数调整起始日期
        if current_week_number is not None and current_week_number > 1:
            # 统一使用日历周：每周7个日历日，非交易日自动跳过
            calendar_days_per_week = 7
            
            # 调整起始日期
            adjusted_start_date = base_start_date + pd.Timedelta(days=calendar_days_per_week * (current_week_number - 1))
            
            if self.detailed_logging:
                self.logger.info(f"📅 调整交易起始日期: 从 {base_start_date.strftime('%Y-%m-%d')} 到 {adjusted_start_date.strftime('%Y-%m-%d')} (第{current_week_number}周)")
            
            # 更新配置
            env_config["start_date"] = adjusted_start_date.strftime("%Y-%m-%d")
            
            # 调整结束日期（统一为一周的日期范围）
            if simulation_days is not None:
                end_date = adjusted_start_date + pd.Timedelta(days=simulation_days * 1.5)  # 考虑非交易日
            else:
                # 默认一周的时间范围（7个日历日，包含非交易日）
                end_date = adjusted_start_date + pd.Timedelta(days=7)
            env_config["end_date"] = end_date.strftime("%Y-%m-%d")
        else:
            # 如果是第一周或未指定周数，使用原始逻辑
            if simulation_days is not None:
                # 调整结束日期以匹配模拟天数
                end_date = base_start_date + pd.Timedelta(days=simulation_days * 1.5)  # 考虑非交易日
                env_config["end_date"] = end_date.strftime("%Y-%m-%d")
        
        # 启用决策快照捕获
        env_config["enable_decision_snapshots"] = True
        
        # 合并config参数（支持状态继承）- 但保护调整后的日期
        if config:
            # 保存已调整的日期（如果有）
            adjusted_start_date_str = env_config.get("start_date")
            adjusted_end_date_str = env_config.get("end_date")
            
            # 更新配置
            env_config.update(config)
            
            # 如果我们之前调整了日期，确保不被覆盖
            if current_week_number is not None and current_week_number > 1:
                if adjusted_start_date_str:
                    env_config["start_date"] = adjusted_start_date_str
                if adjusted_end_date_str:
                    env_config["end_date"] = adjusted_end_date_str
                    
                if self.detailed_logging:
                    self.logger.info(f"🔒 保护调整后的日期: start_date={adjusted_start_date_str}, end_date={adjusted_end_date_str}")
        
        # 确保状态继承配置被正确传递
        if "portfolio_tracker" in self.base_config:
            env_config["portfolio_tracker"] = self.base_config["portfolio_tracker"]
        
        if self.detailed_logging:
            has_tracker = "portfolio_tracker" in env_config
            has_inherited = "inherited_portfolio_state" in env_config
            self.logger.info(f"📋 创建交易环境: 跟踪器={has_tracker}, 继承状态={has_inherited}")
            
            if has_inherited and env_config["inherited_portfolio_state"]:
                inherited_state = env_config["inherited_portfolio_state"]
                cum_return = inherited_state.get("cumulative_return", 0.0)
                self.logger.info(f"🔄 环境将继承累计收益率: {cum_return:.4f}")
        
        # 创建环境实例
        env = StockTradingEnv(env_config)
        
        # 设置联盟上下文用于决策快照
        if coalition_id:
            evaluation_run_id = f"eval_{int(time.time())}_{coalition_id}"
            env.set_coalition_context(coalition_id, evaluation_run_id)
        
        return env
    
    def _execute_coalition_agents_real_time(self,
                                           coalition: Set[str],
                                           state: Dict[str, Any],
                                           agents: Dict[str, Any],
                                           env: Optional[StockTradingEnv] = None,
                                           is_full_coalition: bool = False) -> Dict[str, Any]:
        """
        实时执行联盟中的智能体（无缓存模式）
        
        每次直接调用智能体进行实时分析，不依赖任何缓存机制。
        按TAA优先的层级结构执行：分析层 → 展望层 → 交易层
        
        参数:
            coalition: 智能体联盟（必须包含对应的智能体ID）
            state: 当前环境状态
            agents: 智能体实例字典（必须包含联盟中的所有智能体）
            env: 交易环境实例
            is_full_coalition: 是否为完整联盟，用于控制IO记录
            
        返回:
            智能体实时分析输出字典
        """
        agent_outputs = {}
        
        
        # 按层级实时执行智能体（无缓存模式）
        for layer_idx, layer in enumerate(self.agent_layers):
            # 分析层特殊处理：支持并行或顺序执行
            if layer_idx == 0:  # 分析层 ["NAA", "TAA", "FAA"]
                if self.enable_parallel_execution and self.parallel_llm_manager:
                    self._execute_analysis_layer_parallel(coalition, state, agents, env, is_full_coalition)
                else:
                    self._execute_analysis_layer_sequential(coalition, state, agents, env, is_full_coalition)
                # 分析层完成后，从state中获取analyst_outputs更新agent_outputs
                analyst_outputs = state.get("analyst_outputs", {})
                agent_outputs.update(analyst_outputs)
            elif layer_idx == 1:  # 展望层 ["BOA", "BeOA", "NOA"]
                if self.enable_parallel_execution and self.parallel_llm_manager:
                    self._execute_outlook_layer_parallel(coalition, state, agents, env, is_full_coalition)
                else:
                    self._execute_outlook_layer_sequential(coalition, state, agents, env, is_full_coalition)
                # 展望层完成后，从state中获取outlook_outputs更新agent_outputs
                outlook_outputs = state.get("outlook_outputs", {})
                agent_outputs.update(outlook_outputs)
            else:
                # 其他层级（决策层）使用串行执行
                for agent_id in layer:
                    if agent_id not in coalition:
                        continue
                    
                    try:
                        # 显示智能体执行开始日志
                        if self.detailed_logging:
                            layer_name = "展望层" if agent_id in ["BOA", "BeOA", "NOA"] else "决策层"
                            self.logger.info(f"🤖 执行{layer_name}智能体 {agent_id}...")
                        
                        # 实时分析模式：直接调用智能体进行分析
                        if agent_id in agents:
                            # 详细日志模式：显示LLM输入
                            if self.detailed_logging:
                                self._log_agent_input(agent_id, state, agents[agent_id])

                            # 捕获决策前的上下文
                            decision_start_time = time.time()
                            
                            agent_output = agents[agent_id].process(state, is_full_coalition=is_full_coalition)
                            
                            # 计算执行时间
                            execution_time = time.time() - decision_start_time

                            # 详细日志模式：显示LLM输出
                            if self.detailed_logging:
                                self._log_agent_output(agent_id, agent_output)
                            
                            # 捕获决策快照（如果环境支持）
                            if env and hasattr(env, 'capture_agent_decision_context'):
                                self._capture_agent_decision_snapshot(
                                    env, agent_id, state, agent_output, execution_time, agents[agent_id]
                                )

                            # 标记为实时分析结果
                            if isinstance(agent_output, dict):
                                agent_output["source"] = "real_time_analysis"
                                agent_output["timestamp"] = state.get("current_date", "unknown")

                            agent_outputs[agent_id] = agent_output
                            
                            # 根据层级更新state对象的标准化字段
                            if agent_id in ["BOA", "BeOA", "NOA"]:  # 展望层
                                if "outlook_outputs" not in state:
                                    state["outlook_outputs"] = {}
                                state["outlook_outputs"][agent_id] = agent_output
                                # 保持向后兼容性
                                if "previous_outputs" not in state:
                                    state["previous_outputs"] = {}
                                state["previous_outputs"][agent_id] = agent_output
                            elif agent_id == "TRA":  # 决策层
                                if "trading_outputs" not in state:
                                    state["trading_outputs"] = {}
                                state["trading_outputs"][agent_id] = agent_output
                                # 保持向后兼容性
                                if "previous_outputs" not in state:
                                    state["previous_outputs"] = {}
                                state["previous_outputs"][agent_id] = agent_output

                            # 显示智能体执行完成日志
                            if self.detailed_logging:
                                if agent_id in ["BOA", "BeOA", "NOA"]:
                                    # 展望层智能体特殊处理：显示关键输出信息
                                    outlook_info = self._extract_outlook_info(agent_output)
                                    self.logger.info(f"✅ 展望层智能体 {agent_id} 完成: {outlook_info}")
                                elif agent_id == "TRA":
                                    # 交易智能体特殊处理：显示交易决策
                                    if isinstance(agent_output, dict) and "action" in agent_output:
                                        action = agent_output.get("action", "hold")
                                        confidence = agent_output.get("confidence", 0.0)
                                        reasoning = agent_output.get("reasoning", "无理由")
                                        self.logger.info(f"✅ 决策层智能体 TRA 完成: {action} (信心度: {confidence:.2f})")
                                        self.logger.info(f"📝 决策理由: {reasoning}")
                                else:
                                    self.logger.info(f"✅ 智能体 {agent_id} 执行完成")

                            # 简化完成日志 - 避免重复
                        else:
                            # 如果没有对应的智能体实例，抛出异常
                            raise RuntimeError(f"智能体 {agent_id} 实例不存在，无法进行实时分析")
                    
                    except Exception as e:
                        self.logger.error(f"❌ 智能体 {agent_id} 实时分析失败: {e}")
                        # 实时分析模式下，如果失败则直接抛出异常，不使用默认值
                        raise RuntimeError(f"联盟 {coalition} 中的智能体 {agent_id} 实时分析失败，拒绝使用模拟数据: {e}")
        
        if self.detailed_logging:
            self.logger.info(f"✅ 联盟实时分析完成: {len(agent_outputs)} 个智能体")
        
        return agent_outputs
    
    def _execute_analysis_layer_sequential(self,
                                         coalition: Set[str],
                                         state: Dict[str, Any],
                                         agents: Dict[str, Any],
                                         env: Optional[StockTradingEnv] = None,
                                         is_full_coalition: bool = False) -> None:
        """
        顺序执行分析层智能体 (NAA, TAA, FAA)
        执行完毕后直接在state对象上添加analyst_outputs字段

        参数:
            coalition: 联盟集合
            state: 当前状态（会被直接修改）
            agents: 智能体实例字典
            env: 交易环境（可选）
        """
        analysis_layer = ["NAA", "TAA", "FAA"]
        analysis_agents = [agent_id for agent_id in analysis_layer if agent_id in coalition]
        
        if not analysis_agents:
            if self.detailed_logging:
                self.logger.info("🔄 分析层无可执行智能体")
            state["analyst_outputs"] = {}
            return
        
        if self.detailed_logging:
            self.logger.info(f"🔄 顺序执行分析层智能体: {analysis_agents}")

        # 顺序执行分析层智能体
        analyst_outputs = {}
        for agent_id in analysis_agents:
            try:
                if agent_id not in agents:
                    raise RuntimeError(f"智能体 {agent_id} 实例不存在")

                if self.detailed_logging:
                    self.logger.info(f"🤖 执行智能体 {agent_id}...")

                # 详细日志模式：显示LLM输入
                if self.detailed_logging:
                    self._log_agent_input(agent_id, state, agents[agent_id])

                # 捕获决策前的上下文
                decision_start_time = time.time()
                
                agent_output = agents[agent_id].process(state, is_full_coalition=is_full_coalition)
                
                # 计算执行时间
                execution_time = time.time() - decision_start_time

                # 详细日志模式：显示LLM输出
                if self.detailed_logging:
                    self._log_agent_output(agent_id, agent_output)

                # 捕获决策快照（如果环境支持）
                if env and hasattr(env, 'capture_agent_decision_context'):
                    self._capture_agent_decision_snapshot(
                        env, agent_id, state, agent_output, execution_time, agents[agent_id]
                    )

                # 标记为实时分析结果
                if isinstance(agent_output, dict):
                    agent_output["source"] = "real_time_analysis"
                    agent_output["timestamp"] = state.get("current_date", "unknown")

                # 保存结果到分析层输出
                analyst_outputs[agent_id] = agent_output

            except Exception as e:
                self.logger.error(f"❌ 智能体 {agent_id} 分析失败: {e}")
                raise RuntimeError(f"智能体 {agent_id} 分析失败: {e}")

        # 直接在state对象上添加标准化的analyst_outputs字段
        state["analyst_outputs"] = analyst_outputs
        
        if self.detailed_logging:
            self.logger.info(f"✅ 分析层完成: {len(analyst_outputs)} 个智能体")
        
        # 保持向后兼容性
        if "previous_outputs" not in state:
            state["previous_outputs"] = {}
        state["previous_outputs"].update(analyst_outputs)
    
    def _execute_analysis_layer_parallel(self,
                                        coalition: Set[str],
                                        state: Dict[str, Any],
                                        agents: Dict[str, Any],
                                        env: Optional[StockTradingEnv] = None,
                                        is_full_coalition: bool = False) -> None:
        """
        并行执行分析层智能体 (NAA, TAA, FAA)
        """
        analysis_layer = ["NAA", "TAA", "FAA"]
        analysis_agents = [agent_id for agent_id in analysis_layer if agent_id in coalition]
        
        if not analysis_agents:
            self.logger.info("🔄 分析层无可执行智能体")
            state["analyst_outputs"] = {}
            return
        
        self.logger.info(f"🚀 并行执行分析层智能体: {analysis_agents}")
        
        # 准备并行调用的智能体和状态
        agents_with_states = [(agents[agent_id], state) for agent_id in analysis_agents]
        
        # 使用并行管理器执行
        if self.parallel_llm_manager is not None:
            results = self.parallel_llm_manager.call_agents_parallel(agents_with_states, is_full_coalition=is_full_coalition)
        else:
            # 如果没有并行管理器，则回退到顺序执行
            self.logger.warning("⚠️ 并行管理器不可用，回退到顺序执行")
            self._execute_analysis_layer_sequential(coalition, state, agents, env, is_full_coalition)
            return
        
        # 处理结果
        analyst_outputs = {}
        for agent_id in analysis_agents:
            if agent_id in results:
                analyst_outputs[agent_id] = results[agent_id]
            else:
                self.logger.error(f"❌ 智能体 {agent_id} 并行执行失败")
        
        # 更新状态
        state["analyst_outputs"] = analyst_outputs
        
        if self.detailed_logging:
            self.logger.info(f"✅ 分析层并行完成: {len(analyst_outputs)} 个智能体")
        
        # 保持向后兼容性
        if "previous_outputs" not in state:
            state["previous_outputs"] = {}
        state["previous_outputs"].update(analyst_outputs)
    
    def _execute_outlook_layer_sequential(self,
                                         coalition: Set[str],
                                         state: Dict[str, Any],
                                         agents: Dict[str, Any],
                                         env: Optional[StockTradingEnv] = None,
                                         is_full_coalition: bool = False) -> None:
        """
        顺序执行展望层智能体 (BOA, BeOA, NOA)
        """
        outlook_layer = ["BOA", "BeOA", "NOA"]
        outlook_agents = [agent_id for agent_id in outlook_layer if agent_id in coalition]
        
        if not outlook_agents:
            if self.detailed_logging:
                self.logger.info("🔄 展望层无可执行智能体")
            state["outlook_outputs"] = {}
            return
        
        if self.detailed_logging:
            self.logger.info(f"🔄 顺序执行展望层智能体: {outlook_agents}")
        
        outlook_outputs = {}
        for agent_id in outlook_agents:
            try:
                if agent_id not in agents:
                    raise RuntimeError(f"智能体 {agent_id} 实例不存在")
                
                if self.detailed_logging:
                    self.logger.info(f"🤖 执行展望层智能体 {agent_id}...")
                    self._log_agent_input(agent_id, state, agents[agent_id])
                
                decision_start_time = time.time()
                agent_output = agents[agent_id].process(state, is_full_coalition=is_full_coalition)
                execution_time = time.time() - decision_start_time
                
                if self.detailed_logging:
                    outlook_info = self._extract_outlook_info(agent_output)
                    self.logger.info(f"✅ 展望层智能体 {agent_id} 完成: {outlook_info} (用时: {execution_time:.2f}s)")
                
                outlook_outputs[agent_id] = agent_output
                
            except Exception as e:
                self.logger.error(f"❌ 智能体 {agent_id} 分析失败: {e}")
                raise RuntimeError(f"智能体 {agent_id} 分析失败: {e}")
        
        # 更新状态
        state["outlook_outputs"] = outlook_outputs
        
        if self.detailed_logging:
            self.logger.info(f"✅ 展望层完成: {len(outlook_outputs)} 个智能体")
        
        # 保持向后兼容性
        if "previous_outputs" not in state:
            state["previous_outputs"] = {}
        state["previous_outputs"].update(outlook_outputs)
    
    def _execute_outlook_layer_parallel(self,
                                       coalition: Set[str],
                                       state: Dict[str, Any],
                                       agents: Dict[str, Any],
                                       env: Optional[StockTradingEnv] = None,
                                       is_full_coalition: bool = False) -> None:
        """
        并行执行展望层智能体 (BOA, BeOA, NOA)
        """
        outlook_layer = ["BOA", "BeOA", "NOA"]
        outlook_agents = [agent_id for agent_id in outlook_layer if agent_id in coalition]
        
        if not outlook_agents:
            self.logger.info("🔄 展望层无可执行智能体")
            state["outlook_outputs"] = {}
            return
        
        self.logger.info(f"🚀 并行执行展望层智能体: {outlook_agents}")
        
        # 准备并行调用的智能体和状态
        agents_with_states = [(agents[agent_id], state) for agent_id in outlook_agents]
        
        # 使用并行管理器执行
        if self.parallel_llm_manager is not None:
            results = self.parallel_llm_manager.call_agents_parallel(agents_with_states, is_full_coalition=is_full_coalition)
        else:
            # 如果没有并行管理器，则回退到顺序执行
            self.logger.warning("⚠️ 并行管理器不可用，回退到顺序执行")
            self._execute_outlook_layer_sequential(coalition, state, agents, env, is_full_coalition)
            return
        
        # 处理结果
        outlook_outputs = {}
        for agent_id in outlook_agents:
            if agent_id in results:
                outlook_outputs[agent_id] = results[agent_id]
            else:
                self.logger.error(f"❌ 智能体 {agent_id} 并行执行失败")
        
        # 更新状态
        state["outlook_outputs"] = outlook_outputs
        
        if self.detailed_logging:
            self.logger.info(f"✅ 展望层并行完成: {len(outlook_outputs)} 个智能体")
        
        # 保持向后兼容性
        if "previous_outputs" not in state:
            state["previous_outputs"] = {}
        state["previous_outputs"].update(outlook_outputs)

    def _log_agent_input(self, agent_id: str, state: Dict[str, Any], agent: Any) -> None:
        """
        记录智能体的输入信息（简化版）

        参数:
            agent_id: 智能体ID
            state: 输入状态
            agent: 智能体实例
        """
        # 简化输入日志 - 只记录关键信息
        current_date = state.get('current_date', 'N/A')
        cumulative_return = state.get('cumulative_return', 0.0)
        weekly_return = state.get('weekly_return', 0.0)
        positions_count = len(state.get('positions', {}))
        
        # 根据智能体类型显示特定数据
        data_summary = []
        if agent_id == "NAA":
            news_count = self._count_news_items(state.get('news_history', {}))
            data_summary.append(f"新闻{news_count}条")
        elif agent_id == "TAA":
            price_count = self._count_price_points(state.get('price_history', {}))
            data_summary.append(f"价格{price_count}个点")
        elif agent_id == "FAA":
            fund_count = len(state.get('fundamental_data', {}))
            data_summary.append(f"基本面{fund_count}个股票")
        
        data_info = f", 数据: {'/'.join(data_summary)}" if data_summary else ""
        
        if not self.detailed_logging:
            pass  # 简洁模式下不显示输入日志
        else:
            self.logger.info(f"🤖 {agent_id} 输入: 日期={current_date}, 累计收益={cumulative_return:.4f}, 周收益={weekly_return:.4f}{data_info}")
    
    def _count_news_items(self, news_history: Dict[str, Any]) -> int:
        """统计新闻数量"""
        count = 0
        for date_news in news_history.values():
            if isinstance(date_news, dict):
                for news_list in date_news.values():
                    if isinstance(news_list, list):
                        count += len(news_list)
        return count
    
    def _count_price_points(self, price_history: Dict[str, Any]) -> int:
        """统计价格数据点数量"""
        count = 0
        for prices in price_history.values():
            if isinstance(prices, list):
                count += len(prices)
        return count

    def _extract_outlook_info(self, agent_output: Any) -> str:
        """
        从展望层智能体输出中提取关键信息用于日志显示
        
        参数:
            agent_output: 智能体输出
            
        返回:
            包含关键信息的字符串
        """
        if isinstance(agent_output, dict):
            # 提取展望层智能体的关键信息
            key_info = []
            
            # 提取信号类型和强度
            if 'outlook' in agent_output:
                outlook = agent_output['outlook']
                key_info.append(f"展望={outlook}")
            elif 'signal' in agent_output:
                signal = agent_output['signal']
                key_info.append(f"信号={signal}")
            
            # 提取信心度
            if 'confidence' in agent_output:
                confidence = agent_output['confidence']
                key_info.append(f"信心度={confidence}")
            elif 'strength' in agent_output:
                strength = agent_output['strength']
                key_info.append(f"强度={strength}")
            
            # 提取推理信息（简化版）
            if 'reasoning' in agent_output:
                reasoning = str(agent_output['reasoning'])
                # 截取前30个字符作为摘要
                reasoning_summary = reasoning[:30] + "..." if len(reasoning) > 30 else reasoning
                key_info.append(f"推理={reasoning_summary}")
            
            return ", ".join(key_info) if key_info else "已输出结果"
        else:
            return f"输出类型: {type(agent_output).__name__}"

    def _log_agent_output(self, agent_id: str, output: Any) -> None:
        """
        记录智能体的输出信息（简化版）

        参数:
            agent_id: 智能体ID
            output: 智能体输出
        """
        # 简化输出日志 - 只在详细模式下显示关键信息
        if not self.detailed_logging:
            return  # 简洁模式下不显示输出日志
        
        try:
            if isinstance(output, dict):
                # 只显示最关键的信息
                key_info = []
                
                # 对于不同的智能体显示不同的关键信息
                if agent_id == "TRA":
                    for key in ['action', 'position_size', 'confidence']:
                        if key in output:
                            key_info.append(f"{key}={output[key]}")
                elif agent_id == "NAA":
                    if 'sentiment_score' in output:
                        key_info.append(f"sentiment={output['sentiment_score']}")
                else:
                    if 'confidence' in output:
                        key_info.append(f"confidence={output['confidence']}")
                
                if key_info:
                    self.logger.info(f"🤖 {agent_id} 输出: {', '.join(key_info)}")
                else:
                    self.logger.info(f"🤖 {agent_id} 输出: {len(output)} 个字段")
            else:
                output_str = str(output)
                if len(output_str) > 50:
                    self.logger.info(f"🤖 {agent_id} 输出: {output_str[:30]}...")
                else:
                    self.logger.info(f"🤖 {agent_id} 输出: {output_str}")

        except Exception as e:
            self.logger.warning(f"{agent_id} 输出处理异常: {e}")
    
    def _get_trading_action(self,
                          agent_outputs: Dict[str, Any],
                          coalition: Set[str]) -> Dict[str, float]:
        """
        从智能体输出中提取交易动作

        参数:
            agent_outputs: 智能体输出字典
            coalition: 智能体联盟

        返回:
            交易动作字典
        """
        if "TRA" not in coalition or "TRA" not in agent_outputs:
            # 如果没有交易智能体，返回持有动作
            if self.detailed_logging:
                self.logger.info("🔍 调试: 联盟中没有TRA智能体，返回持有动作")
            return {"__HOLD__": 1.0}

        tra_output = agent_outputs["TRA"]
        if self.detailed_logging:
            self.logger.info(f"🔍 调试: TRA输出类型={type(tra_output)}, 内容={tra_output}")

        # 处理不同格式的TRA输出
        if isinstance(tra_output, dict):
            # 优先使用LLM智能体生成的trading_actions
            if "trading_actions" in tra_output:
                action = tra_output["trading_actions"]
                if self.detailed_logging:
                    self.logger.info(f"🔍 调试: 找到trading_actions字段={action}")
                return action
            elif "actions" in tra_output:
                action = tra_output["actions"]
                if self.detailed_logging:
                    self.logger.info(f"🔍 调试: 找到actions字段={action}")
                return action
            elif "action" in tra_output:
                # LLM格式的动作
                action_str = tra_output["action"].lower()
                position_size = tra_output.get("position_size", 0.5)  # 默认半仓
                if self.detailed_logging:
                    self.logger.info(f"🔍 调试: 找到action字段={action_str}, position_size={position_size}")

                if action_str == "buy":
                    return {"AAPL": position_size}
                elif action_str == "sell":
                    return {"AAPL": -position_size}
                else:  # hold
                    return {"__HOLD__": 1.0}
            else:
                if self.detailed_logging:
                    self.logger.info(f"🔍 调试: TRA输出中没有找到交易动作字段，可用字段: {list(tra_output.keys())}")
        else:
            if self.detailed_logging:
                self.logger.info(f"🔍 调试: TRA输出不是字典格式")

        # 默认持有
        if self.detailed_logging:
            self.logger.info("🔍 调试: 使用默认持有动作")
        return {"__HOLD__": 1.0}
    
    def _calculate_sharpe_ratio(self, daily_returns: List[float]) -> float:
        """
        计算夏普比率
        
        参数:
            daily_returns: 每日收益率列表
            
        返回:
            夏普比率
        """
        if not daily_returns or len(daily_returns) < 2:
            self.logger.warning(f"🚨 Sharpe计算失败: 收益率数据不足 (长度: {len(daily_returns) if daily_returns else 0})")
            return 0.0
        
        returns_array = np.array(daily_returns)

        # 详细调试信息（只在详细模式下显示）
        if self.detailed_logging:
            self.logger.info(f"📊 Sharpe计算调试:")
            self.logger.info(f"  收益率数组长度: {len(returns_array)}")
            self.logger.info(f"  收益率范围: {np.min(returns_array):.6f} ~ {np.max(returns_array):.6f}")
            self.logger.info(f"  收益率均值: {np.mean(returns_array):.6f}")
            self.logger.info(f"  收益率标准差: {np.std(returns_array):.6f}")
        
        # 计算年化收益率和波动率
        mean_return = np.mean(returns_array) * 252  # 年化收益率
        std_return = np.std(returns_array) * np.sqrt(252)  # 年化波动率
        
        self.logger.info(f"  年化收益率: {mean_return:.6f}")
        self.logger.info(f"  年化波动率: {std_return:.6f}")
        
        # 计算夏普比率
        if std_return == 0:
            self.logger.warning("🚨 年化波动率为0，无法计算Sharpe比率")
            return 0.0
        
        sharpe_ratio = (mean_return - self.risk_free_rate) / std_return
        self.logger.info(f"  最终Sharpe比率: {sharpe_ratio:.6f}")
        return sharpe_ratio
    
    def _simulate_agent_output(self,
                             agent_id: str,
                             state: Dict[str, Any],
                             previous_outputs: Dict[str, Any]) -> Dict[str, Any]:
        """
        模拟智能体输出（当真实智能体不可用时）

        参数:
            agent_id: 智能体ID
            state: 当前状态
            previous_outputs: 之前的智能体输出

        返回:
            模拟的智能体输出
        """
        # 使用参数避免未使用警告
        _ = state, previous_outputs

        if agent_id in ["BOA", "BeOA", "NOA"]:
            # 展望层智能体的模拟输出
            return {
                "outlook": "neutral",
                "confidence": 0.5,
                "reasoning": f"模拟的{agent_id}输出"
            }
        elif agent_id == "TRA":
            # 交易智能体的模拟输出
            return {
                "action": "hold",
                "confidence": 0.5,
                "reasoning": "模拟的交易决策"
            }
        else:
            return self._get_default_agent_output(agent_id)
    
    def _get_default_analysis_output(self, agent_id: str) -> Dict[str, Any]:
        """获取分析智能体的默认输出"""
        if agent_id == "NAA":
            return {"sentiment": 0.0, "summary": "无新闻数据"}
        elif agent_id == "TAA":
            return {"trend": "neutral", "indicators": {}}
        elif agent_id == "FAA":
            return {"valuation": "fair", "metrics": {}}
        else:
            return {}
    
    def _get_default_agent_output(self, agent_id: str) -> Dict[str, Any]:
        """获取智能体的默认输出"""
        return {
            "agent_id": agent_id,
            "output": "default",
            "confidence": 0.0,
            "reasoning": f"默认输出 - {agent_id}"
        }
    
    def _update_stats(self, simulation_time: float, success: bool) -> None:
        """更新模拟统计信息"""
        self._stats["total_simulations"] += 1
        self._stats["total_simulation_time"] += simulation_time
        self._stats["last_simulation"] = datetime.now()
        
        if success:
            self._stats["successful_simulations"] += 1
        else:
            self._stats["failed_simulations"] += 1
        
        # 计算平均模拟时间
        if self._stats["total_simulations"] > 0:
            self._stats["average_simulation_time"] = (
                self._stats["total_simulation_time"] / self._stats["total_simulations"]
            )
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取模拟统计信息
        
        返回:
            包含统计信息的字典
        """
        stats = self._stats.copy()
        if stats["total_simulations"] > 0:
            stats["success_rate"] = (
                stats["successful_simulations"] / stats["total_simulations"]
            ) * 100
        else:
            stats["success_rate"] = 0.0
        
        # 添加周期性评估统计
        stats["weekly_stats"] = self._weekly_stats.copy()
        return stats
    
    def set_weekly_shapley_callback(self, callback_func):
        """
        设置周末Shapley值计算回调函数

        参数:
            callback_func: 回调函数，接收(coalition, week_data, all_weekly_data)参数
        """
        self.weekly_shapley_callback = callback_func
        self.logger.info("已设置周末Shapley值计算回调函数")

    def set_current_simulation_week(self, week_number: Optional[int]) -> None:
        """
        设置当前模拟周数，用于正确显示日志

        参数:
            week_number: 当前周数，如果为None则清除设置
        """
        self.current_simulation_week = week_number
        if week_number is not None:
            self.logger.debug(f"设置当前模拟周数: {week_number}")
    
    # ===================
    # 决策快照捕获方法
    # ===================
    
    def _capture_agent_decision_snapshot(self,
                                       env: StockTradingEnv,
                                       agent_id: str,
                                       state: Dict[str, Any],
                                       agent_output: Any,
                                       execution_time: float,
                                       agent_instance: Any) -> Optional[str]:
        """
        捕获智能体决策快照
        
        参数:
            env: 交易环境实例
            agent_id: 智能体ID
            state: 环境状态
            agent_output: 智能体输出
            execution_time: 执行时间
            agent_instance: 智能体实例
            
        返回:
            快照ID，如果失败则返回None
        """
        try:
            # 解析智能体输出
            parsed_decision = "HOLD"  # 默认值
            confidence_score = 0.0
            reasoning = ""
            
            if isinstance(agent_output, dict):
                # 提取决策信息
                action = agent_output.get("action", "hold")
                parsed_decision = action.upper() if action else "HOLD"
                
                confidence_score = float(agent_output.get("confidence", 0.0))
                reasoning = str(agent_output.get("reasoning", ""))
            elif isinstance(agent_output, str):
                # 如果输出是字符串，尝试解析
                if "buy" in agent_output.lower():
                    parsed_decision = "BUY"
                elif "sell" in agent_output.lower():
                    parsed_decision = "SELL"
                else:
                    parsed_decision = "HOLD"
                reasoning = agent_output
            
            # 获取提示词（如果智能体支持）
            prompt_used = ""
            model_used = ""
            temperature = 0.0
            
            if hasattr(agent_instance, 'get_prompt_template'):
                try:
                    prompt_used = agent_instance.get_prompt_template()
                except:
                    prompt_used = f"Prompt template for {agent_id}"
            
            if hasattr(agent_instance, 'llm_interface'):
                try:
                    llm_config = agent_instance.llm_interface.get_config()
                    model_used = llm_config.get("model", "unknown")
                    temperature = llm_config.get("temperature", 0.0)
                except:
                    model_used = "unknown"
                    temperature = 0.0
            
            # 捕获决策快照
            snapshot_id = env.capture_agent_decision_context(
                agent_id=agent_id,
                prompt_used=prompt_used,
                agent_output=str(agent_output),
                parsed_decision=parsed_decision,
                confidence_score=confidence_score,
                reasoning=reasoning,
                execution_time=execution_time,
                model_used=model_used,
                temperature=temperature
            )
            
            if snapshot_id and self.detailed_logging:
                self.logger.debug(f"📸 捕获决策快照: {agent_id} -> {snapshot_id}")
                
            return snapshot_id
            
        except Exception as e:
            self.logger.warning(f"捕获决策快照失败 {agent_id}: {e}")
            return None
    
    def _update_decision_performance(self,
                                   env: StockTradingEnv,
                                   coalition: Set[str],
                                   info: Dict[str, Any]) -> None:
        """
        更新决策性能数据
        
        参数:
            env: 交易环境实例
            coalition: 智能体联盟
            info: 步骤执行后的信息
        """
        if not env or not hasattr(env, 'update_decision_performance'):
            return
            
        try:
            # 获取当前净值
            current_net_worth = info.get("net_worth", 0.0)
            daily_return = info.get("daily_return", 0.0)
            
            # 计算市场变动（简化处理）
            market_movement = {}
            if hasattr(env, 'stocks') and env.stocks:
                # 使用日收益率作为市场变动的代理
                for stock in env.stocks:
                    market_movement[stock] = daily_return
            
            # 更新联盟中每个智能体的决策性能
            for agent_id in coalition:
                success = env.update_decision_performance(
                    agent_id=agent_id,
                    post_decision_net_worth=current_net_worth,
                    market_movement=market_movement
                )
                
                if success and self.detailed_logging:
                    self.logger.debug(f"📊 更新决策性能: {agent_id}")
                    
        except Exception as e:
            self.logger.warning(f"更新决策性能失败: {e}")
    
    def get_decision_snapshots(self, env: Optional[StockTradingEnv] = None) -> Dict[str, Any]:
        """
        获取决策快照统计信息
        
        参数:
            env: 交易环境实例
            
        返回:
            快照统计信息
        """
        if not env or not hasattr(env, 'get_snapshot_statistics'):
            return {"enabled": False, "message": "Decision snapshots not available"}
            
        try:
            return env.get_snapshot_statistics()
        except Exception as e:
            return {"enabled": False, "error": str(e)}
    
    def export_decision_snapshots(self, env: Optional[StockTradingEnv] = None, file_path: Optional[str] = None) -> bool:
        """
        导出决策快照到文件
        
        参数:
            env: 交易环境实例
            file_path: 导出文件路径
            
        返回:
            是否导出成功
        """
        if not env or not hasattr(env, 'export_decision_snapshots'):
            return False
            
        if not file_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_path = f"decision_snapshots_{timestamp}.json"
            
        try:
            return env.export_decision_snapshots(file_path)
        except Exception as e:
            self.logger.error(f"导出决策快照失败: {e}")
            return False
    
    def _is_week_end(self, day: int) -> bool:
        """
        判断是否为一周的最后一个交易日
        
        参数:
            day: 当前交易日（从0开始）
            
        返回:
            是否为周末
        """
        # 计算当前周内的交易日（考虑周数）
        current_week = day // self.trading_days_per_week
        day_in_week = day % self.trading_days_per_week
        
        # 判断是否为当前周的最后一个交易日
        is_week_end = day_in_week == self.trading_days_per_week - 1
        
        if is_week_end and self.detailed_logging:
            self.logger.info(f"📅 检测到周末: 交易日={day}, 周数={current_week+1}, 周内交易日={day_in_week+1}/{self.trading_days_per_week}")
        
        return is_week_end
    
    def _process_weekly_data(self,
                           week_returns: List[float],
                           coalition: Set[str],
                           end_day: int,
                           week_number: Optional[int] = None) -> Dict[str, Any]:
        """
        处理一周的交易数据

        参数:
            week_returns: 一周的每日收益率
            coalition: 当前联盟
            end_day: 周结束日
            week_number: 明确指定的周数（可选）

        返回:
            周级数据汇总
        """
        # 计算周数：如果明确指定则使用指定值，否则基于end_day计算
        if week_number is not None:
            calculated_week_number = week_number
        else:
            # 修正周数计算：基于完整周数计算
            calculated_week_number = (end_day // self.trading_days_per_week) + 1

        if not week_returns:
            return {
                "week_number": calculated_week_number,
                "coalition": list(coalition),
                "trading_days": 0,
                "total_return": 0.0,
                "average_daily_return": 0.0,
                "volatility": 0.0,
                "sharpe_ratio": 0.0,
                "end_day": end_day
            }

        week_returns_array = np.array(week_returns)
        total_return = np.sum(week_returns_array)
        avg_return = np.mean(week_returns_array)
        volatility = np.std(week_returns_array) if len(week_returns) > 1 else 0.0

        # 计算周级夏普比率
        if volatility > 0:
            weekly_sharpe = (avg_return * 252 - self.risk_free_rate) / (volatility * np.sqrt(252))
        else:
            weekly_sharpe = 0.0

        return {
            "week_number": calculated_week_number,
            "coalition": list(coalition),
            "trading_days": len(week_returns),
            "total_return": total_return,
            "average_daily_return": avg_return,
            "volatility": volatility,
            "sharpe_ratio": weekly_sharpe,
            "daily_returns": week_returns.copy(),
            "end_day": end_day,
            "timestamp": datetime.now().isoformat()
        }
    
    def _trigger_weekly_shapley_calculation(self, 
                                          coalition: Set[str], 
                                          week_data: Dict[str, Any], 
                                          all_weekly_data: List[Dict[str, Any]]) -> None:
        """
        触发周末Shapley值计算
        
        参数:
            coalition: 当前联盟
            week_data: 当前周数据
            all_weekly_data: 所有周数据列表
        """
        try:
            
            # 更新统计信息
            self._weekly_stats["completed_weeks"] += 1
            self._weekly_stats["last_weekly_evaluation"] = datetime.now()
            
            # 如果设置了回调函数，调用它
            if self.weekly_shapley_callback:
                try:
                    self.weekly_shapley_callback(coalition, week_data, all_weekly_data)
                    self._weekly_stats["shapley_calculations"] += 1
                except Exception as e:
                    self.logger.error(f"周末Shapley值计算回调执行失败: {e}")
            else:
                # 默认行为：记录周级性能分析
                self._log_weekly_performance_analysis(coalition, week_data, all_weekly_data)
                self._weekly_stats["shapley_calculations"] += 1
            
        except Exception as e:
            self.logger.error(f"触发周末Shapley值计算失败: {e}")
    
    def _log_weekly_performance_analysis(self, 
                                       coalition: Set[str], 
                                       week_data: Dict[str, Any], 
                                       all_weekly_data: List[Dict[str, Any]]) -> None:
        """
        记录周级性能分析日志
        
        参数:
            coalition: 联盟
            week_data: 当前周数据
            all_weekly_data: 所有周数据
        """
        week_num = week_data["week_number"]
        sharpe = week_data["sharpe_ratio"]
        total_return = week_data["total_return"]
        
        self.logger.info("=" * 60)
        self.logger.info(f"第 {week_num} 周性能分析 - 联盟: {coalition}")
        self.logger.info(f"周总收益率: {total_return:.4f}")
        self.logger.info(f"周夏普比率: {sharpe:.4f}")
        self.logger.info(f"交易天数: {week_data['trading_days']}")
        self.logger.info("=" * 60)
    
    def _persist_final_daily_states(self, env, daily_returns: List[float]) -> None:
        """
        持久化模拟结束时的每日状态数据
        
        Args:
            env: 交易环境实例
            daily_returns: 每日收益率列表
        """
        if self.daily_state_manager is None:
            return
        
        try:
            # 从环境中获取最终状态
            if hasattr(env, 'get_current_state'):
                current_state = env.get_current_state()
                current_date = current_state.get("current_date", "unknown")
                
                # 确保状态包含必要的字段
                if "analyst_outputs" not in current_state:
                    current_state["analyst_outputs"] = {}
                if "outlook_outputs" not in current_state:
                    current_state["outlook_outputs"] = {}
                if "trading_outputs" not in current_state:
                    current_state["trading_outputs"] = {}
                
                # 添加当前收益率信息
                if daily_returns:
                    current_state["previous_day_return"] = daily_returns[-1]
                
                # 保存到数据库
                success = self.daily_state_manager.save_daily_state(current_date, current_state)
                
                if success:
                    analyst_count = len(current_state.get("analyst_outputs", {}))
                    outlook_count = len(current_state.get("outlook_outputs", {}))
                    trading_count = len(current_state.get("trading_outputs", {}))
                    
                    self.logger.debug(
                        f"Final daily state persisted: {current_date} "
                        f"(A:{analyst_count}, O:{outlook_count}, T:{trading_count})"
                    )
                else:
                    self.logger.warning(f"Failed to persist final daily state for {current_date}")
            else:
                self.logger.debug("Environment does not support state retrieval, skipping persistence")
                
        except Exception as e:
            # 持久化失败不应影响主流程
            self.logger.warning(f"Exception during final daily state persistence: {str(e)}")
