# Implementation Plan

## 任务概述

将巨大的`ContributionAssessor`类重构为低耦合、高聚合的模块化架构。采用渐进式重构策略，确保在整个过程中保持系统功能的完整性和向后兼容性。

## 实现任务

- [ ] 1. 创建基础设施层组件
  - 创建事件总线、配置管理器、服务注册表等基础设施组件
  - 实现依赖注入容器和服务发现机制
  - 建立统一的错误处理和日志记录框架
  - _Requirements: 5.1, 5.2, 7.1, 7.2, 8.1_

- [x] 1.1 实现事件总线系统
  - 创建`IEventBus`接口和`EventBus`实现类
  - 实现事件发布、订阅和取消订阅机制
  - 添加事件过滤和异步处理支持
  - 编写事件总线的单元测试
  - _Requirements: 5.1, 5.2_

- [x] 1.2 创建配置管理器
  - 实现`IConfigurationManager`接口和`ConfigurationManager`类
  - 支持多层级配置和环境变量覆盖
  - 添加配置验证和类型转换功能
  - 创建配置数据模型类（AssessmentConfig, OPROConfig）
  - _Requirements: 8.1, 8.2, 8.3_

- [x] 1.3 建立服务注册表和依赖注入
  - 实现`ServiceRegistry`类管理服务实例
  - 支持单例和工厂模式的服务创建
  - 实现循环依赖检测和解决机制
  - 添加服务生命周期管理功能
  - _Requirements: 5.1, 5.2, 5.3_

- [x] 1.4 创建统一错误处理框架
  - 定义异常层次结构（AssessmentError及其子类）
  - 实现`ErrorHandler`类统一处理错误
  - 集成错误处理与事件总线和日志系统
  - 创建错误恢复和重试机制
  - _Requirements: 7.2, 7.4_

- [x] 2. 定义数据传输对象和接口
  - 创建所有DTO类（AssessmentRequest, AssessmentResult等）
  - 定义服务接口（ICoalitionService, ISimulationService等）
  - 实现数据验证和序列化功能
  - 建立接口契约和文档
  - _Requirements: 2.3, 5.2, 6.2_

- [x] 2.1 创建核心数据传输对象
  - 实现`AssessmentRequest`和`AssessmentResult`类
  - 创建各阶段结果DTO（CoalitionResult, SimulationResult等）
  - 添加数据验证装饰器和类型检查
  - 实现DTO的序列化和反序列化方法
  - _Requirements: 2.3, 6.3_

- [x] 2.2 定义服务接口契约
  - 创建所有服务的抽象基类接口
  - 定义方法签名、参数和返回值规范
  - 添加接口文档和使用示例
  - 实现接口兼容性检查工具
  - _Requirements: 5.2, 6.2_

- [x] 2.3 实现状态管理服务接口
  - 创建`IStateManager`接口和实现类
  - 设计状态数据结构和更新机制
  - 集成事件发布功能用于状态变更通知
  - 添加状态持久化和恢复功能
  - _Requirements: 4.1, 4.2, 4.3_

- [x] 3. 实现联盟生成服务
  - 将原有的联盟生成逻辑提取到独立的CoalitionService中
  - 实现ICoalitionService接口
  - 保持原有功能的完整性
  - 添加服务级别的错误处理和日志记录
  - _Requirements: 1.1, 1.2, 2.1, 2.2_

- [x] 3.1 创建CoalitionService核心实现
  - 从ContributionAssessor中提取`_run_coalition_generation_phase`方法
  - 重构为CoalitionService类的`generate_coalitions`方法
  - 实现依赖注入的构造函数
  - 保持原有的联盟生成和剪枝逻辑不变
  - _Requirements: 1.1, 1.3_

- [x] 3.2 集成联盟服务与基础设施
  - 添加事件发布功能（联盟生成开始、完成、失败）
  - 集成统一的错误处理和日志记录
  - 实现服务配置和参数注入
  - 添加性能监控和统计收集
  - _Requirements: 2.2, 7.1, 7.3_

- [x] 3.3 编写联盟服务单元测试
  - 创建CoalitionService的完整测试套件
  - 使用Mock对象隔离外部依赖
  - 测试各种边界条件和错误场景
  - 验证与原有实现的功能一致性
  - _Requirements: 1.4_

- [x] 4. 实现交易模拟服务
  - 将交易模拟逻辑提取到独立的SimulationService中
  - 实现并发执行管理和资源控制
  - 保持分阶段模拟的逻辑不变
  - 集成详细日志模式切换功能
  - _Requirements: 1.1, 1.2, 2.1, 2.2_

- [x] 4.1 创建SimulationService核心实现
  - 提取`_run_trading_simulation_phase`相关方法
  - 重构为SimulationService的`run_simulations`方法
  - 保持分阶段模拟逻辑（完整联盟详细日志 + 子集联盟简洁日志）
  - 实现智能联盟选择算法
  - _Requirements: 1.1, 1.3_

- [x] 4.2 实现并发执行器
  - 创建`IConcurrentExecutor`接口和实现类
  - 提取并发模拟相关方法到独立组件
  - 实现线程安全的结果收集和统计
  - 添加并发任务的监控和控制功能
  - _Requirements: 2.1, 2.2, 7.3_

- [x] 4.3 集成日志模式管理
  - 实现详细/简洁日志模式的动态切换
  - 集成子集日志缓存管理器
  - 添加模拟进度跟踪和报告功能
  - 优化日志输出的性能和可读性
  - _Requirements: 7.1, 7.4_

- [x] 4.4 编写模拟服务测试
  - 创建SimulationService的测试套件
  - 测试串行和并发执行模式
  - 验证日志模式切换功能
  - 测试资源限制和错误恢复机制
  - _Requirements: 1.4_

- [x] 5. 实现Shapley值计算服务
  - 将周期性Shapley值计算逻辑提取到独立服务
  - 实现多种计算模式（预计算结果汇总、实时计算、降级模式）
  - 集成交易日历和时间管理功能
  - 保持计算精度和结果格式不变
  - _Requirements: 1.1, 1.2, 2.1, 2.2_

- [x] 5.1 创建ShapleyService核心实现
  - 提取`_run_periodic_shapley_calculation_phase`相关方法
  - 重构为ShapleyService的`calculate_periodic_shapley`方法
  - 保持三种计算模式的逻辑不变
  - 实现结果汇总和格式化功能
  - _Requirements: 1.1, 1.3_

- [x] 5.2 集成交易日历管理
  - 创建`ITradingCalendar`接口抽象交易日计算
  - 集成实际交易日数据库查询功能
  - 实现周期划分和时间窗口管理
  - 添加节假日和特殊日期处理
  - _Requirements: 2.1, 2.2_

- [x] 5.3 实现结果存储和序列化
  - 集成周期性结果的文件保存功能
  - 实现JSON序列化的frozenset键处理
  - 添加结果压缩和归档管理
  - 创建结果查询和检索接口
  - _Requirements: 7.3_

- [x] 5.4 编写Shapley服务测试
  - 创建ShapleyService的完整测试套件
  - 测试各种计算模式和边界条件
  - 验证时间窗口划分的准确性
  - 测试结果存储和序列化功能
  - _Requirements: 1.4_

- [x] 6. 实现OPRO优化服务
  - 将OPRO相关功能提取到独立的OPROService中
  - 实现可选启用/禁用的服务模式
  - 保持与历史得分管理器的集成
  - 添加优化结果的A/B测试支持
  - _Requirements: 1.1, 1.2, 3.1, 3.2, 3.3_

- [x] 6.1 创建OPROService核心实现
  - 提取`_run_opro_optimization_phase`相关方法
  - 重构为OPROService的`optimize_agents`方法
  - 实现服务的启用/禁用状态管理
  - 保持与现有OPRO组件的兼容性
  - _Requirements: 3.1, 3.2, 1.1_

- [x] 6.2 集成历史得分管理
  - 重构历史得分更新和查询逻辑
  - 实现智能体性能趋势分析
  - 添加优化建议生成功能
  - 集成A/B测试结果比较机制
  - _Requirements: 3.3, 3.4_

- [x] 6.3 实现提示词模板管理
  - 提取提示词获取和更新相关方法
  - 实现固定模板和可优化部分的分离
  - 添加提示词版本管理和回滚功能
  - 集成智能体状态快照机制
  - _Requirements: 3.1, 3.2_

- [x] 6.4 编写OPRO服务测试
  - 创建OPROService的测试套件
  - 测试启用/禁用状态切换
  - 验证优化结果的应用和回滚
  - 测试与历史得分管理器的集成
  - _Requirements: 1.4, 3.4_

- [x] 7. 创建阶段协调器
  - 实现PhaseCoordinator管理四个阶段的执行流程
  - 建立阶段间的数据传递和错误处理机制
  - 集成事件发布和状态管理功能
  - 实现执行统计和性能监控
  - _Requirements: 2.1, 2.2, 2.3, 2.4_

- [x] 7.1 实现PhaseCoordinator核心逻辑
  - 创建`IPhaseCoordinator`接口和实现类
  - 实现四阶段的顺序执行和数据传递
  - 添加阶段级别的错误处理和恢复
  - 集成执行时间统计和性能监控
  - _Requirements: 2.1, 2.2_

- [x] 7.2 集成服务依赖注入
  - 通过构造函数注入所有服务依赖
  - 实现服务的延迟初始化和生命周期管理
  - 添加服务健康检查和故障转移
  - 集成配置驱动的服务组合
  - _Requirements: 5.1, 5.2, 8.4_

- [x] 7.3 实现工作流事件发布
  - 在每个阶段开始、完成、失败时发布事件
  - 集成详细的执行上下文和元数据
  - 实现事件的异步处理和批量发送
  - 添加事件过滤和订阅管理功能
  - _Requirements: 2.3, 4.2, 7.1_

- [x] 7.4 编写协调器测试
  - 创建PhaseCoordinator的集成测试
  - 测试完整的四阶段执行流程
  - 验证错误处理和恢复机制
  - 测试事件发布和状态更新功能
  - _Requirements: 1.4, 2.4_

- [x] 8. 重构ContributionAssessor为外观模式
  - 保持所有现有公共方法的接口不变
  - 将实现委托给PhaseCoordinator
  - 实现向后兼容的参数转换和结果格式化
  - 添加新旧实现的切换开关
  - _Requirements: 6.1, 6.2, 6.3, 6.4_

- [x] 8.1 实现外观模式接口
  - 保持ContributionAssessor类的所有公共方法
  - 将`run`方法的实现委托给PhaseCoordinator
  - 实现参数验证和DTO转换逻辑
  - 保持返回值格式的完全兼容性
  - _Requirements: 6.1, 6.2_

- [x] 8.2 集成服务工厂和注册表
  - 在ContributionAssessor初始化时创建服务实例
  - 实现服务的配置驱动创建和注入
  - 添加服务创建失败的降级处理
  - 集成新旧实现的功能开关
  - _Requirements: 5.1, 8.4_

- [x] 8.3 保持统计和状态接口
  - 委托所有统计方法到StateManager
  - 保持`get_stats`、`get_module_stats`等方法的兼容性
  - 实现并发统计和配置方法的代理
  - 添加状态数据的格式转换和兼容性处理
  - _Requirements: 6.3, 4.4_

- [x] 8.4 实现兼容性测试
  - 创建向后兼容性的完整测试套件
  - 对比新旧实现的输出结果一致性
  - 测试所有公共方法的接口兼容性
  - 验证异常处理和错误消息的一致性
  - _Requirements: 6.4, 1.4_

- [x] 9. 创建服务工厂和启动器
  - 实现ServiceFactory管理服务实例的创建
  - 创建ApplicationBootstrapper处理系统初始化
  - 集成配置加载和验证功能
  - 实现优雅的启动和关闭流程
  - _Requirements: 5.1, 8.1, 8.2_

- [x] 9.1 实现服务工厂
  - 创建`ServiceFactory`类管理服务创建逻辑
  - 实现基于配置的服务实例化
  - 添加服务依赖关系的自动解析
  - 集成服务的单例和原型模式支持
  - _Requirements: 5.1, 5.3_

- [x] 9.2 创建应用启动器
  - 实现`ApplicationBootstrapper`处理系统初始化
  - 集成配置加载、验证和服务注册
  - 添加启动失败的错误处理和诊断
  - 实现优雅关闭和资源清理机制
  - _Requirements: 8.1, 8.2_

- [x] 9.3 集成配置验证
  - 实现配置文件的结构和类型验证
  - 添加必需配置项的检查和默认值设置
  - 集成环境变量和命令行参数覆盖
  - 创建配置错误的详细诊断信息
  - _Requirements: 8.3, 7.2_

- [x] 9.4 编写启动器测试
  - 创建ServiceFactory和ApplicationBootstrapper的测试
  - 测试各种配置场景和错误条件
  - 验证服务创建和依赖注入的正确性
  - 测试启动和关闭流程的健壮性
  - _Requirements: 1.4_

- [x] 10. 性能优化和监控
  - 实现性能监控和指标收集
  - 优化内存使用和垃圾回收
  - 添加缓存机制和资源池管理
  - 创建性能基准测试和回归检测
  - _Requirements: 7.3, 1.1_

- [x] 10.1 实现性能监控系统
  - 创建性能指标收集和报告机制
  - 集成方法级别的执行时间统计
  - 添加内存使用和资源消耗监控
  - 实现性能告警和异常检测
  - _Requirements: 7.3_

- [x] 10.2 优化内存和资源使用
  - 分析和优化大对象的内存占用
  - 实现对象池和缓存机制
  - 优化并发执行的资源管理
  - 添加内存泄漏检测和预防
  - _Requirements: 1.1_

- [x] 10.3 创建性能基准测试
  - 建立重构前后的性能基准对比
  - 创建自动化的性能回归测试
  - 实现负载测试和压力测试场景
  - 添加性能报告和趋势分析
  - _Requirements: 1.1, 1.4_

- [x] 11. 文档和迁移指南
  - 创建架构文档和API参考
  - 编写迁移指南和最佳实践
  - 更新现有文档和代码注释
  - 创建故障排除和维护指南
  - _Requirements: 8.1, 8.2_

- [x] 11.1 编写架构文档
  - 创建系统架构和设计决策文档
  - 编写各服务的API参考和使用示例
  - 添加配置选项和参数说明
  - 创建故障排除和调试指南
  - _Requirements: 8.1_

- [x] 11.2 创建迁移指南
  - 编写从旧架构到新架构的迁移步骤
  - 提供配置迁移和兼容性检查工具
  - 创建常见问题和解决方案文档
  - 添加性能调优和最佳实践建议
  - _Requirements: 8.2_

- [x] 11.3 更新代码文档
  - 更新所有类和方法的文档字符串
  - 添加类型注解和接口契约说明
  - 创建代码示例和使用模式文档
  - 集成自动化文档生成和更新流程
  - _Requirements: 8.1_

- [ ] 12. 集成测试和验收测试
  - 创建端到端的集成测试套件
  - 实现与现有系统的兼容性测试
  - 执行性能和稳定性验收测试
  - 进行用户验收测试和反馈收集
  - _Requirements: 1.4, 6.4_

- [ ] 12.1 创建端到端集成测试
  - 实现完整工作流的自动化测试
  - 测试各种配置和环境组合
  - 验证错误处理和恢复机制
  - 测试并发执行和资源限制场景
  - _Requirements: 1.4_

- [ ] 12.2 执行兼容性验证
  - 对比新旧实现的功能一致性
  - 测试现有调用代码的兼容性
  - 验证配置文件和参数的兼容性
  - 检查异常处理和错误消息的一致性
  - _Requirements: 6.4_

- [ ] 12.3 进行性能和稳定性测试
  - 执行长时间运行的稳定性测试
  - 进行内存泄漏和资源泄漏检测
  - 测试高并发和高负载场景
  - 验证性能指标和资源使用优化
  - _Requirements: 1.1, 7.3_

- [ ] 12.4 收集用户反馈和验收
  - 与现有用户进行迁移测试
  - 收集性能和易用性反馈
  - 验证新功能和改进的有效性
  - 根据反馈进行最终调整和优化
  - _Requirements: 6.4_