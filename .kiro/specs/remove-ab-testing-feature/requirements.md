# 删除A/B测试功能需求文档

## 介绍

本文档定义了从OPRO系统中删除A/B测试功能的需求。目标是在不影响系统核心功能和其他运行模式的前提下，清理和简化代码结构，移除A/B测试相关的代码和依赖。

## 需求

### 需求1：移除A/B测试运行模式

**用户故事：** 作为系统维护者，我希望移除A/B测试运行模式，以简化系统架构和减少代码复杂度。

#### 验收标准

1. WHEN 用户运行 `python run_opro_system.py --mode ab-testing` THEN 系统 SHALL 返回错误提示"不支持的运行模式"
2. WHEN 用户查看帮助信息 `python run_opro_system.py --help` THEN 系统 SHALL 不显示ab-testing模式选项
3. WHEN 用户使用其他运行模式 THEN 系统 SHALL 正常工作不受影响

### 需求2：清理A/B测试相关代码

**用户故事：** 作为开发者，我希望移除所有A/B测试相关的代码，以保持代码库的整洁性。

#### 验收标准

1. WHEN 检查run_opro_system.py文件 THEN 系统 SHALL 不包含任何A/B测试相关的函数和参数
2. WHEN 检查contribution_assessment模块 THEN 系统 SHALL 移除ab_testing_manager.py文件
3. WHEN 检查assessor.py THEN 系统 SHALL 移除所有A/B测试相关的方法和导入
4. WHEN 运行代码静态检查 THEN 系统 SHALL 不存在未使用的A/B测试相关导入

### 需求3：保持系统功能完整性

**用户故事：** 作为用户，我希望在移除A/B测试功能后，其他所有功能仍然正常工作。

#### 验收标准

1. WHEN 用户运行evaluation模式 THEN 系统 SHALL 正常执行标准评估
2. WHEN 用户运行optimization模式 THEN 系统 SHALL 正常执行OPRO优化
3. WHEN 用户运行weekly模式 THEN 系统 SHALL 正常执行周期性优化
4. WHEN 用户运行integrated模式 THEN 系统 SHALL 正常执行集成模式
5. WHEN 用户运行dashboard模式 THEN 系统 SHALL 正常显示仪表板数据

### 需求4：更新文档和配置

**用户故事：** 作为用户，我希望文档和配置文件反映最新的功能状态。

#### 验收标准

1. WHEN 查看run_opro_system.py的文档字符串 THEN 系统 SHALL 不包含A/B测试相关的使用示例
2. WHEN 查看配置文件 THEN 系统 SHALL 移除A/B测试相关的配置项
3. WHEN 查看README或其他文档 THEN 系统 SHALL 更新功能列表，移除A/B测试功能描述

### 需求5：清理测试和日志

**用户故事：** 作为系统维护者，我希望清理与A/B测试相关的测试文件和日志引用。

#### 验收标准

1. WHEN 检查测试文件 THEN 系统 SHALL 移除所有A/B测试相关的测试用例
2. WHEN 检查日志配置 THEN 系统 SHALL 移除A/B测试相关的日志记录器
3. WHEN 运行系统 THEN 系统 SHALL 不产生A/B测试相关的日志输出

### 需求6：向后兼容性处理

**用户故事：** 作为用户，我希望在尝试使用已删除的A/B测试功能时，能够得到清晰的错误提示。

#### 验收标准

1. WHEN 用户使用--max-iterations参数 THEN 系统 SHALL 显示警告"该参数已废弃，A/B测试功能已移除"
2. WHEN 用户在配置文件中包含A/B测试配置 THEN 系统 SHALL 忽略这些配置并记录警告日志
3. WHEN 用户调用已删除的A/B测试方法 THEN 系统 SHALL 抛出AttributeError并提供清晰的错误信息