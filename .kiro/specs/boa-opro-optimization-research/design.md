# 设计文档

## 概述

本设计文档详细分析了 OPRO 优化阶段中 BOA (BullishOutlookAgent) 智能体提示词优化失败的技术问题。通过深入研究代码架构、数据流和组件交互，我们将系统性地分析失败的根本原因，并为后续修复工作提供技术指导。

## 架构分析

### 系统组件架构

```mermaid
graph TB
    subgraph "智能体层级"
        BOA[BOA - BullishOutlookAgent]
        BaseAgent[BaseAgent]
        OPROBaseAgent[OPROBaseAgent]
    end
    
    subgraph "OPRO优化系统"
        OPROOptimizer[OPRO优化器]
        HistoricalScoreManager[历史得分管理器]
        FailureCaseMiner[失败案例挖掘器]
    end
    
    subgraph "评估系统"
        ContributionAssessor[贡献度评估器]
        ShapleyCalculator[Shapley值计算器]
    end
    
    BOA --> BaseAgent
    BaseAgent -.-> OPROBaseAgent
    OPROOptimizer --> HistoricalScoreManager
    OPROOptimizer --> FailureCaseMiner
    ContributionAssessor --> OPROOptimizer
    ContributionAssessor --> ShapleyCalculator
```

### 继承结构问题分析

**当前继承关系：**
- `BOA` → `BaseAgent`
- `OPROBaseAgent` → `BaseAgent`

**问题识别：**
1. BOA 没有继承 OPROBaseAgent，缺少 OPRO 优化支持
2. BaseAgent 不提供动态提示词管理功能
3. 缺少提示词版本控制和性能跟踪机制

## 组件和接口

### 核心接口分析

#### 1. 智能体接口层

**BaseAgent 接口：**
```python
class BaseAgent(ABC):
    def get_prompt_template(self) -> str
    def process(self, state: Dict[str, Any]) -> Dict[str, Any]
    def format_state_for_llm(self, state: Dict[str, Any]) -> str
```

**OPROBaseAgent 扩展接口：**
```python
class OPROBaseAgent(BaseAgent):
    def update_prompt(self, new_prompt: str) -> bool
    def optimize_with_opro(self, force: bool = False) -> Dict[str, Any]
    def get_current_prompt(self) -> str
    def record_performance(self, score: float) -> None
```

**BOA 当前实现缺陷：**
- 缺少 `update_prompt()` 方法
- 缺少 `optimize_with_opro()` 方法
- 无法记录性能数据
- 不支持动态提示词更新

#### 2. OPRO优化器接口

**关键方法分析：**
```python
def optimize_agent_prompt(self, agent_id: str, current_prompt: str) -> Dict[str, Any]:
    # 1. 获取历史数据 - 依赖 historical_score_manager
    # 2. 获取失败案例 - 依赖 failure_case_miner  
    # 3. 生成候选提示词 - 依赖 LLM接口
    # 4. 评估候选提示词 - 依赖智能体支持动态更新
```

**数据依赖关系：**
- `historical_score_manager.get_agent_optimization_history()`
- `failure_case_miner.get_failure_snapshots()`
- `assessor.get_complete_prompt_template_for_agent()`

### 数据流分析

#### 1. 失败案例数据获取流程

```mermaid
sequenceDiagram
    participant OPRO as OPRO优化器
    participant HSM as 历史得分管理器
    participant FCM as 失败案例挖掘器
    participant DB as 数据库
    
    OPRO->>HSM: get_agent_optimization_history(BOA)
    HSM->>DB: 查询优化历史
    DB-->>HSM: 返回历史数据
    HSM-->>OPRO: 历史数据 (可能为空)
    
    OPRO->>FCM: get_failure_snapshots(BOA)
    FCM->>DB: 查询失败案例
    DB-->>FCM: 返回失败案例
    FCM-->>OPRO: 失败案例数据
    
    Note over OPRO: 如果数据不足，优化失败
```

#### 2. 提示词更新流程

```mermaid
sequenceDiagram
    participant OPRO as OPRO优化器
    participant BOA as BOA智能体
    participant LLM as LLM接口
    
    OPRO->>LLM: 生成候选提示词
    LLM-->>OPRO: 候选提示词列表
    OPRO->>OPRO: 评估候选提示词
    OPRO->>BOA: update_prompt(new_prompt)
    BOA-->>OPRO: 方法不存在错误
    
    Note over OPRO: 提示词更新失败
```

## 数据模型

### 1. 智能体状态模型

```python
@dataclass
class AgentState:
    agent_id: str
    current_prompt: str
    prompt_version: str
    performance_history: List[float]
    optimization_history: List[Dict]
    supports_opro: bool
    last_optimization_time: Optional[datetime]
```

### 2. OPRO优化结果模型

```python
@dataclass
class OPROOptimizationResult:
    success: bool
    agent_id: str
    original_prompt: str
    optimized_prompt: Optional[str]
    estimated_improvement: float
    failure_reason: Optional[str]
    candidates_generated: int
    candidates_evaluated: int
```

### 3. 失败案例数据模型

```python
@dataclass
class FailureCase:
    case_id: str
    agent_id: str
    trade_date: str
    market_context: Dict[str, Any]
    decision_made: str
    optimal_decision: str
    opportunity_cost: float
    failure_category: str
    failure_severity: float
```

## 错误处理

### 1. 继承结构错误处理

**问题类型：**
- `AttributeError`: BOA 对象没有 `update_prompt` 方法
- `TypeError`: 智能体不支持动态提示词更新

**错误处理策略：**
```python
def check_opro_compatibility(agent) -> bool:
    """检查智能体是否支持OPRO优化"""
    required_methods = ['update_prompt', 'get_current_prompt', 'record_performance']
    return all(hasattr(agent, method) for method in required_methods)

def safe_optimize_agent(agent_id: str, agent) -> Dict[str, Any]:
    """安全的智能体优化"""
    if not check_opro_compatibility(agent):
        return {
            "success": False,
            "error": f"智能体 {agent_id} 不支持OPRO优化",
            "required_inheritance": "OPROBaseAgent"
        }
    # 继续优化流程...
```

### 2. 数据缺失错误处理

**数据源验证：**
```python
def validate_optimization_data(agent_id: str) -> Dict[str, Any]:
    """验证优化所需数据"""
    validation_result = {
        "has_historical_data": False,
        "has_failure_cases": False,
        "data_quality_score": 0.0,
        "missing_components": []
    }
    
    # 检查历史得分数据
    history = historical_score_manager.get_agent_optimization_history(agent_id)
    validation_result["has_historical_data"] = len(history) >= 2
    
    # 检查失败案例数据
    failures = failure_case_miner.get_failure_snapshots(agent_id)
    validation_result["has_failure_cases"] = len(failures) > 0
    
    return validation_result
```

### 3. 验证标准错误处理

**候选提示词验证流程：**
```python
def multi_stage_validation(candidate: str) -> Dict[str, Any]:
    """多阶段验证候选提示词"""
    stages = [
        ("basic_format", validate_basic_format),
        ("hardcoded_output", check_hardcoded_output),
        ("semantic_type", validate_semantic_type),
        ("structure_completeness", validate_structure)
    ]
    
    for stage_name, validator in stages:
        if not validator(candidate):
            return {
                "passed": False,
                "failed_stage": stage_name,
                "candidate_preview": candidate[:100]
            }
    
    return {"passed": True, "stage_results": "all_passed"}
```

## 测试策略

### 1. 单元测试策略

**智能体继承测试：**
```python
def test_boa_opro_compatibility():
    """测试BOA是否支持OPRO优化"""
    boa = BullishOutlookAgent()
    
    # 测试必需方法存在性
    assert hasattr(boa, 'update_prompt'), "BOA缺少update_prompt方法"
    assert hasattr(boa, 'get_current_prompt'), "BOA缺少get_current_prompt方法"
    assert hasattr(boa, 'record_performance'), "BOA缺少record_performance方法"
```

**数据获取测试：**
```python
def test_historical_data_availability():
    """测试历史数据可用性"""
    hsm = HistoricalScoreManager()
    history = hsm.get_agent_optimization_history("BOA")
    
    assert len(history) >= 2, "BOA历史数据不足"
    assert all('score' in item for item in history), "历史数据格式不正确"
```

### 2. 集成测试策略

**OPRO优化流程测试：**
```python
def test_opro_optimization_flow():
    """测试完整OPRO优化流程"""
    # 1. 准备测试数据
    setup_test_historical_data("BOA")
    setup_test_failure_cases("BOA")
    
    # 2. 创建支持OPRO的BOA实例
    boa = create_opro_compatible_boa()
    
    # 3. 执行优化
    result = opro_optimizer.optimize_agent_prompt("BOA", boa.get_prompt_template())
    
    # 4. 验证结果
    assert result["success"], f"优化失败: {result.get('error')}"
    assert "optimized_prompt" in result, "缺少优化后的提示词"
```

### 3. 性能测试策略

**候选生成性能测试：**
```python
def test_candidate_generation_performance():
    """测试候选提示词生成性能"""
    start_time = time.time()
    
    candidates = opro_optimizer._generate_prompt_candidates(
        meta_prompt="test_prompt", 
        k=8, 
        temperature=0.7
    )
    
    generation_time = time.time() - start_time
    
    assert len(candidates) >= 4, "生成的候选数量不足"
    assert generation_time < 60, "候选生成时间过长"
```

## 安全考虑

### 1. 提示词注入防护

**输入验证：**
```python
def validate_prompt_safety(prompt: str) -> bool:
    """验证提示词安全性"""
    dangerous_patterns = [
        r"ignore\s+previous\s+instructions",
        r"system\s*:\s*you\s+are\s+now",
        r"<\s*script\s*>",
        r"eval\s*\(",
        r"exec\s*\("
    ]
    
    for pattern in dangerous_patterns:
        if re.search(pattern, prompt, re.IGNORECASE):
            return False
    
    return True
```

### 2. 数据访问控制

**权限验证：**
```python
def check_data_access_permissions(agent_id: str, data_type: str) -> bool:
    """检查数据访问权限"""
    permissions = {
        "BOA": ["market_data", "analyst_outputs"],  # 展望层权限
        "NAA": ["market_data"],  # 分析层权限
        "TRA": ["market_data", "analyst_outputs", "outlook_outputs"]  # 决策层权限
    }
    
    allowed_data = permissions.get(agent_id, [])
    return data_type in allowed_data
```

### 3. 优化结果验证

**结果完整性检查：**
```python
def validate_optimization_result(result: Dict[str, Any]) -> bool:
    """验证优化结果完整性"""
    required_fields = ["success", "agent_id", "optimized_prompt"]
    
    if not all(field in result for field in required_fields):
        return False
    
    if result["success"] and not result.get("optimized_prompt"):
        return False
    
    return True
```

## 性能优化

### 1. 缓存策略

**历史数据缓存：**
```python
class CachedHistoricalScoreManager:
    def __init__(self):
        self._cache = {}
        self._cache_ttl = 300  # 5分钟缓存
        self._cache_timestamps = {}
    
    def get_agent_optimization_history(self, agent_id: str):
        """带缓存的历史数据获取"""
        cache_key = f"history_{agent_id}"
        current_time = time.time()
        
        if (cache_key in self._cache and 
            current_time - self._cache_timestamps.get(cache_key, 0) < self._cache_ttl):
            return self._cache[cache_key]
        
        # 从数据库获取数据
        data = self._fetch_from_database(agent_id)
        self._cache[cache_key] = data
        self._cache_timestamps[cache_key] = current_time
        
        return data
```

### 2. 并发优化

**并行候选评估：**
```python
def evaluate_candidates_parallel(candidates: List[str]) -> List[Dict]:
    """并行评估候选提示词"""
    with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
        futures = [
            executor.submit(evaluate_single_candidate, candidate)
            for candidate in candidates
        ]
        
        results = []
        for future in concurrent.futures.as_completed(futures):
            try:
                result = future.result(timeout=30)
                results.append(result)
            except Exception as e:
                logger.error(f"候选评估失败: {e}")
                continue
        
        return results
```

### 3. 内存优化

**大数据处理优化：**
```python
def process_large_failure_dataset(agent_id: str) -> Iterator[Dict]:
    """流式处理大型失败案例数据集"""
    batch_size = 1000
    offset = 0
    
    while True:
        batch = fetch_failure_cases_batch(agent_id, offset, batch_size)
        if not batch:
            break
            
        for case in batch:
            yield process_failure_case(case)
        
        offset += batch_size
```

## 监控和日志

### 1. 详细日志策略

**分层日志记录：**
```python
class OPROLogger:
    def __init__(self, agent_id: str):
        self.agent_id = agent_id
        self.logger = logging.getLogger(f"opro.{agent_id}")
    
    def log_optimization_start(self, context: Dict):
        """记录优化开始"""
        self.logger.info(f"🎯 开始优化智能体 {self.agent_id}")
        self.logger.debug(f"优化上下文: {context}")
    
    def log_data_availability(self, data_status: Dict):
        """记录数据可用性"""
        for data_type, available in data_status.items():
            status = "✅" if available else "❌"
            self.logger.info(f"{status} {data_type}: {'可用' if available else '不可用'}")
    
    def log_candidate_generation(self, stats: Dict):
        """记录候选生成统计"""
        self.logger.info(f"📊 候选生成统计:")
        self.logger.info(f"  目标数量: {stats['target_count']}")
        self.logger.info(f"  成功生成: {stats['successful_count']}")
        self.logger.info(f"  成功率: {stats['success_rate']:.1f}%")
```

### 2. 性能监控

**关键指标监控：**
```python
class OPROMetrics:
    def __init__(self):
        self.metrics = defaultdict(list)
    
    def record_optimization_time(self, agent_id: str, duration: float):
        """记录优化耗时"""
        self.metrics[f"{agent_id}_optimization_time"].append(duration)
    
    def record_candidate_success_rate(self, agent_id: str, rate: float):
        """记录候选生成成功率"""
        self.metrics[f"{agent_id}_candidate_success_rate"].append(rate)
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        summary = {}
        for metric_name, values in self.metrics.items():
            summary[metric_name] = {
                "average": np.mean(values),
                "min": min(values),
                "max": max(values),
                "count": len(values)
            }
        return summary
```

### 3. 错误追踪

**错误分类和追踪：**
```python
class OPROErrorTracker:
    def __init__(self):
        self.error_counts = defaultdict(int)
        self.error_details = defaultdict(list)
    
    def track_error(self, error_type: str, agent_id: str, details: Dict):
        """追踪错误"""
        error_key = f"{agent_id}_{error_type}"
        self.error_counts[error_key] += 1
        self.error_details[error_key].append({
            "timestamp": datetime.now().isoformat(),
            "details": details
        })
    
    def get_error_report(self) -> Dict[str, Any]:
        """生成错误报告"""
        return {
            "error_counts": dict(self.error_counts),
            "most_common_errors": sorted(
                self.error_counts.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:5]
        }
```