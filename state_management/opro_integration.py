"""
OPRO集成接口

为OPRO优化系统提供状态管理数据接口
"""

from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import logging

from .state_manager import StateManager


class OPRODataProvider:
    """
    OPRO数据提供器
    
    从状态管理系统中提取OPRO优化所需的数据
    """
    
    def __init__(self, state_manager: StateManager):
        """
        初始化OPRO数据提供器
        
        Args:
            state_manager: 状态管理器实例
        """
        self.state_manager = state_manager
        self.logger = logging.getLogger("OPRODataProvider")
    
    def get_agent_weekly_io_data(self, agent_id: str, weeks: int = 1) -> List[Dict[str, Any]]:
        """
        获取智能体的周级输入输出数据
        
        Args:
            agent_id: 智能体ID
            weeks: 获取最近几周的数据
            
        Returns:
            List[Dict]: 输入输出数据列表
        """
        training_data = self.state_manager.get_opro_training_data(agent_id, weeks)
        
        # 转换为简化的OPRO格式
        weekly_io_data = []
        for io_record in training_data.get("io_history", []):
            weekly_io_data.append({
                "timestamp": io_record.get("date"),
                "input": self._summarize_input_for_opro(io_record.get("input", {})),
                "output": self._summarize_output_for_opro(io_record.get("output", {})),
                "processing_time": io_record.get("processing_time", 0),
                "success": io_record.get("success", True),
                "agent_id": agent_id
            })
        
        self.logger.info(f"获取 {agent_id} 的周级IO数据: {len(weekly_io_data)} 条记录")
        return weekly_io_data
    
    def get_agent_performance_summary(self, agent_id: str, weeks: int = 4) -> Dict[str, Any]:
        """
        获取智能体性能摘要
        
        Args:
            agent_id: 智能体ID
            weeks: 统计周数
            
        Returns:
            Dict: 性能摘要
        """
        training_data = self.state_manager.get_opro_training_data(agent_id, weeks)
        
        io_history = training_data.get("io_history", [])
        failure_cases = training_data.get("failure_cases", [])
        success_cases = training_data.get("success_cases", [])
        
        total_executions = len(io_history)
        successful_executions = len(success_cases)
        failed_executions = len(failure_cases)
        
        # 计算平均处理时间
        processing_times = [record.get("processing_time", 0) for record in io_history]
        avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0
        
        # 分析失败模式
        failure_patterns = self._analyze_failure_patterns(failure_cases)
        
        performance_summary = {
            "agent_id": agent_id,
            "evaluation_period": f"{weeks} weeks",
            "total_executions": total_executions,
            "successful_executions": successful_executions,
            "failed_executions": failed_executions,
            "success_rate": successful_executions / total_executions if total_executions > 0 else 0,
            "average_processing_time": avg_processing_time,
            "failure_patterns": failure_patterns,
            "performance_trend": self._calculate_performance_trend(io_history)
        }
        
        self.logger.info(f"生成 {agent_id} 性能摘要: 成功率 {performance_summary['success_rate']:.2%}")
        return performance_summary
    
    def get_failure_cases_for_reflection(self, agent_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取用于反思的失败案例
        
        Args:
            agent_id: 智能体ID
            limit: 最大案例数量
            
        Returns:
            List[Dict]: 失败案例列表
        """
        training_data = self.state_manager.get_opro_training_data(agent_id, weeks=4)
        failure_cases = training_data.get("failure_cases", [])
        
        # 选择最具代表性的失败案例
        selected_cases = []
        for case in failure_cases[-limit:]:  # 取最近的失败案例
            exec_record = case.get("execution_record", {})
            
            reflection_case = {
                "date": case.get("date"),
                "agent_id": agent_id,
                "input_summary": self._summarize_input_for_opro(exec_record.get("input_data", {})),
                "expected_output": "successful_analysis",
                "actual_output": exec_record.get("output_data", {}),
                "error_message": exec_record.get("error_message", ""),
                "processing_time": exec_record.get("processing_time", 0),
                "context": self._extract_failure_context(exec_record)
            }
            
            selected_cases.append(reflection_case)
        
        self.logger.info(f"获取 {agent_id} 的失败案例: {len(selected_cases)} 个")
        return selected_cases
    
    def get_success_patterns(self, agent_id: str, limit: int = 5) -> List[Dict[str, Any]]:
        """
        获取成功模式案例
        
        Args:
            agent_id: 智能体ID
            limit: 最大案例数量
            
        Returns:
            List[Dict]: 成功案例列表
        """
        training_data = self.state_manager.get_opro_training_data(agent_id, weeks=4)
        success_cases = training_data.get("success_cases", [])
        
        # 选择处理时间较短且输出质量较高的成功案例
        sorted_cases = sorted(
            success_cases,
            key=lambda x: x.get("execution_record", {}).get("processing_time", float('inf'))
        )
        
        selected_cases = []
        for case in sorted_cases[:limit]:
            exec_record = case.get("execution_record", {})
            
            success_case = {
                "date": case.get("date"),
                "agent_id": agent_id,
                "input_summary": self._summarize_input_for_opro(exec_record.get("input_data", {})),
                "output_summary": self._summarize_output_for_opro(exec_record.get("output_data", {})),
                "processing_time": exec_record.get("processing_time", 0),
                "success_factors": self._identify_success_factors(exec_record)
            }
            
            selected_cases.append(success_case)
        
        self.logger.info(f"获取 {agent_id} 的成功案例: {len(selected_cases)} 个")
        return selected_cases
    
    def _summarize_input_for_opro(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """为OPRO优化总结输入数据"""
        summary = {}
        
        # 基础信息
        for key in ["current_date", "cash", "positions"]:
            if key in input_data:
                summary[key] = input_data[key]
        
        # 数据摘要
        if "news_summary" in input_data:
            summary["news_data"] = input_data["news_summary"]
        elif "news_history" in input_data:
            summary["news_data"] = {"has_news": True, "type": "full_history"}
        
        if "price_summary" in input_data:
            summary["price_data"] = input_data["price_summary"]
        elif "price_history" in input_data:
            summary["price_data"] = {"has_prices": True, "type": "full_history"}
        
        if "fundamental_summary" in input_data:
            summary["fundamental_data"] = input_data["fundamental_summary"]
        elif "fundamental_data" in input_data:
            summary["fundamental_data"] = {"has_fundamentals": True, "type": "full_data"}
        
        # 前序智能体输出
        if "analyst_outputs" in input_data:
            summary["analyst_inputs"] = list(input_data["analyst_outputs"].keys())
        
        if "outlook_outputs" in input_data:
            summary["outlook_inputs"] = list(input_data["outlook_outputs"].keys())
        
        return summary
    
    def _summarize_output_for_opro(self, output_data: Dict[str, Any]) -> Dict[str, Any]:
        """为OPRO优化总结输出数据"""
        summary = {}
        
        # 提取关键输出字段
        key_fields = [
            "sentiment_score", "confidence", "analysis", "action", 
            "position_size", "trading_actions", "outlook", "recommendation"
        ]
        
        for field in key_fields:
            if field in output_data:
                if field == "analysis" and isinstance(output_data[field], str):
                    # 分析内容只保留前100字符
                    summary[field] = output_data[field][:100] + "..." if len(output_data[field]) > 100 else output_data[field]
                else:
                    summary[field] = output_data[field]
        
        # 添加输出统计
        summary["output_fields_count"] = len(output_data)
        summary["has_analysis"] = "analysis" in output_data
        
        return summary
    
    def _analyze_failure_patterns(self, failure_cases: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析失败模式"""
        if not failure_cases:
            return {"total_failures": 0}
        
        error_types = {}
        failure_dates = []
        
        for case in failure_cases:
            exec_record = case.get("execution_record", {})
            error_msg = exec_record.get("error_message", "unknown_error")
            
            # 分类错误类型
            error_type = self._classify_error_type(error_msg)
            error_types[error_type] = error_types.get(error_type, 0) + 1
            
            failure_dates.append(case.get("date"))
        
        return {
            "total_failures": len(failure_cases),
            "error_types": error_types,
            "most_common_error": max(error_types.items(), key=lambda x: x[1])[0] if error_types else "none",
            "failure_dates": failure_dates[-5:]  # 最近5次失败日期
        }
    
    def _classify_error_type(self, error_message: str) -> str:
        """分类错误类型"""
        error_msg_lower = error_message.lower()
        
        if "timeout" in error_msg_lower or "time" in error_msg_lower:
            return "timeout_error"
        elif "json" in error_msg_lower or "parse" in error_msg_lower:
            return "parsing_error"
        elif "llm" in error_msg_lower or "api" in error_msg_lower:
            return "llm_api_error"
        elif "data" in error_msg_lower or "missing" in error_msg_lower:
            return "data_error"
        else:
            return "unknown_error"
    
    def _calculate_performance_trend(self, io_history: List[Dict[str, Any]]) -> str:
        """计算性能趋势"""
        if len(io_history) < 2:
            return "insufficient_data"
        
        # 按日期排序
        sorted_history = sorted(io_history, key=lambda x: x.get("date", ""))
        
        # 计算最近一半和前一半的成功率
        mid_point = len(sorted_history) // 2
        first_half = sorted_history[:mid_point]
        second_half = sorted_history[mid_point:]
        
        first_half_success_rate = sum(1 for record in first_half if record.get("success", False)) / len(first_half)
        second_half_success_rate = sum(1 for record in second_half if record.get("success", False)) / len(second_half)
        
        if second_half_success_rate > first_half_success_rate + 0.1:
            return "improving"
        elif second_half_success_rate < first_half_success_rate - 0.1:
            return "declining"
        else:
            return "stable"
    
    def _extract_failure_context(self, exec_record: Dict[str, Any]) -> Dict[str, Any]:
        """提取失败上下文"""
        return {
            "processing_time": exec_record.get("processing_time", 0),
            "input_complexity": len(exec_record.get("input_data", {})),
            "execution_order": exec_record.get("execution_order", 0)
        }
    
    def _identify_success_factors(self, exec_record: Dict[str, Any]) -> List[str]:
        """识别成功因素"""
        factors = []
        
        processing_time = exec_record.get("processing_time", 0)
        if processing_time < 1.0:
            factors.append("fast_processing")
        
        output_data = exec_record.get("output_data", {})
        if len(output_data) > 5:
            factors.append("rich_output")
        
        if "confidence" in output_data and isinstance(output_data["confidence"], (int, float)):
            if output_data["confidence"] > 0.8:
                factors.append("high_confidence")
        
        return factors
