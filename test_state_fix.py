#!/usr/bin/env python3
"""
测试状态继承修复方案
验证 get_clean_state_for_new_phase 方法是否正确处理周内阶段转换
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from portfolio_state_tracker import PortfolioStateTracker


def test_intra_week_phase_transition():
    """测试周内阶段转换的状态清理"""
    print("=== 测试周内阶段转换状态清理 ===")
    
    # 创建跟踪器
    tracker = PortfolioStateTracker()
    
    # 模拟第1周完整联盟运行了3天
    tracker.start_new_week(1)
    tracker.add_daily_record("2024-01-01", 10500, 0.01, 0.01, 0.01, 0.0)  # 第1天
    tracker.add_daily_record("2024-01-02", 10800, 0.03, 0.04, 0.04, 0.0)  # 第2天
    tracker.add_daily_record("2024-01-03", 11000, 0.02, 0.06, 0.06, 0.0)  # 第3天 (完整联盟结束)
    
    print(f"完整联盟结束后的状态:")
    latest_state = tracker.get_latest_state()
    print(f"  累计收益率: {latest_state['cumulative_return']:.4f}")
    print(f"  当前周收益率: {latest_state['weekly_return']:.4f}")
    print(f"  上周收益率: {latest_state['last_week_return']:.4f}")
    
    # 现在子集联盟开始，应该获取干净状态
    print(f"\n子集联盟开始 (同一周内新阶段):")
    clean_state = tracker.get_clean_state_for_new_phase(1)  # 仍然是第1周
    
    if clean_state:
        print(f"  累计收益率: {clean_state['cumulative_return']:.4f} (应保持)")
        print(f"  当前周收益率: {clean_state['weekly_return']:.4f} (应重置为0)")
        print(f"  上周收益率: {clean_state['last_week_return']:.4f}")
        
        # 验证结果
        assert clean_state['cumulative_return'] == 0.06, f"累计收益率应为0.06，实际为{clean_state['cumulative_return']}"
        assert clean_state['weekly_return'] == 0.0, f"当前周收益率应重置为0，实际为{clean_state['weekly_return']}"
        print("  ✅ 周内阶段转换测试通过!")
    else:
        print("  ❌ 无法获取干净状态")
        return False
    
    return True


def test_cross_week_transition():
    """测试跨周转换的状态清理"""
    print("\n=== 测试跨周转换状态清理 ===")
    
    # 创建跟踪器
    tracker = PortfolioStateTracker()
    
    # 模拟第1周运行完毕
    tracker.start_new_week(1)
    tracker.add_daily_record("2024-01-01", 10500, 0.01, 0.01, 0.01, 0.0)
    tracker.add_daily_record("2024-01-02", 10800, 0.03, 0.04, 0.04, 0.0)
    tracker.add_daily_record("2024-01-03", 11000, 0.02, 0.06, 0.06, 0.0)
    tracker.add_daily_record("2024-01-04", 11100, 0.01, 0.07, 0.07, 0.0)
    tracker.add_daily_record("2024-01-05", 11200, 0.01, 0.08, 0.08, 0.0)  # 第1周结束
    
    print(f"第1周结束后的状态:")
    latest_state = tracker.get_latest_state()
    print(f"  累计收益率: {latest_state['cumulative_return']:.4f}")
    print(f"  当前周收益率: {latest_state['weekly_return']:.4f}")
    print(f"  上周收益率: {latest_state['last_week_return']:.4f}")
    
    # 现在开始第2周
    print(f"\n第2周开始 (跨周转换):")
    clean_state = tracker.get_clean_state_for_new_phase(2)  # 开始第2周
    
    if clean_state:
        print(f"  累计收益率: {clean_state['cumulative_return']:.4f} (应保持)")
        print(f"  当前周收益率: {clean_state['weekly_return']:.4f} (应重置为0)")
        print(f"  上周收益率: {clean_state['last_week_return']:.4f} (应为上周的周收益率)")
        
        # 验证结果
        assert clean_state['cumulative_return'] == 0.08, f"累计收益率应为0.08，实际为{clean_state['cumulative_return']}"
        assert clean_state['weekly_return'] == 0.0, f"当前周收益率应重置为0，实际为{clean_state['weekly_return']}"
        assert clean_state['last_week_return'] == 0.08, f"上周收益率应为0.08（第1周的周收益率），实际为{clean_state['last_week_return']}"
        print("  ✅ 跨周转换测试通过!")
    else:
        print("  ❌ 无法获取干净状态")
        return False
    
    return True


def test_correct_last_week_return():
    """测试上周收益率的正确性（修复第二周获取错误收益率的问题）"""
    print("\n=== 测试上周收益率正确性 ===")
    
    # 创建跟踪器
    tracker = PortfolioStateTracker()
    
    # 模拟第1周：周收益率从0增长到5%
    tracker.start_new_week(1)
    tracker.add_daily_record("2024-01-01", 10100, 0.01, 0.01, 0.01, 0.0)  # 周收益率1%
    tracker.add_daily_record("2024-01-02", 10300, 0.02, 0.03, 0.03, 0.0)  # 周收益率3%
    tracker.add_daily_record("2024-01-03", 10500, 0.02, 0.05, 0.05, 0.0)  # 第1周结束，周收益率5%
    
    print(f"第1周结束状态:")
    week1_state = tracker.get_latest_state()
    print(f"  累计收益率: {week1_state['cumulative_return']:.4f}")
    print(f"  第1周周收益率: {week1_state['weekly_return']:.4f}")
    print(f"  上周收益率: {week1_state['last_week_return']:.4f}")
    
    # 测试get_week_end_return_state方法
    week1_end_state = tracker.get_week_end_return_state(1)
    print(f"\nget_week_end_return_state(1)返回:")
    print(f"  累计收益率: {week1_end_state['cumulative_return']:.4f}")
    print(f"  周收益率: {week1_end_state['weekly_return']:.4f}")
    print(f"  last_week_return: {week1_end_state['last_week_return']:.4f}")
    
    # 验证week_end_return_state的正确性
    assert week1_end_state['weekly_return'] == 0.05, f"第1周周收益率应为0.05，实际为{week1_end_state['weekly_return']}"
    assert week1_end_state['last_week_return'] == 0.05, f"get_week_end_return_state应返回周收益率0.05，实际为{week1_end_state['last_week_return']}"
    
    # 开始第2周，测试get_clean_state_for_new_phase
    print(f"\n第2周开始:")
    clean_state_week2 = tracker.get_clean_state_for_new_phase(2)
    
    if clean_state_week2:
        print(f"  累计收益率: {clean_state_week2['cumulative_return']:.4f} (应保持0.05)")
        print(f"  当前周收益率: {clean_state_week2['weekly_return']:.4f} (应重置为0)")
        print(f"  上周收益率: {clean_state_week2['last_week_return']:.4f} (应为第1周的周收益率0.05)")
        
        # 验证结果
        assert clean_state_week2['cumulative_return'] == 0.05, f"累计收益率应为0.05，实际为{clean_state_week2['cumulative_return']}"
        assert clean_state_week2['weekly_return'] == 0.0, f"当前周收益率应重置为0，实际为{clean_state_week2['weekly_return']}"
        assert clean_state_week2['last_week_return'] == 0.05, f"上周收益率应为0.05（第1周的周收益率），实际为{clean_state_week2['last_week_return']}"
        print("  ✅ 上周收益率正确性测试通过!")
        return True
    else:
        print("  ❌ 无法获取第2周干净状态")
        return False


def test_complete_coalition_filtering():
    """测试完整联盟过滤功能（只保存完整联盟的记录）"""
    print("\n=== 测试完整联盟过滤功能 ===")
    
    # 创建跟踪器
    tracker = PortfolioStateTracker()
    tracker.start_new_week(1)
    
    # 模拟完整联盟记录（7个智能体）
    complete_coalition_info = {
        "coalition_id": "frozenset({'TRA', 'NOA', 'NAA', 'BeOA', 'FAA', 'BOA', 'TAA'})",
        "coalition_size": 7
    }
    
    # 模拟子集联盟记录（5个智能体）
    subset_coalition_info = {
        "coalition_id": "frozenset({'TRA', 'NAA', 'BeOA', 'BOA', 'TAA'})",
        "coalition_size": 5
    }
    
    print("添加完整联盟记录...")
    tracker.add_daily_record(
        "2024-01-01", 10500, 0.01, 0.01, 0.01, 0.0, 
        coalition_info=complete_coalition_info
    )
    
    print("尝试添加子集联盟记录（应被跳过）...")
    tracker.add_daily_record(
        "2024-01-01", 10300, -0.01, -0.01, -0.01, 0.0,
        coalition_info=subset_coalition_info
    )
    
    print("再次添加完整联盟记录...")
    tracker.add_daily_record(
        "2024-01-02", 10800, 0.03, 0.04, 0.04, 0.0,
        coalition_info=complete_coalition_info
    )
    
    # 检查结果
    all_records = tracker.get_all_records()
    print(f"\n保存的记录数量: {len(all_records)}")
    
    # 验证只保存了完整联盟的记录
    if len(all_records) == 2:  # 应该只有2条记录（完整联盟的）
        latest_state = tracker.get_latest_state()
        print(f"最新状态累计收益率: {latest_state['cumulative_return']:.4f}")
        
        # 验证最新状态是完整联盟的结果，不是子集联盟的
        if latest_state['cumulative_return'] == 0.04:  # 完整联盟的累计收益率
            print("✅ 完整联盟过滤测试通过! 只保存了完整联盟的记录。")
            return True
        else:
            print(f"❌ 最新状态不正确，期望0.04，实际{latest_state['cumulative_return']:.4f}")
            return False
    else:
        print(f"❌ 记录数量不正确，期望2条，实际{len(all_records)}条")
        return False


def main():
    """运行所有测试"""
    print("开始测试状态继承修复方案...\n")
    
    test1_passed = test_intra_week_phase_transition()
    test2_passed = test_cross_week_transition()
    test3_passed = test_correct_last_week_return()
    test4_passed = test_complete_coalition_filtering()
    
    print(f"\n=== 测试总结 ===")
    print(f"周内阶段转换: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"跨周转换: {'✅ 通过' if test2_passed else '❌ 失败'}")
    print(f"上周收益率正确性: {'✅ 通过' if test3_passed else '❌ 失败'}")
    print(f"完整联盟过滤: {'✅ 通过' if test4_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed and test3_passed and test4_passed:
        print("\n🎉 所有测试通过! 状态继承修复方案工作正常。")
        return True
    else:
        print("\n⚠️ 部分测试失败，需要检查实现。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)