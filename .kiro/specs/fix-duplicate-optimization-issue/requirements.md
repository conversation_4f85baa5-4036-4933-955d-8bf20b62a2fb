# 需求文档

## 介绍

当前系统在 phase3 识别出 Shapley 值最差的 agent 后，会触发两次提示词优化：一次是正常的 phase4 OPRO 优化，另一次是周期性提示词优化。这种重复优化是不必要的，会浪费计算资源并可能导致优化效果冲突。需要修复这个重复优化问题，确保只执行必要的 phase4 优化。

## 需求

### 需求 1

**用户故事：** 作为系统管理员，我希望系统在 phase3 后只执行一次提示词优化，以避免资源浪费和优化冲突。

#### 验收标准

1. 当 phase3 识别出最差 agent 时，系统应该只执行 phase4 的 OPRO 优化
2. 当 phase3 识别出最差 agent 时，系统不应该执行周期性提示词优化
3. 如果没有通过 phase3 识别最差 agent，周期性优化可以正常执行
4. 系统应该记录优化决策的日志，明确说明为什么选择或跳过某种优化
5. 系统应该确保能够在修复后和现在一样运行

### 需求 2

**用户故事：** 作为开发者，我希望能够清楚地理解系统的优化逻辑流程，以便于维护和调试。

#### 验收标准

1. 当系统决定执行 phase4 优化时，应该记录决策原因
2. 当系统跳过周期性优化时，应该记录跳过原因
3. 优化流程应该有清晰的条件判断逻辑
4. 代码中应该有明确的注释说明优化策略的选择逻辑

### 需求 3

**用户故事：** 作为系统用户，我希望优化过程是高效的，不会因为重复优化而延长执行时间。

#### 验收标准

1. 当存在 phase4 优化时，周期性优化应该被自动跳过
2. 系统应该在开始优化前检查是否已有其他优化在进行
3. 优化过程应该有互斥机制，防止同时进行多种优化
4. 系统应该能够检测并报告重复优化的情况

### 需求 4

**用户故事：** 作为质量保证工程师，我希望能够验证修复后的系统不会出现重复优化问题。

#### 验收标准

1. 当 phase3 触发时，系统应该只执行一种优化策略
2. 系统应该提供测试接口来验证优化逻辑
3. 修复后的系统应该通过所有现有的测试用例
4. 应该有新的测试用例来验证重复优化问题已被解决