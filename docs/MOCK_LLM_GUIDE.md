# 虚拟LLM接口使用指南

## 概述

虚拟LLM接口（Mock LLM Interface）是为了方便快速测试和开发而设计的模拟LLM提供商。它无需真实的API密钥，返回预定义的合理响应，非常适合以下场景：

- 🚀 **快速测试**: 无需配置API密钥即可测试系统功能
- 🔧 **开发调试**: 在开发过程中快速验证代码逻辑
- 📚 **演示展示**: 在演示或教学中展示系统功能
- 🧪 **单元测试**: 为自动化测试提供稳定的响应

## 特性

### ✅ 支持的功能
- 模拟所有智能体类型的响应（NAA, TAA, FAA, BOA, BeOA, NOA, TRA）
- 智能识别提示内容并返回相应的响应类型
- 模拟API调用延迟（0.1-0.5秒）
- 添加随机性以模拟真实LLM的变化
- 完全兼容现有的LLMInterface接口

### 🎭 智能体响应类型

| 智能体 | 响应内容 | 示例字段 |
|--------|----------|----------|
| NAA (新闻分析师) | 情绪分析、关键因素 | sentiment, confidence, key_factors |
| TAA (技术分析师) | 技术指标、趋势分析 | trend, support_level, indicators |
| FAA (基本面分析师) | 估值分析、财务健康 | valuation, pe_ratio, financial_health |
| BOA (看涨观点) | 积极因素、增长驱动 | outlook, key_drivers, time_horizon |
| BeOA (看跌观点) | 风险因素、下跌预期 | risk_factors, bearish_reasoning |
| NOA (中性观察者) | 平衡观点、综合分析 | balanced_view, neutral_assessment |
| TRA (交易员) | 交易决策、风险管理 | action, position_size, stop_loss |

## 使用方法

### 1. 基本使用

```bash
# 使用虚拟LLM运行快速测试
python run_opro_system.py --provider mock --mode evaluation --quick-test

# 使用虚拟LLM运行完整评估
python run_opro_system.py --provider mock --mode evaluation

# 检查虚拟LLM环境
python check_llm_setup.py --provider mock
```

### 2. 代码中使用

```python
from contribution_assessment.llm_interface import LLMInterface

# 创建虚拟LLM接口
llm = LLMInterface(provider="mock")

# 进行分析调用
result = llm.analyze("分析当前市场趋势", model="mock-model")
print(result)
```

### 3. 测试套件

```bash
# 运行虚拟LLM测试套件
python test_mock_llm.py
```

## 响应示例

### 技术分析师响应
```json
{
  "trend": "upward",
  "support_level": 150.0,
  "resistance_level": 180.0,
  "indicators": {
    "rsi": 65.0,
    "macd": "bullish",
    "moving_average": "above"
  },
  "recommendation": "买入",
  "reasoning": "技术指标显示上升趋势",
  "confidence": 0.75,
  "timestamp": **********.0,
  "mock_response": true
}
```

### 新闻分析师响应
```json
{
  "sentiment": "neutral",
  "confidence": 0.7,
  "key_factors": ["市场波动", "政策影响", "行业趋势"],
  "recommendation": "持有",
  "reasoning": "基于当前新闻分析，市场情绪相对稳定",
  "timestamp": **********.0,
  "mock_response": true
}
```

## 配置选项

虚拟LLM接口支持以下配置：

### 环境变量
- 无需任何环境变量配置
- 自动跳过API密钥检查

### 模型参数
- 所有模型参数（temperature, top_p等）都会被接受但忽略
- 模型名称可以是任意值，推荐使用 "mock-model"

## 与真实LLM的对比

| 特性 | 虚拟LLM | 真实LLM |
|------|---------|---------|
| API密钥 | ❌ 不需要 | ✅ 必需 |
| 网络连接 | ❌ 不需要 | ✅ 必需 |
| 响应时间 | 🚀 0.1-0.5秒 | ⏱️ 1-10秒 |
| 响应质量 | 📋 预定义 | 🧠 智能生成 |
| 成本 | 💰 免费 | 💳 按使用付费 |
| 稳定性 | ✅ 100%可用 | ⚠️ 依赖服务 |

## 最佳实践

### 开发阶段
1. 使用虚拟LLM进行初期开发和调试
2. 验证系统架构和数据流
3. 测试错误处理逻辑

### 测试阶段
1. 编写单元测试时使用虚拟LLM
2. 集成测试中混合使用虚拟和真实LLM
3. 性能测试时使用虚拟LLM排除网络因素

### 生产部署
1. 生产环境必须使用真实LLM
2. 保留虚拟LLM作为降级方案
3. 监控和日志中标识响应来源

## 注意事项

### ⚠️ 限制
- 响应内容是预定义的，不会根据实际输入生成
- 无法处理复杂的上下文理解
- 不适合生产环境的实际决策

### 🔧 扩展
- 可以通过修改 `MockLLMClient` 类添加更多响应类型
- 支持自定义响应模板和关键词映射
- 可以集成到CI/CD流程中进行自动化测试

## 故障排除

### 常见问题

**Q: 虚拟LLM返回的响应格式不正确？**
A: 检查 `MockLLMClient` 中的响应模板，确保格式符合预期。

**Q: 无法识别特定的智能体类型？**
A: 在 `_identify_agent_type` 方法中添加相应的关键词映射。

**Q: 需要添加新的响应类型？**
A: 修改 `agent_responses` 字典，添加新的智能体响应模板。

### 调试技巧
1. 启用详细日志：`--verbose`
2. 检查响应中的 `mock_response: true` 标识
3. 使用测试套件验证功能：`python test_mock_llm.py`

## 更新日志

### v1.0.0 (2025-01-17)
- ✨ 初始版本发布
- 🎭 支持7种智能体类型的模拟响应
- 🔧 完整的测试套件
- 📚 详细的使用文档

---

💡 **提示**: 虚拟LLM接口是开发和测试的强大工具，但请记住在生产环境中使用真实的LLM提供商以获得最佳效果。
