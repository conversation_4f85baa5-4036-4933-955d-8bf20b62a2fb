# 🔄 周期性在线优化系统使用指南

## 🎯 概述

周期性在线优化系统实现了真正的"在线学习"，在模拟过程中每周自动检测和优化智能体性能，而不是等待完整模拟结束后再进行批量优化。

## 🚀 快速开始

### 基础用法

```bash
# 启用周期性优化（推荐！）
python run_opro_system.py --provider zhipuai --mode weekly

# 或者使用 --weekly-optimization 参数
python run_opro_system.py --provider zhipuai --weekly-optimization
```

### 自定义参数

```bash
# 自定义优化频率为每5天
python run_opro_system.py --provider zhipuai --mode weekly --optimization-frequency 5

# 设置每周期最多优化3个智能体
python run_opro_system.py --provider zhipuai --mode weekly --max-agents-per-cycle 3

# 设置目标改进阈值
python run_opro_system.py --provider zhipuai --mode weekly --target-improvement-threshold 0.02

# 组合使用多个参数
python run_opro_system.py --provider zhipuai --mode weekly \
    --optimization-frequency 5 \
    --max-agents-per-cycle 3 \
    --min-days-for-optimization 3 \
    --target-improvement-threshold 0.015
```

## 📋 命令行参数说明

### 周期性优化参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--weekly-optimization` | False | 启用周期性优化 |
| `--mode weekly` | - | 直接运行周期性优化模式 |
| `--optimization-frequency` | 7 | 优化频率（天），建议5-10天 |
| `--min-days-for-optimization` | 5 | 最少运行天数才开始优化 |
| `--max-agents-per-cycle` | 2 | 每个周期最多优化的智能体数量 |
| `--target-improvement-threshold` | 0.01 | 目标改进阈值（1%） |

### 基础参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--provider` | zhipuai | LLM提供商（zhipuai/openai） |
| `--symbol` | AAPL | 股票代码 |
| `--start-date` | 2025-01-01 | 开始日期 |
| `--end-date` | 2025-04-30 | 结束日期 |
| `--verbose` | False | 详细日志输出 |

## 🔄 系统工作流程

### 1. 初始化阶段
- 加载智能体和市场数据
- 初始化周期性优化管理器
- 设置优化配置参数

### 2. 模拟运行阶段
- 每天执行交易决策
- 实时监控智能体性能
- 检测周期边界（默认每7天）

### 3. 周期性优化阶段（每周触发）
- **性能评估**: 计算当周Shapley值
- **目标识别**: 选择表现最差的智能体
- **提示词优化**: 使用OPRO算法优化
- **实时应用**: 立即更新智能体配置

### 4. 持续学习
- 智能体在剩余模拟中使用优化后的提示词
- 循环执行优化过程直到模拟结束

## 📊 优化策略

### 智能体选择策略
- **优先最差**: 优先优化Shapley值最低的智能体
- **限制数量**: 每周期最多优化2个智能体（可调）
- **性能阈值**: 只优化低于阈值的智能体

### 优化频率建议
- **积极优化**: 每5天（--optimization-frequency 5）
- **标准优化**: 每7天（默认）
- **保守优化**: 每10天（--optimization-frequency 10）

## 🎯 使用建议

### 快速验证
```bash
# 快速测试（短时间运行）
python run_opro_system.py --provider zhipuai --mode weekly \
    --start-date 2025-01-01 --end-date 2025-01-20 --verbose
```

### 完整评估
```bash
# 长期评估（3个月）
python run_opro_system.py --provider zhipuai --mode weekly \
    --start-date 2025-01-01 --end-date 2025-04-30 \
    --optimization-frequency 7 --verbose
```

### 调优实验
```bash
# 对比不同优化频率
python run_opro_system.py --provider zhipuai --mode weekly --optimization-frequency 5
python run_opro_system.py --provider zhipuai --mode weekly --optimization-frequency 10
```

## 📈 结果解读

### 日志输出示例
```
🔄 运行模式: 周期性在线优化 (每周优化)
📋 周期性优化配置:
  优化频率: 每 7 天
  最少运行天数: 5 天
  每周期最多优化: 2 个智能体
  目标改进阈值: 0.01

🚀 开始第 2 周优化 (2025-01-16)
📊 阶段1：评估当周智能体性能...
🎯 阶段2：选择优化目标...
选择优化目标: NAA, FAA
🔧 阶段3：执行智能体优化...
  优化智能体 NAA (当前得分: -0.123456)
    ✅ 优化成功，预期改进: 0.025000
🔄 阶段4：应用优化结果...
    🔄 智能体 NAA 提示词已更新
✅ 第 2 周优化完成，优化了 1 个智能体
```

### 性能监控
- **优化成功率**: 查看多少智能体被成功优化
- **改进幅度**: 查看预期和实际改进效果
- **系统稳定性**: 监控优化过程中的错误

## 🛠️ 故障排除

### 常见问题

**1. 导入错误**
```
ImportError: 周期性优化模块未找到
```
**解决**: 确保所有文件都已正确添加到项目中

**2. 配置错误**
```
无法将"WeeklyOptimizationConfig"类型的参数分配
```
**解决**: 使用 `weekly_config.to_dict()` 传递配置

**3. 优化失败**
```
❌ 第 2 周优化失败: OPRO功能未启用
```
**解决**: 确保启用了 `--enable-opro` (默认启用)

### 调试模式
```bash
# 开启详细日志
python run_opro_system.py --provider zhipuai --mode weekly --verbose

# 输出到日志文件
python run_opro_system.py --provider zhipuai --mode weekly --log-file weekly_debug.log
```

## 🔍 高级功能

### 导出结果
```bash
# 导出优化结果
python run_opro_system.py --provider zhipuai --mode weekly \
    --output weekly_results.json --export-opro-data
```

### 与其他模式对比
```bash
# 传统离线优化
python run_opro_system.py --provider zhipuai --mode optimization

# 周期性在线优化  
python run_opro_system.py --provider zhipuai --mode weekly

# 仅评估（无优化）
python run_opro_system.py --provider zhipuai --mode evaluation --disable-opro
```

## 🎉 系统优势

### 对比传统方法

| 特性 | 传统离线优化 | 周期性在线优化 |
|------|-------------|---------------|
| **优化时机** | 模拟结束后 | 模拟过程中 |
| **学习效果** | 延迟反馈 | 实时反馈 |
| **适应性** | 静态策略 | 动态进化 |
| **数据利用** | 批量处理 | 增量学习 |
| **系统响应** | 一次性调整 | 持续改进 |

### 核心价值
- **🔄 持续学习**: 智能体在模拟中不断进化
- **⚡ 快速响应**: 及时发现并修正策略问题
- **📈 增量改进**: 基于最新数据持续优化
- **🎯 精准定位**: 准确识别需要改进的智能体
- **💡 自适应**: 根据市场变化动态调整策略

现在您可以直接运行以下命令来体验周期性优化系统：

```bash
python run_opro_system.py --provider zhipuai --mode weekly --verbose
```

这将启动每周自动优化的系统，您可以在日志中看到智能体的实时进化过程！