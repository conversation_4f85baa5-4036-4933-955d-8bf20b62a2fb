
### **面向 Python 开发的核心代码规约**

#### **第一章：核心理念 (Core Philosophy)**

我们致力于用 Python 构建清晰、健壮且易于演进的软件。我们的代码哲学是：**通过模块和类实现高度内聚，借助抽象基类（ABC）和依赖注入实现低耦合，并利用 Python 的动态特性和设计模式遵循最小化修改原则。**


#### **第四章：需要警惕的 Python "坏味道"**

  * **滥用全局变量 (`global`):** 这是最糟糕的耦合形式，它在模块间创建了不可见的、脆弱的依赖。应通过函数参数和返回值来传递状态。
  * **过长的函数和类:** 一个函数超过一屏，或者一个类有几十个方法，通常意味着它的内聚性极低。
  * **猴子补丁 (Monkey Patching) 用于生产代码:** 在测试中 `mock` 是必要的，但在生产代码中动态地修改第三方库或标准库的行为，会使系统行为变得不可预测，是极度危险的耦合。
  * **字典作为上帝对象:** 滥用字典来传递包含各种不同类型和结构的数据。虽然灵活，但没有契约，很容易出错。考虑使用 `dataclasses` 或 `Pydantic` 模型来定义清晰的数据结构。