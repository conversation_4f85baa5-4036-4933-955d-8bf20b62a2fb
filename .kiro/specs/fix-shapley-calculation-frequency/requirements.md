# 需求文档

## 介绍

在修复重复优化问题后，系统出现了新的问题：Shapley值计算不再按照预期的频率（每5个交易日）进行，而是等到所有交易日完成后才进入阶段3进行Shapley值计算。这导致系统失去了周期性分析的能力，影响了Phase4 OPRO优化的效果，因为OPRO优化需要基于周期性的Shapley值来识别表现最差的agent。

## 需求

### 需求 1

**用户故事：** 作为系统用户，我希望系统能够按照配置的频率（每5个交易日）进行Shapley值计算，以便获得周期性的性能分析。

#### 验收标准

1. 当系统运行时，应该每5个交易日(我希望这是一个可调的参数,将来我可能会尝试不同的交易日长度)计算一次Shapley值
2. 当达到5个交易日时，系统应该进入阶段3进行Shapley值计算
3. 当Shapley值计算完成后，系统应该继续下一个5天周期的模拟
4. 系统应该在日志中清楚显示当前是第几个交易周期

### 需求 2

**用户故事：** 作为开发者，我希望能够理解为什么移除周期性优化后影响了Shapley值计算频率。

#### 验收标准

1. 当分析代码时，应该能够识别出影响Shapley值计算频率的具体代码位置
2. 当分析代码时，应该能够区分周期性优化逻辑和周期性Shapley值计算逻辑
3. 当分析代码时，应该能够确定哪些代码被错误地移除了

### 需求 3

**用户故事：** 作为系统管理员，我希望修复后的系统能够正确地进行周期性Shapley值计算，同时不重新引入重复优化问题。

#### 验收标准

1. 当系统修复后，应该能够每5个交易日进行一次Shapley值计算
2. 当系统修复后，Phase4 OPRO优化应该能够基于最新的周期性Shapley值进行
3. 当系统修复后，不应该出现重复优化的问题
4. 当系统修复后，应该保持原有的四阶段流程结构

### 需求 4

**用户故事：** 作为质量保证工程师，我希望有测试用例来验证周期性Shapley值计算功能正常工作。

#### 验收标准

1. 当创建测试时，应该有测试用例验证每5个交易日进行一次Shapley值计算
2. 当创建测试时，应该有测试用例验证多个周期的Shapley值计算
3. 当运行测试时，所有相关的测试用例都应该通过
4. 当运行测试时，应该能够验证Phase4优化能够获得正确的周期性Shapley值数据