# 实现计划

- [x] 1. 移除 ContributionAssessor 中的周期性优化方法
  - 从 `contribution_assessment/assessor.py` 中移除 `run_with_weekly_optimization()` 方法
  - 移除 `_run_weekly_optimization_simulation()` 方法及其相关代码
  - 移除 `WeeklyOptimizationManager` 和 `WeeklyOptimizationConfig` 的导入语句
  - _需求: 1.1, 2.1_

- [x] 2. 更新运行脚本中的周期性优化模式处理
  - 修改 `run_opro_system.py` 中的 `run_weekly_optimization_mode()` 函数
  - 将周期性模式重定向到标准评估模式或返回不支持的错误信息
  - 更新命令行参数解析，移除或标记废弃周期性优化相关参数
  - _需求: 1.2, 3.1_

- [x] 3. 清理配置文件和文档中的周期性优化引用
  - 检查并移除配置文件中的周期性优化相关配置项
  - 更新代码注释，移除对周期性优化的引用
  - 清理不再使用的导入语句和变量定义
  - _需求: 2.2, 3.2_

- [x] 4. 创建测试用例验证重复优化问题已解决
  - 编写测试用例验证标准四阶段流程正常执行
  - 编写测试用例验证 Phase4 优化正常工作且只执行一次
  - 编写测试用例验证移除周期性优化后系统稳定性
  - _需求: 4.1, 4.2, 4.3_

- [x] 5. 运行回归测试确保功能完整性
  - 运行现有的所有测试用例确保没有破坏现有功能
  - 验证标准模式 (`--mode evaluation`) 正常工作
  - 验证优化模式 (`--mode optimization`) 正常工作
  - 确认不再出现重复优化的日志记录
  - _需求: 3.3, 4.4_

- [ ] 6. 更新帮助文档和错误消息
  - 更新 `run_opro_system.py` 中的帮助文档，移除周期性模式说明
  - 为尝试使用已移除功能的用户提供清晰的错误消息和迁移指导
  - 更新代码中的注释和文档字符串
  - _需求: 2.3, 3.4_